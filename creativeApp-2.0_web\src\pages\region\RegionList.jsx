import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditRegion from './EditRegion';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const RegionList = () => {
    const [regions, setRegions] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedRegionId, setSelectedRegionId] = useState(null);
    const [error, setError] = useState(null);

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Product Type Name", key: "name" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchRegions = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}regions`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();

                setRegions(data.regions.map(region => ({
                    id: region.id,
                    department: region.department,
                    team: region.team,
                    name: region.name,
                    created_by: region.created_by,
                    updated_by: region.updated_by,
                })));
            } catch (error) {
                setError(error.message);
            }
        };

        fetchRegions();
    }, []);

    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}regions/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete region: ' + response.statusText);
            }

            setRegions(prevRegions => prevRegions.filter(region => region.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    const handleEdit = (id) => {
        setSelectedRegionId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={regions}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedRegionId}
            />
            {modalVisible && (
                <EditRegion
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    regionId={selectedRegionId}
                />
            )}
        </div>
    );
};

export default RegionList;
