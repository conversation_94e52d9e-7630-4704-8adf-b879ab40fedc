import React, { useState, useEffect } from "react";
import { API_URL } from './../../../common/fetchData/apiConfig'; 
import useFetchApiData from './../../../common/fetchData/useFetchApiData';
import { alertMessage } from "../../../common/coreui";

const EditTaskRecord = ({ isVisible, setVisible, dataItemsId }) => {
    const [ticketNumber, setTicketNumber] = useState("");
    const [month, setMonth] = useState("");
    const [week, setWeek] = useState("");
    const [receivedDate, setReceivedDate] = useState("");
    const [dueDate, setDueDate] = useState("");
    const [accountName, setAccountName] = useState("");
    const [unit, setUnit] = useState(null);
    const [campaignName, setCampaignName] = useState("");
    const [notes, setNotes] = useState("");
    const [selectedDepartment, setSelectedDepartment] = useState("");
    const [selectedDepartmentName, setSelectedDepartmentName] = useState("");
    const [selectedTeam, setSelectedTeam] = useState("");
    const [filteredTeams, setFilteredTeams] = useState([]);
    const [selectedProductType, setSelectedProductType] = useState("");
    const [selectedTaskType, setSelectedTaskType] = useState("");
    const [selectedRevisionType, setSelectedRevisionType] = useState("");
    const [selectedReporter, setSelectedReporter] = useState("");
    const [selectedRegion, setSelectedRegion] = useState("");
    const [selectedPriority, setSelectedPriority] = useState("");
    const [error, setError] = useState(null);
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [productTypes, setProductTypes] = useState([]);
    const [taskTypes, setTaskTypes] = useState([]);
    const [revisionTypes, setRevisionTypes] = useState([]);
    const [regions, setRegions] = useState([]);
    const [priorities, setPriorities] = useState([]);
    const [reporters, setReporters] = useState([]);
    const [revisionTaskTypeId, setRevisionTaskTypeId] = useState(null);
    const [loggedUsers, setLoggedUsers] = useState([]);
    const [loggedInUserId, setLoggedInUserId] = useState("");
    const [loggedInUsersDepartment, setLoggedInUsersDepartment] = useState("");
    const [loggedInUsersDepartmentId, setLoggedInUsersDepartmentId] = useState("");
    const [loggedInUsersTeamId, setLoggedInUsersTeamId] = useState("");
    const [loggedInUsersteamName, setLoggedInUsersTeam] = useState("");
    const [loggedInUsersTeams, setLoggedInUsersTeams] = useState([]);
    const [selectedTeamId, setSelectedTeamId] = useState(null);
    const [successMessage, setSuccessMessage] = useState('');
    const [filteredReporters, setFilteredReporters] = useState([]);

    const token = localStorage.getItem('token');

    // Fetching data by useFetchApiData component
    const { data: productTypeData } = useFetchApiData(`${API_URL}product-types`, token);
    const { data: taskTypeData } = useFetchApiData(`${API_URL}task-types`, token);
    const { data: revisionTypeData } = useFetchApiData(`${API_URL}revision-types`, token);
    const { data: regionData } = useFetchApiData(`${API_URL}regions`, token);
    const { data: priorityData } = useFetchApiData(`${API_URL}priorities`, token);
    const { data: reporterData } = useFetchApiData(`${API_URL}reporters`, token);
    const { data: loggedUsersData } = useFetchApiData(`${API_URL}logged-users`, token);
    const { data: departmentsData } = useFetchApiData(`${API_URL}departments`, token);
    const { data: teamsData } = useFetchApiData(`${API_URL}/teams`, token);

    useEffect(() => {
        if (productTypeData) {
            setProductTypes(productTypeData.productTypes || []);
        }
        if (departmentsData) {
            setDepartments(departmentsData.departments || []);
        }
        if (teamsData) {
            console.log('Teams', teamsData);
            setTeams(teamsData.teams || []);
        }
        if (taskTypeData) {
            setTaskTypes(taskTypeData.taskTypes || []);
            
            // Find the task type with the name 'Revision' and set its ID
            const revisionType = taskTypeData.taskTypes.find(task => task.name === 'Revision');
            if (revisionType) {
                setRevisionTaskTypeId(revisionType.id);  // Store the ID of the 'Revision' task type
            }
        }
        if (revisionTypeData) {
            setRevisionTypes(revisionTypeData.revisionTypes || []);
        }
        if (regionData) {
            setRegions(regionData.regions || []);
        }
        if (priorityData) {
            setPriorities(priorityData.priorities || []);
        }

        if (reporterData) {
            console.log("Reporter Data:", reporterData);
            setReporters(reporterData.reporters || []);
        }

        if (loggedUsersData && loggedUsersData.departments && loggedUsersData.teams) {

            const user = loggedUsersData;
            console.log('Logged In User', user);
    
            // Extract department info, ensuring departments exist
            const departmentId = user.departments.length > 0 ? user.departments[0].id : '';
            setSelectedDepartment(departmentId); // Set the selected department ID
    
            // Extract all teams assigned to the user (Many-to-many relationship)
            const loggedInUsersTeams = user.teams.length > 0 ? user.teams.map(team => team.id) : [];
            setLoggedInUsersTeams(loggedInUsersTeams);  // Set the teams here
    
            // Filter teams for the logged-in user
            const userTeams = filterTeamsForLoggedInUser(loggedInUsersTeams, user.teams);
            setFilteredTeams(userTeams);
    
            // Automatically select the first team if available, and set the selected team from user data
            if (userTeams.length > 0) {
                const defaultTeam = userTeams.find(team => team.id === user.teams[0].id); // Set from the logged-in user's team
                setSelectedTeam(defaultTeam ? defaultTeam.id : userTeams[0].id); // Set the default team
            }
        }
        
    }, [taskTypeData, revisionTypeData, regionData, priorityData, reporterData, loggedUsersData]);

    // The handler function for team selection
    const handleTeamChange = (e) => {
        const selectedTeamId = e.target.value; // Get the team ID from the selected option
        setSelectedTeam(selectedTeamId); // Update the selected team ID in the state
    };

    // Function to filter teams based on the logged-in user’s teams
    const filterTeamsForLoggedInUser = (loggedInUsersTeams, teams) => {
        return teams.filter(team => loggedInUsersTeams.includes(team.id));
    };

    // Handle department change and filter teams based on selected department
    const handleDepartmentChange = (e) => {
        const departmentId = e.target.value;
        setSelectedDepartment(departmentId);

        // Find the department name from loggedUsersData (check if departments exist)
        const department = loggedUsersData?.departments?.find(dept => dept.id === parseInt(departmentId));
        setSelectedDepartment(department ? department.name : "");

        // Filter teams based on the department selection
        const userTeams = filterTeamsForLoggedInUser(loggedInUsersTeams, loggedUsersData?.teams || []);
        setFilteredTeams(userTeams);

        // Automatically select the first team if available
        if (userTeams.length > 0 && !selectedTeam) {
            setSelectedTeam(userTeams[0].id); 
        }
    };

    // Handle team change and filter product types based on selected team
    const filterProductTypesByTeam = () => {
        if (!selectedTeam) return productTypes; 
        return productTypes.filter((productType) => productType.team_id === parseInt(selectedTeam));
    };

    const filteredProductTypes = filterProductTypesByTeam();


    // Task Type
    const filterTaskTypesByTeam = () => {
        if (!selectedTeam) return taskTypes;
        return taskTypes.filter((taskType) => taskType.team_id === parseInt(selectedTeam));
    };

    const filteredTaskTypes = filterTaskTypesByTeam();

    // Filter revision types dynamically based on the selected team and task type
    const filterRevisionTypesByTeam = () => {
        if (!selectedTeam || !selectedTaskType) return [];
        // Assuming taskTypes is an array of task types and each taskType has a name and id
        const selectedTaskTypeObj = taskTypes.find((task) => task.id === parseInt(selectedTaskType));
        if (selectedTaskTypeObj && selectedTaskTypeObj.name === 'Revision') {
            return revisionTypes.filter((revisionType) => revisionType.team_id === parseInt(selectedTeam));
        }
        return [];
    };

    // Set revisionTypeDisabled based on selectedTaskType being "Revision"
    const revisionTypeDisabled = () => {
        const selectedTaskTypeObj = taskTypes.find((task) => task.id === parseInt(selectedTaskType));
        return !(selectedTaskTypeObj && selectedTaskTypeObj.name === 'Revision');
    };

    const filteredRevisionTypes = revisionTypeDisabled() ? [] : filterRevisionTypesByTeam();

    // Handle task type change
    const handleTaskTypeChange = (e) => {
        const taskTypeId = e.target.value; // Ensure this gets the task type ID
        setSelectedTaskType(taskTypeId);  // Set the selected task type ID
        setSelectedRevisionType('');  // Reset revision type when task type changes
    };

    // Filter regions based on selected team
    const filterRegionsByTeam = () => {
        if (!selectedTeam) return regions;

        return regions.filter((regions) => regions.team_id === parseInt(selectedTeam));
    };

    const filteredRegions = filterRegionsByTeam();

    // Filter Priority based on selected team
    const filterPriorityByTeam = () => {
        if (!selectedTeam) return priorities;

        return priorities.filter((priorities) => priorities.team_id === parseInt(selectedTeam));
    };

    const filteredPriority = filterPriorityByTeam();



    // Debugging: Check the selected team ID and reporters filter
    const filterReportersByTeam = () => {
        if (!selectedTeam) return reporters; // If no team is selected, return all reporters
    
        // Filter reporters based on the selected team ID
        const filtered = reporters.filter((reporter) => reporter.team === parseInt(selectedTeam));
        console.log("Filtered Reporters: ", filtered);
        return filtered;
    };
    
    useEffect(() => {
        const filtered = filterReportersByTeam();
        setFilteredReporters(filtered); // Update filtered reporters based on the selected team
    }, [selectedTeam, reporters]);
    
    
    
    

    // Fetch and set task data when dataItemsId changes
    useEffect(() => {
        if (dataItemsId) {
            const fetchTaskData = async () => {
                const token = localStorage.getItem("token");
                try {
                    const response = await fetch(`${API_URL}task-detail/${dataItemsId}`, {
                        method: "GET",
                        headers: {
                            Authorization: `Bearer ${token}`,
                            "Content-Type": "application/json",
                        },
                    });
    
                    if (!response.ok) {
                        throw new Error("Failed to fetch task data.");
                    }
    
                    const data = await response.json();
                    const task = data.taskDetail;
    
                    setTicketNumber(task.ticket_number);
                    setMonth(task.month);
                    setWeek(task.week);
                    setReceivedDate(task.received_date);
                    setDueDate(task.due_date);
                    setUnit(task.unit);
                    setAccountName(task.account_name);
                    setCampaignName(task.campaign_name);
                    setNotes(task.notes);
    
                    const department = departments.find(dept => dept.name === task.department);
                    if (department) {
                        setSelectedDepartment(department.id);
                    }
    
                    // Normalize the team name comparison by trimming spaces and ignoring case
                    const normalizedTeamName = task.team.trim().toLowerCase();
                    const team = filteredTeams.find(team => team.name.trim().toLowerCase() === normalizedTeamName);
                    if (team) {
                        setSelectedTeam(team.id); // Set the selected team ID
                    } else {
                        console.log("No team found with the selected name", task.team);
                    }
    
                    setSelectedReporter(task.reporter_id);
                    setSelectedProductType(task.product_type_id);
                    setSelectedTaskType(task.task_type_id);
                    setSelectedRevisionType(task.revision_type_id);
                    setSelectedRegion(task.region_id);
                    setSelectedPriority(task.priority_id);
                } catch (error) {
                    console.error(error.message);
                }
            };
            fetchTaskData();
        }
    }, [dataItemsId, departments, filteredTeams]); // Ensure to re-run when filteredTeams changes
    
        
    // Handle saving edited task data
    const handleSubmit = async (event) => {
        event.preventDefault();

        const selectedTeamName = filteredTeams.find(team => team.id === parseInt(selectedTeam))?.name; // Find the team name by ID
            // Find the department by its id
        const department = departments.find(dept => dept.id === selectedDepartment);

        // Check if department was found and extract its name
        const departmentName = department ? department.name : "";

        const formData = {
            ticket_number: ticketNumber,
            month,
            week,
            received_date: receivedDate,
            due_date: dueDate,
            unit: unit,
            account_name: accountName,
            campaign_name: campaignName,
            notes,
            product_type_id: selectedProductType,
            task_type_id: selectedTaskType,
            revision_type_id: selectedRevisionType,
            reporter_id: selectedReporter,
            region_id: selectedRegion,
            priority_id: selectedPriority,
            department: departmentName,
            team: selectedTeamName, // Ensure correct key name for team
            updated_by: loggedInUserId
        };

        try {
            const token = localStorage.getItem("token");
            const response = await fetch(`${API_URL}task-detail/${dataItemsId}`, {
                method: "PUT",
                headers: {
                    Authorization: `Bearer ${token}`,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(formData),
            });

            if (!response.ok) {
                throw new Error("Failed to update task.");
            }

            const data = await response.json();
            console.log('Edited task', data);
            
            const taskNumber = data.taskDetail.ticket_number;
            //setSuccessMessage(`Task id "${data.taskDetail.ticket_number}" updated successfully!`);
            alertMessage('success', `Task id "${taskNumber}" updated successfully!`);
    
            // Optionally, close the modal after success
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage('');
            }, 2000); 

        } catch (error) {
            //setError(error.message);
            alertMessage('error');
        }
    };

    // Handle closing the modal
    const handleClose = () => {
        setVisible(false);
    };

    // Modal rendering
    return isVisible ? (
        <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
            <div className="bg-white rounded-lg shadow-md w-full max-w-4xl relative">
                <div className="flex justify-between items-center mb-4 bg-gray-100 px-4 py-2">
                    <h4 className="text-base text-left font-medium text-gray-800">Edit Task Record</h4>
                    <button
                        className="text-3xl text-gray-500 hover:text-gray-800"
                        onClick={handleClose}
                    >
                        &times;
                    </button>
                </div>
                <form onSubmit={handleSubmit}>
                    <div className="flex flex-wrap gap-6 text-left p-6 overflow-y-auto max-h-[70vh] scrollbar-vertical">
                        {/* Department Select */}
                        <div className="mb-4 w-full sm:w-[47%]">
                            <label className="block text-sm font-medium text-gray-700 pb-4">Department <span className='text-red-600'>*</span></label>
                            <select
                                id="department"
                                value={selectedDepartment}
                                onChange={handleDepartmentChange}
                                required
                                className="w-full p-2 border border-gray-300 rounded-md"
                            >
                                <option value="">Select a Department</option>
                                {loggedUsersData?.departments?.map((department) => (
                                    <option key={department.id} value={department.id}>
                                        {department.name}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Team Select */}
                        {selectedDepartment && (
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label className="block text-sm font-medium text-gray-700 pb-4">Teams <span className='text-red-600'>*</span></label>
                                <select
                                    id="teams"
                                    value={selectedTeam} // Keep the selectedTeam as team.id for internal logic
                                    onChange={(e) => handleTeamChange(e)} // Handle team change to store the team id
                                    required
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                >
                                    <option value="">Select a Team</option>
                                    {filteredTeams.length > 0 ? (
                                        filteredTeams.map((team) => (
                                            <option key={team.id} value={team.id}> {/* Use team.id as the value */}
                                                {team.name} {/* Display team name in the dropdown */}
                                            </option>
                                        ))
                                    ) : (
                                        <option value="" disabled>No teams available</option>
                                    )}
                                </select>
                            </div>
                        )}

                        {/* Ticket Related Data */}
                        {selectedTeam && (
                            <>
                                {/*  Product Type */}
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="productType" className="block text-sm font-medium text-gray-700 pb-4">
                                        Product Type <span className='text-red-600'>*</span>
                                    </label>
                                    <select
                                        id="productType"
                                        value={selectedProductType}
                                        onChange={(e) => setSelectedProductType(e.target.value)}
                                        required
                                        className="w-full p-2 border border-gray-300 rounded-md"
                                    >
                                        <option value="">Select a Product Type</option>
                                        {filteredProductTypes.length > 0 ? (
                                            filteredProductTypes.map((productType) => (
                                                <option key={productType.id} value={productType.id}>
                                                    {productType.name}
                                                </option>
                                            ))
                                        ) : (
                                            <option value="" disabled>No product types available</option>
                                        )}
                                    </select>
                                </div>

                                {/* Task Type */}
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="taskType" className="block text-sm font-medium text-gray-700 pb-4">
                                        Task Type <span className='text-red-600'>*</span>
                                    </label>
                                    <select
                                        id="taskType"
                                        value={selectedTaskType}
                                        onChange={(e) => setSelectedTaskType(e.target.value)}
                                        required
                                        className="w-full p-2 border border-gray-300 rounded-md"
                                    >
                                        <option value="">Select a Task Type</option>
                                        {filteredTaskTypes.length > 0 ? (
                                            filteredTaskTypes.map((taskType) => (
                                                <option key={taskType.id} value={taskType.id}>
                                                    {taskType.name}
                                                </option>
                                            ))
                                        ) : (
                                            <option value="" disabled>No task types available</option>
                                        )}
                                    </select>
                                </div>

                                {/* Revision Type */}
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="revisionType" className="block text-sm font-medium text-gray-700 pb-4">
                                        Revision Type
                                    </label>
                                    <select
                                        id="revisionType"
                                        value={selectedRevisionType}
                                        onChange={(e) => setSelectedRevisionType(e.target.value)}
                                        className={`w-full p-2 border border-gray-300 rounded-md ${revisionTypeDisabled() ? 'bg-gray-100' : ''}`}
                                        disabled={revisionTypeDisabled()} // Dynamically disable based on the task type
                                    >
                                        <option value="">Select a Revision Type</option>
                                        {filteredRevisionTypes.length > 0 ? (
                                            filteredRevisionTypes.map((revisionType) => (
                                                <option key={revisionType.id} value={revisionType.id}>
                                                    {revisionType.name}
                                                </option>
                                            ))
                                        ) : (
                                            <option value="" disabled>No revision types available</option>
                                        )}
                                    </select>
                                </div>


                                {/*  Priority */}
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="priority" className="block text-sm font-medium text-gray-700 pb-4">
                                        Priority <span className='text-red-600'>*</span>
                                    </label>
                                    <select
                                        id="priority"
                                        value={selectedPriority}
                                        onChange={(e) => setSelectedPriority(e.target.value)}
                                        required
                                        className="w-full p-2 border border-gray-300 rounded-md"
                                    >
                                        <option value="">Select a Priority Type</option>
                                        {filteredPriority.length > 0 ? (
                                            filteredPriority.map((priority) => (
                                                <option key={priority.id} value={priority.id}>
                                                    {priority.name}
                                                </option>
                                            ))
                                        ) : (
                                            <option value="" disabled>No priority available</option>
                                        )}
                                    </select>
                                </div>

                                {/*  Reporter */}
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="reporter" className="block text-sm font-medium text-gray-700 pb-4">
                                        Reporter <span className='text-red-600'>*</span>
                                    </label>
                                    <select
                                        id="reporter"
                                        value={selectedReporter || ""}
                                        onChange={(e) => setSelectedReporter(e.target.value)}
                                        required
                                        className="w-full p-2 border border-gray-300 rounded-md"
                                    >
                                        <option value="">Select a Reporter Type</option>
                                        {filteredReporters.length > 0 ? (
                                            filteredReporters.map((reporter) => (
                                                <option key={reporter.id} value={reporter.id}>
                                                    {reporter.name}
                                                </option>
                                            ))
                                        ) : (
                                            <option value="" disabled>No Reporter available</option>
                                        )}
                                    </select>
                                </div>


                                {/*  Region */}
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="region" className="block text-sm font-medium text-gray-700 pb-4">
                                        Region <span className='text-red-600'>*</span>
                                    </label>
                                    <select
                                        id="region"
                                        value={selectedRegion}
                                        onChange={(e) => setSelectedRegion(e.target.value)}
                                        required
                                        className="w-full p-2 border border-gray-300 rounded-md"
                                    >
                                        <option value="">Select a Region Type</option>
                                        {filteredRegions.length > 0 ? (
                                            filteredRegions.map((region) => (
                                                <option key={region.id} value={region.id}>
                                                    {region.name}
                                                </option>
                                            ))
                                        ) : (
                                            <option value="" disabled>No Region available</option>
                                        )}
                                    </select>
                                </div>
                            </>
                            
                        )}
                        
                        {/* Ticket Number */}
                        <div className="mb-4 w-full sm:w-[47%]">
                            <label htmlFor="ticketNumber" className="block text-sm font-medium text-gray-700 pb-4">
                                Ticket Number <span className='text-red-600'>*</span>
                            </label>
                            <input
                                id="ticketNumber"
                                type="text"
                                value={ticketNumber}
                                onChange={(e) => setTicketNumber(e.target.value)}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            />
                        </div>

                        {/* Received Date */}
                        <div className="mb-4 w-full sm:w-[47%]">
                            <label htmlFor="receivedDate" className="block text-sm font-medium text-gray-700 pb-4">
                                Received Date <span className='text-red-600'>*</span>
                            </label>
                            <input
                                id="receivedDate"
                                type="date"
                                value={receivedDate || ""}
                                onChange={(e) => setReceivedDate(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>

                        {/* Due Date */}
                        <div className="mb-4 w-full sm:w-[47%]">
                            <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 pb-4">
                                Due Date <span className='text-red-600'>*</span>
                            </label>
                            <input
                                id="dueDate"
                                type="date"
                                value={dueDate || ""}
                                onChange={(e) => setDueDate(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>

                        {/* Unit */}
                        <div className="mb-4 w-full sm:w-[47%]">
                            <label htmlFor="unit" className="block text-sm font-medium text-gray-700 pb-4">
                                Unit <span className='text-red-600'>*</span>
                            </label>
                            <input
                                id="unit"
                                type="number"
                                value={unit !== null ? unit : ""} // Ensure unit is either a number or empty string
                                onChange={(e) => setUnit(e.target.value ? parseFloat(e.target.value) : null)} // Handle empty values as null
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>

                        {/* Account Name */}
                        <div className="mb-4 w-full sm:w-[47%]">
                            <label htmlFor="accountName" className="block text-sm font-medium text-gray-700 pb-4">
                                Account Name <span className='text-red-600'>*</span>
                            </label>
                            <input
                                id="accountName"
                                type="text"
                                value={accountName || ""}
                                onChange={(e) => setAccountName(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>

                        {/* Campaign Name */}
                        <div className="mb-4 w-full sm:w-[47%]">
                            <label htmlFor="campaignName" className="block text-sm font-medium text-gray-700 pb-4">
                                Campaign Name <span className='text-red-600'>*</span>
                            </label>
                            <input
                                id="campaignName"
                                type="text"
                                value={campaignName || ""}
                                onChange={(e) => setCampaignName(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>

                        {/* Notes */}
                        <div className="mb-4 w-full sm:w-[100%]">
                            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 pb-4">
                                Notes
                            </label>
                            <textarea
                                id="notes"
                                value={notes || ""}
                                onChange={(e) => setNotes(e.target.value)}
                                rows={4}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>                   

                    </div>

                    <div className="p-4 text-center">
                        {error && <div className="text-red-600 text-sm mb-4">{error}</div>}
                        {successMessage && <div className="text-green-600 text-sm mb-4">{successMessage}</div>}
                    </div>

                    {/* Submit button */}
                    <div className="py-4 mb-4 text-center">
                        <button
                            type="submit"
                            className="bg-primary text-white px-6 py-3 rounded-md hover:bg-secondary"
                        >
                            Update Task Record
                        </button>
                    </div>
                </form>
            </div>
        </div>
    ) : null;
};

export default EditTaskRecord;
