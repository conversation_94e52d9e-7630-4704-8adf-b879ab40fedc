<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('departments', function (Blueprint $table) {
            // Add hod column if it doesn't exist
            if (!Schema::hasColumn('departments', 'hod')) {
                $table->string('hod')->nullable()->after('name');
            }
            
            // Add launch_date column if it doesn't exist
            if (!Schema::hasColumn('departments', 'launch_date')) {
                $table->date('launch_date')->nullable()->after('hod');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('departments', function (Blueprint $table) {
            if (Schema::hasColumn('departments', 'hod')) {
                $table->dropColumn('hod');
            }
            
            if (Schema::hasColumn('departments', 'launch_date')) {
                $table->dropColumn('launch_date');
            }
        });
    }
};
