import React, { useEffect, useState } from 'react';
import EditNotice from './EditNotice';
import ViewNotice from './ViewNotice';
import TablePagination from '../../common/table/TablePagination';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const isTokenValid = () => {
    return localStorage.getItem('token') !== null;
};

const NoticeList = () => {
    const [notices, setNotices] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewModalVisible, setViewModalVisible] = useState(false);
    const [selectedNotice, setSelectedNotice] = useState(null);
    const [selectedNoticeId, setSelectedNoticeId] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 5;

    useEffect(() => {
        fetchNotices();
    }, []);

    const fetchNotices = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            setLoading(false);
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}notices`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`Error ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            setNotices(data);
        } catch (error) {
            setError(error.message);
        } finally {
            setLoading(false);
        }
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const handleView = (notice) => {
        setSelectedNotice(notice);
        setViewModalVisible(true);
    };

    const handleEdit = (id) => {
        setSelectedNoticeId(id);
        setModalVisible(true);
    };

    const handleDelete = async (id) => {
        if (!window.confirm('Are you sure you want to delete this notice?')) return;

        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}notices/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete the notice');
            }

            setNotices((prevNotices) => prevNotices.filter((notice) => notice.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    if (notices.length === 0) {
        return <div className="text-center text-lg text-gray-500">No data found, please add data to see in the list.</div>;
    }

    const paginatedNotices = notices.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

    return (
        <div className="p-4">
            <table className="w-full border-collapse border border-gray-300">
                <thead>
                    <tr className="bg-gray-200">
                        <th className="border border-gray-300 px-4 py-2">SL</th>
                        <th className="border border-gray-300 px-4 py-2">Title</th>
                        <th className="border border-gray-300 px-4 py-2">Content</th>
                        <th className="border border-gray-300 px-4 py-2">Category</th>
                        <th className="border border-gray-300 px-4 py-2">Priority</th>
                        <th className="border border-gray-300 px-4 py-2">Department</th>
                        <th className="border border-gray-300 px-4 py-2">Team</th>
                        <th className="border border-gray-300 px-4 py-2">Expiry Date</th>
                        <th className="border border-gray-300 px-4 py-2">Published Date</th>
                        <th className="border border-gray-300 px-4 py-2">Status</th>
                        <th className="border border-gray-300 px-4 py-2">Created By</th>
                        <th className="border border-gray-300 px-4 py-2">Updated By</th>
                        <th className="border border-gray-300 px-4 py-2">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {paginatedNotices.map((notice, index) => (
                        <tr key={notice.id} className="hover:bg-gray-100">
                            <td className="border border-gray-300 px-4 py-2">{(currentPage - 1) * itemsPerPage + index + 1}</td>
                            <td className="border border-gray-300 px-4 py-2">{notice.title}</td>
                            <td className="border border-gray-300 px-4 py-2">
                                <div dangerouslySetInnerHTML={{ __html: notice.content }} />
                            </td>
                            <td className="border border-gray-300 px-4 py-2">{notice.category}</td>
                            <td className="border border-gray-300 px-4 py-2">{notice.priority}</td>
                            <td className="border border-gray-300 px-4 py-2">{notice.department}</td>
                            <td className="border border-gray-300 px-4 py-2">{notice.team}</td>
                            <td className="border border-gray-300 px-4 py-2">{notice.expiry_date}</td>
                            <td className="border border-gray-300 px-4 py-2">{notice.published_date}</td>
                            <td className="border border-gray-300 px-4 py-2">{notice.status}</td>
                            <td className="border border-gray-300 px-4 py-2">{notice.created_by}</td>
                            <td className="border border-gray-300 px-4 py-2">{notice.updated_by}</td>
                            <td className="border border-gray-300 px-4 py-2 flex space-x-2">
                                <button onClick={() => handleView(notice)} className="border border-gray-400 text-gray-700 px-3 py-1 rounded-md hover:bg-gray-100">
                                    View
                                </button>
                                <button onClick={() => handleEdit(notice.id)} className="bg-yellow-500 text-white px-3 py-1 rounded-md hover:bg-yellow-600">
                                    Edit
                                </button>
                                <button onClick={() => handleDelete(notice.id)} className="bg-red-500 text-white px-3 py-1 rounded-md hover:bg-red-600">
                                    Delete
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>

            <TablePagination currentPage={currentPage} totalItems={notices.length} itemsPerPage={itemsPerPage} onPageChange={handlePageChange} />

            <ViewNotice isVisible={viewModalVisible} setVisible={setViewModalVisible} notice={selectedNotice} />
            {modalVisible && <EditNotice isVisible={modalVisible} setVisible={setModalVisible} noticeId={selectedNoticeId} />}
        </div>
    );
};

export default NoticeList;
