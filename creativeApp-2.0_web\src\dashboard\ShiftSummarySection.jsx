import { useEffect, useState } from "react";
import Loading from "../common/Loading";
import { API_URL } from "../common/fetchData/apiConfig";

// Remove the normalizeShift function as it's now handled in the hook

const SmallStat = ({ label, value }) => (
  <div className="rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 px-4 py-3 flex items-center justify-between">
    <div className="text-sm font-medium text-gray-700 dark:text-gray-200">{label}</div>
    <div className="flex items-center gap-2 text-gray-900 dark:text-gray-100">
      <span>👤</span>
      <span className="font-semibold">{String(value).padStart(2, "0")}</span>
    </div>
  </div>
);

const ShiftCard = ({ shift }) => (
  <div className="rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4">
    <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">{shift.name} Shift</h4>
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
      <SmallStat label="Total Designer" value={shift.designer} />
      <SmallStat label="Total Developer" value={shift.developer} />
      <SmallStat label="Total QA" value={shift.qa} />
    </div>
  </div>
);

const ShiftSummarySection = () => {
  const [shifts, setShifts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchShiftData = async () => {
      const token = localStorage.getItem("token");
      if (!token) {
        setError("No auth token found");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch shift statistics from the new endpoint
        const shiftStatsResponse = await fetch(`${API_URL}/list/shift-stats`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
        });

        if (!shiftStatsResponse.ok) {
          throw new Error('Failed to fetch shift statistics');
        }

        const shiftStatsData = await shiftStatsResponse.json();

        // Process the shift data from backend
        const processedShifts = shiftStatsData.map(shift => ({
          name: shift.name,
          designer: shift.designer || 0,
          developer: shift.developer || 0,
          qa: shift.qa || 0,
          total: shift.total || 0
        }));

        // Sort by preferred order
        const order = ['evening', 'morning', 'night'];
        processedShifts.sort((a, b) =>
          order.indexOf(a.name.toLowerCase()) - order.indexOf(b.name.toLowerCase())
        );

        setShifts(processedShifts);
        setError(null);
      } catch (err) {
        console.error("Error fetching shift data:", err);
        setError("Unable to load shift data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchShiftData();
  }, []);

  if (loading) return <Loading />;
  if (error) return <div className="text-red-500 text-center py-4">{error}</div>;
  if (!shifts.length) return <div className="text-gray-500 text-center py-4">No shift data available</div>;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
      {shifts.map((shift, i) => (
        <ShiftCard key={`${shift.name}-${i}`} shift={shift} />
      ))}
    </div>
  );
};

export default ShiftSummarySection;