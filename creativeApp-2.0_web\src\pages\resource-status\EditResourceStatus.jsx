import React, { useEffect, useState } from 'react';
import { alertMessage } from '../../common/coreui';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditResourceStatus = ({ isVisible, setVisible, dataItemsId }) => {
    const [resourceStatusName, setResourceStatusName] = useState('');
    const [resourceStatuses, setResourceStatuses] = useState([]);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    useEffect(() => {
        const fetchStatusName = async () => {
            if (!dataItemsId) return;

            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}resource_statuses`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch resource statuses: ' + response.statusText);
                }

                const data = await response.json();
                const statusArray = data['resource_status'];  // Adjusted to access the resource statuses correctly
                if (!Array.isArray(statusArray)) {
                    throw new Error('Expected resource statuses to be an array.');
                }

                const statusData = statusArray.find(status => status.id === dataItemsId);
                if (statusData) {
                    setResourceStatusName(statusData.name);  // Set the name from the matching status
                } else {
                    throw new Error('Resource status not found. Please check the ID.');
                }
            } catch (error) {
                setError(error.message);
            }
        };

        fetchStatusName();
    }, [dataItemsId]);

    // Update the Resource Status
    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedStatusName = resourceStatusName.trim();

        // Get user_id from localStorage for 'updated_by'
        const updatedBy = loggedInUser;

        if (!updatedBy) {
            setError('User is not logged in.');
            return;
        }

        if (Array.isArray(resourceStatuses)) {
            const statusExists = resourceStatuses.some(status => {
                const statusNameLower = status.name.toLowerCase().trim();
                return statusNameLower === trimmedStatusName.toLowerCase();
            });

            if (statusExists) {
                setError('Resource status already exists. Please add a different status.');
                const timeoutId = setTimeout(() => setError(''), 3000);
                return () => clearTimeout(timeoutId);
            }
        }

        setError('');

        try {
            const token = localStorage.getItem('token');      

            const response = await fetch(`${API_URL}resource_statuses/${dataItemsId}`, {
                method: 'PUT',  // Use PUT for updating
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedStatusName,
                    updated_by: updatedBy,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to update resource status: ' + response.statusText);
            }

            const result = await response.json();

            //setSuccessMessage(result.message || 'Resource status updated successfully!'); // Use message from API response
            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Resource status updated successfully.',
            });

            setResourceStatusName('');

            // Optionally, refetch resource statuses
            const newResourceStatusesResponse = await fetch(`${API_URL}resource_statuses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!newResourceStatusesResponse.ok) {
                throw new Error('Failed to fetch resource statuses: ' + newResourceStatusesResponse.statusText);
            }

            const newResourceStatusesData = await newResourceStatusesResponse.json();
            setResourceStatuses(newResourceStatusesData.resource_status || []);

            // Close the modal after a short delay
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage(''); // Clear the success message
            }, 1000);

        } catch (error) {
            alertMessage('error');
        }
    };

    if (!isVisible) return null;

    return (
        <div
            className="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50"
            onClick={() => setVisible(false)}
        >
            <div
                className="relative bg-white rounded-lg shadow-lg max-w-md w-full p-5"
                onClick={(e) => e.stopPropagation()} // Prevent click from closing the modal
            >
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Update Resource Status</h3>
                    <button
                        className="text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>
                {error && <div className="text-red-500">{error}</div>}
                {successMessage && <div className="text-green-500">{successMessage}</div>}
                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label htmlFor="name" className="block mb-2">Resource Status Name</label>
                        <input
                            type="text"
                            id="name"
                            value={resourceStatusName}
                            onChange={(e) => setResourceStatusName(e.target.value)}
                            className="border rounded w-full p-2"
                            required
                        />
                    </div>
                    <button
                        type="submit"
                        className="bg-primary hover:bg-secondary text-white rounded-md px-4 py-2"
                    >
                        Update Resource Status
                    </button>
                </form>
            </div>
        </div>
    );
};

export default EditResourceStatus;
