import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const AddBlood = ({isVisible, setVisible}) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [bloods, setBloods] = useState([]);  // Track existing blood groups
    const [bloodName, setBloodName] = useState('');  // Blood group name input
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    useEffect(() => {
        const fetchBloods = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}bloods`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                setBloods(data.bloods);  // Set fetched blood groups
            } catch (error) {
                setError(error.message);
            }
        };

        fetchBloods();
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedBloodName = bloodName.trim();

        const createdBy = loggedInUser;

        if (!createdBy) {
            setError('User is not logged in.');
            return;
        }

        // Check if the blood group already exists
        const bloodExists = bloods.some((blood) => {
            const bloodNameLower = blood.name.toLowerCase().trim();
            return bloodNameLower === trimmedBloodName.toLowerCase();
        });

        if (bloodExists) {
            setError('Blood group already exists. Please add a different blood group.');
            setTimeout(() => setError(''), 3000);
            return;  // Exit if blood group already exists
        }

        setError('');  // Clear any previous error

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            // Send the new blood group data along with created_by and updated_by as full name
            const response = await fetch(`${API_URL}bloods`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedBloodName,
                    created_by: createdBy,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to save blood group: ' + response.statusText);
            }

            const result = await response.json();
            //setSuccessMessage(`Blood group "${result.name || trimmedBloodName}" added successfully!`);

            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Blood group added successfully.',
            });
            
            setBloodName('');  // Clear the blood group input field

            // Refetch the blood groups list after adding the new blood group
            const newBloodsResponse = await fetch(`${API_URL}bloods`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!newBloodsResponse.ok) {
                throw new Error('Failed to fetch blood groups: ' + newBloodsResponse.statusText);
            }

            const newBloodsData = await newBloodsResponse.json();
            setBloods(newBloodsData.bloods);  // Update the blood groups list

        } catch (error) {
            alertMessage('error');
        }
    };

    // Check if the current location is for the modal
    if (!isVisible) return null;

    return (
        <>
            
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                <div className="bg-white rounded-lg shadow-md w-full max-w-lg relative">
                    <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                        <h3 className="text-base text-left font-medium text-gray-800">Add Blood Group</h3>
                        <button
                            className="text-2xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                    </div>
                    <form onSubmit={handleSubmit} className='p-6'>
                        <div className="mb-4">
                            <label htmlFor="bloodName" className="block text-sm font-medium text-gray-700 pb-4">
                                Blood Group Name
                            </label>
                            <input
                                type="text"
                                id="bloodName"
                                value={bloodName}
                                onChange={(e) => setBloodName(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                            {error && <p className="text-red-500 text-sm">{error}</p>}
                        </div>
                        <div className='py-4'>
                            <button
                                type="submit"
                                className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                            >
                                Add Blood Group
                            </button>
                        </div>
                        {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                    </form>
                </div>
            </div>
            
        </>
    );
};

export default AddBlood;
