import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import useFetchApiData from '../../../common/fetchData/useFetchApiData'; // Importing custom hook
import { alertMessage } from '../../../common/coreui';

const API_URL = process.env.REACT_APP_BASE_API_URL+'/';

const EditProductType = ({ isVisible, setVisible, dataItemsId }) => {
    const navigate = useNavigate();
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [selectedDepartment, setSelectedDepartment] = useState('');
    const [selectedTeam, setSelectedTeam] = useState('');
    const [productTypeName, setProductTypeName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    // Fetch departments and teams using the custom hook
    const token = localStorage.getItem('token');
    const { data: departmentsData } = useFetchApiData(`${API_URL}departments`, token);
    const { data: teamsData } = useFetchApiData(`${API_URL}/teams`, token);

    useEffect(() => {
        if (departmentsData) {
            setDepartments(departmentsData.departments || []);
        }
        if (teamsData) {
            setTeams(teamsData.teams || []);
        }
    }, [departmentsData, teamsData]);

    // Fetch Product Type Details to Edit
    useEffect(() => {
        if (!dataItemsId || !departments.length) return;

        const fetchProductType = async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}product-type/${dataItemsId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch product type details');
                }

                const data = await response.json();
                const productType = data.productType;

                // Set the values from the fetched product type
                setProductTypeName(productType.name);

                // Set the selected department and team using their ids
                setSelectedDepartment(productType.department_id);
                setSelectedTeam(productType.team_id);

                // Fetch teams for the selected department
                const department = departments.find(dep => dep.id === productType.department_id);

                if (department && department.teams) {
                    setTeams(department.teams);

                    // Set the selected team (if not already set)
                    if (!productType.team_id && department.teams.length > 0) {
                        setSelectedTeam(department.teams[0].id);
                    }
                } else {
                    setTeams([]);
                }
            } catch (error) {
                setError(error.message);
            }
        };

        fetchProductType();
    }, [dataItemsId, departments]);

    // Handle Department Change and Fetch Teams
    const handleDepartmentChange = (e) => {
        const departmentId = e.target.value;
        setSelectedDepartment(departmentId);
        setSelectedTeam('');

        if (departmentId) {
            const department = departments.find(dep => dep.id === departmentId);

            if (department && department.teams && department.teams.length > 0) {
                setTeams(department.teams);
                setSelectedTeam(department.teams[0].id); // Set the first team's id as default
            } else {
                setTeams([]);
                setSelectedTeam('');
            }
        } else {
            setTeams([]);
            setSelectedTeam('');
        }
    };

    const handleSubmit = async (event) => {
        event.preventDefault();

        // Get user_id from localStorage for 'created_by'
        const updatedBy = loggedInUser;

        if (!updatedBy) {
            setError('User is not logged in.');
            return;
        }

        if (!selectedDepartment || !selectedTeam || !productTypeName) {
            setError('Please fill all fields.');
            return;
        }

        setError('');
        try {
            const token = localStorage.getItem('token');
            const userId = localStorage.getItem('user_id');  // Fetching logged-in user's ID
            if (!token || !userId) {
                setError('Authentication token or user ID is missing.');
                return;
            }

            const department = departments.find(dep => dep.id === selectedDepartment);
            const team = teams.find(t => t.id === selectedTeam);

            // Prepare request payload with department_id, team_id, and created_by (user_id)
            const response = await fetch(`${API_URL}product-type/${dataItemsId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    department_id: department.id,  // Send department ID (instead of object)
                    team_id: team.id,              // Send team ID (instead of object)
                    name: productTypeName,
                    updated_by: updatedBy          // Send user ID (created_by)
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to update product type.');
            }

            const result = await response.json();
            //setSuccessMessage(`Product Type "${result.product_type.name}" updated successfully!`);
            alertMessage('success')
            // Close the modal after a short delay
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage(''); // Clear the success message
            }, 2000);
        } catch (error) {
            //setError(error.message);
            alertMessage('error')
        }
    };

    const handleClose = () => {
        setVisible(false);
    };

    return (
        <>
            {isVisible && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative overflow-y-auto max-h-[90vh] mt-10">
                        <button onClick={handleClose} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                            &times;
                        </button>
                        <h4 className="text-xl font-semibold mb-4 py-4">Edit Product Type</h4>
                        <form onSubmit={handleSubmit}>
                            <div className="mb-4">
                                <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">
                                    Select Department
                                </label>
                                <select
                                    id="department"
                                    value={selectedDepartment}
                                    onChange={handleDepartmentChange}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select a Department</option>
                                    {departments.length === 0 ? (
                                        <option disabled>No departments available</option>
                                    ) : (
                                        departments.map((department) => (
                                            <option key={department.id} value={department.id}>
                                                {department.name}
                                            </option>
                                        ))
                                    )}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="team" className="block text-sm font-medium text-gray-700 pb-4">
                                    Select Team
                                </label>
                                <select
                                    id="team"
                                    value={selectedTeam}
                                    onChange={(e) => setSelectedTeam(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select a Team</option>
                                    {teams.length === 0 ? (
                                        <option disabled>No teams available</option>
                                    ) : (
                                        teams.map((team) => (
                                            <option key={team.id} value={team.id}>
                                                {team.name}
                                            </option>
                                        ))
                                    )}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="productTypeName" className="block text-sm font-medium text-gray-700 pb-4">
                                    Product Type Name
                                </label>
                                <input
                                    id="productTypeName"
                                    type="text"
                                    value={productTypeName}
                                    onChange={(e) => setProductTypeName(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                />
                            </div>

                            <div className="py-4">
                                <button
                                    type="submit"
                                    className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                                >
                                    Update Product Type
                                </button>
                            </div>

                            {error && <p className="text-red-500 text-sm">{error}</p>}

                            {successMessage && 
                            <div className='bg-[#DAF8E6] p-6 rounded-lg border-l-4 border-green-500 flex flex-row items-center'>
                                <span className="material-symbols-rounded bg-green-500 text-gray-700 p-1 rounded-md">check_circle</span>
                                {/* Success message display */}
                                <p className="text-green-500 text-xl font-medium pl-6">{successMessage}</p>
                            </div>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default EditProductType;
