import React, { useEffect, useState } from 'react';
import EditTimeCard from './EditTimeCard';
import TablePagination from '../../common/table/TablePagination';
import { API_URL } from './../../common/fetchData/apiConfig'; 
import TableContent from '../../common/table/TableContent';
import useFetchApiData from './../../common/fetchData/useFetchApiData'
import { DateTimeFormatTable } from '../../common/DateTimeFormatTable';
import { DateTimeFormatHour } from './../../common/DateTimeFormatTable';


const TimeCardList = ({ searchTerm }) => {
    const [timeCards, setTimeCards] = useState([]);
    const [taskDetails, setTaskDetails] = useState([]);
    const [productTypes, setProductTypes] = useState([]);
    const [taskTypes, setTaskTypes] = useState([]);
    const [recordTypes, setRecordTypes] = useState([]);
    const [revisionTypes, setRevisionTypes] = useState([]);
    const [regions, setRegions] = useState([]);
    const [priority, setPriority] = useState([]);
    const [reporters, setReporters] = useState([]);
    const [teams, setTeams] = useState([]);
    const [users, setUsers] = useState([]);
    const [schedules, setSchedules] = useState([]);
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedTimeId, setSelectedTimeId] = useState(null);
    const [filteredTimes, setFilteredTimes] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 20;

    const token = localStorage.getItem('token');

     // Fetching time cards using the custom hook
    const { data: timeCardsData } = useFetchApiData(`${API_URL}time-cards`, token);
    const { data: productTypesData} = useFetchApiData(`${API_URL}product-types`, token);
    const { data: taskTypesData} = useFetchApiData(`${API_URL}task-types`, token);
    const { data: revisionTypesData} = useFetchApiData(`${API_URL}revision-types`, token);
    const { data: recordTypesData} = useFetchApiData(`${API_URL}record-types`, token);
    const { data: priorityData} = useFetchApiData(`${API_URL}priorities`, token);
    const { data: teamsData} = useFetchApiData(`${API_URL}/teams`, token);
    const { data: usersData} = useFetchApiData(`${API_URL}/users`, token);
    const { data: shiftData} = useFetchApiData(`${API_URL}schedules`, token);
    const { data: taskDetailsData} = useFetchApiData(`${API_URL}task-details`, token);

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Date", key: "date" },
        { label: "Task Created At", key: "created_at" },
        { label: "Month", key: "month" },
        { label: "Week", key: "week" },
        { label: "Shift", key: "shift" },
        { label: "SEBPO Team Member", key: "user" },
        { label: "Task Ticket Number", key: "ticket" },
        { label: "Priority", key: "priority" },
        { label: "Task Type", key: "task_type" },
        { label: "Product Type", key: "product_type" },
        { label: "Record Type", key: "record_type" },
        { label: "Unit", key: "unit" },
        { label: "Work Hours", key: "hour" },
        { label: "Effort", key: "effort" }, //Need to calculate effort
        { label: "Reporter", key: "reporter" },
        { label: "Region", key: "region" },
        { label: "Account Name", key: "account" },
        { label: "Campaign Name", key: "campaign" },
        { label: "SLA Achieved?", key: "sla" },
        { label: "Client Reported Errors", key: "client_error" },
        { label: "Hight Priority / Rush Task", key: "high_priority" },
        { label: "Notes", key: "notes" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        if (recordTypesData && recordTypesData.recordTypes) {
            console.log('Record Types', recordTypesData);
            setRecordTypes(recordTypesData.recordTypes || []);
        } else {
            console.warn("recordTypesData or recordTypes is null or undefined");
            setRecordTypes([]);  // Ensure an empty array is set if there's an issue with recordTypesData
        }
    
        console.log('Time Card Data:', timeCardsData);
        if (timeCardsData) {
            console.log('Time Cards:', timeCardsData);
    
            // Create a map for record types from the recordTypesData
            const recordTypeMap = (recordTypesData?.recordTypes || []).reduce((map, recordType) => {
                if (recordType) {
                    map[recordType.id] = recordType.name;  // Map record type id to its name
                }
                return map;
            }, {});
    
            // Create other maps for product types, task types, priorities, etc.
            const productTypeMap = taskDetails.reduce((map, taskDetail) => {
                const productType = taskDetail.product_types;  // Assuming this contains the product type data
                if (productType && productType.id) {
                    map[productType.id] = productType.name;
                }
                return map;
            }, {});
    
            const taskTypeMap = taskDetails.reduce((map, taskDetail) => {
                const taskType = taskDetail.task_types;  // Assuming this contains the task type data
                if (taskType && taskType.id) {
                    map[taskType.id] = taskType.name;
                }
                return map;
            }, {});
    
            const priorityMap = taskDetails.reduce((map, taskDetail) => {
                const priority = taskDetail.priorities;  // Assuming this contains the priority data
                if (priority && priority.id) {
                    map[priority.id] = priority.name;
                }
                return map;
            }, {});
    
            const reporterMap = taskDetails.reduce((map, taskDetail) => {
                const reporter = taskDetail.reporters;  // Assuming this contains the reporter data
                if (reporter && reporter.id) {
                    map[reporter.id] = reporter.name;
                }
                return map;
            }, {});
    
            const regionMap = taskDetails.reduce((map, taskDetail) => {
                const region = taskDetail.regions;  // Assuming this contains the region data
                if (region && region.id) {
                    map[region.id] = region.name;
                }
                return map;
            }, {});
    
            const teamsMap = teams.reduce((map, team) => {
                map[team.id] = team.name;
                return map;
            }, {});
    
            const schedulesMap = schedules.reduce((map, shift) => {
                map[shift.id] = shift.shift_name;
                return map;
            }, {});
    
            const usersMap = usersData?.reduce((map, user) => {
                const fname = user.fname || 'User name not found';
                const lname = user.lname || '';
                const fullName = `${fname} ${lname}`.trim();
                map[user.id] = fullName;
                return map;
            }, {}) || {};
    
            // Map users' departments by user ID
            const userDepartmentsMap = usersData?.reduce((map, user) => {
                if (user.departments && user.departments.length > 0) {
                    map[user.id] = user.departments[0].name || 'No department'; // assuming a user can only belong to one department based on your structure
                }
                return map;
            }, {}) || {};
    
            // Function to get the week number from the date
            const getWeekNumber = (dateString) => {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return 'N/A';
                const startDate = new Date(date.getFullYear(), 0, 1);
                const days = Math.floor((date - startDate) / (24 * 60 * 60 * 1000));
                return Math.ceil((days + 1) / 7);
            };
    
            // Sort tasks in descending order based on `created_at` date
            //const sortedTimeCards = timeCardsData.timeCards.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    
            // Map time cards with the corresponding record_type name
            const mappedTimes = timeCardsData.timeCards.map((timeCard, index) => {
                // Ensure hour is a number before performing any multiplication
                const timeParts = timeCard.hour.split(':');
                const hours = timeParts[0] ? Number(timeParts[0]) : 0; // Extract hours (e.g., '14' from '14:35:00')
                const minutes = timeParts[1] ? Number(timeParts[1]) : 0; // Extract minutes (e.g., '35' from '14:35:00')
    
                // Calculate workHour as a decimal number
                const workHour = hours + (minutes / 60); // Convert minutes to fractional hours
                const effort = (workHour).toFixed(2);
    
                console.log('WorkHour:', workHour);
    
                return {
                    id: index + 1,
                    department: userDepartmentsMap[timeCard.user] || '',
                    team: teamsMap[timeCard.team] || '',
                    date: DateTimeFormatTable(timeCard.created_at) || 'N/A',
                    created_at: DateTimeFormatHour(timeCard.created_at) || 'N/A', // Format date
                    month: timeCard.created_at ? new Date(timeCard.created_at).toLocaleString('en-GB', { month: 'long' }) : 'N/A', // Month name from date
                    week: timeCard.created_at ? getWeekNumber(timeCard.created_at) : 'N/A', // Week number from date
                    shift: schedulesMap[timeCard.shift] || '',
                    user: usersMap[timeCard.user] || '',
                    ticket: timeCard.ticket || '',
                    priority: priorityMap[timeCard.priority] || 'N/A',
                    task_type: taskTypeMap[timeCard.task_type] || 'N/A',
                    product_type: productTypeMap[timeCard.product_type] || '',
                    record_type: recordTypeMap[timeCard.record_type] || 'N/A', // Mapping record_type using recordTypeMap
                    unit: timeCard.unit || '0',
                    hour: timeCard.hour || '0',
                    effort: effort || 0,
                    reporter: reporterMap[timeCard.reporter] || 'N/A',
                    region: regionMap[timeCard.region] || '',
                    account: timeCard.account || '',
                    campaign: timeCard.campaign || '',
                    sla: timeCard.sla || 'N/A',
                    client_error: timeCard.client_error || '0',
                    high_priority: timeCard.high_priority || 'N/A',
                    notes: timeCard.notes || '',
                    created_by: timeCard.created_by || 'N/A',
                    updated_by: timeCard.updated_by || 'N/A',
                };
            });
    
            // Set the updated time cards
            setTimeCards(mappedTimes);
            setFilteredTimes(mappedTimes);
        }
    
    }, [timeCardsData, currentPage, itemsPerPage, productTypes, recordTypesData]);
    

    useEffect(() => {

        if (productTypesData) {
          setProductTypes(productTypesData.productTypes || []);
        }
        if (taskTypesData) {
            setTaskTypes(taskTypesData.taskTypes || []);
        }
        if (revisionTypesData) {
            setRevisionTypes(revisionTypesData.revisionTypes || []);
        }
        if (teamsData) {
            setTeams(teamsData.teams || []);
        }
        if (shiftData) {
            setSchedules(shiftData.schedules || []);
        }
        if (usersData) {
            setUsers(usersData.users || []);
        }
        if (taskDetailsData) {
            console.log('Task Details:', taskDetailsData);
            setTaskDetails(taskDetailsData.taskDetails || []);
        }

      }, [productTypesData, taskTypesData, revisionTypesData, teamsData, shiftData, usersData]);

    // Filter search
    useEffect(() => {
        const normalizedSearchTerm = searchTerm ? searchTerm.toLowerCase().trim() : '';

        const highlightText = (text) => {
            const strText = text ? text.toString() : '';

            const regex = new RegExp(`(${normalizedSearchTerm})`, 'gi');
            const parts = strText.split(regex);

            return parts.map((part, index) => {
                return regex.test(part) ? (
                    <span key={index} className="bg-yellow-300">{part}</span>
                ) : part;
            });
        };

        if (!normalizedSearchTerm) {
            setFilteredTimes(timeCards);
            return;
        }

        const filtered = timeCards.filter(timeCard => {
            return Object.values(timeCard).some(value => {
                const valueStr = value ? value.toString().toLowerCase() : '';
                return valueStr.includes(normalizedSearchTerm);
            });
        }).map(timeCard => ({
            id: timeCard.id,
            ticket: highlightText(timeCard.ticket),
            priority: highlightText(timeCard.priority),
            task_type: highlightText(timeCard.task_type),
            product_type: highlightText(timeCard.product_type),
            record_type: highlightText(timeCard.record_type),
            unit: highlightText(timeCard.unit),
            hour: highlightText(timeCard.hour),
            reporter: highlightText(timeCard.reporter),
            region: highlightText(timeCard.region),
            account: highlightText(timeCard.account),
            campaign: highlightText(timeCard.campaign),
            sla: highlightText(timeCard.sla),
            client_error: highlightText(timeCard.client_error),
            notes: highlightText(timeCard.notes),
            created_by: highlightText(timeCard.created_by),
        }));

        setFilteredTimes(filtered);
    }, [searchTerm, timeCards]);

    // Pagination logic
    const startIndex = (currentPage - 1) * itemsPerPage;
    const currentPageUsers = filteredTimes.slice(startIndex, startIndex + itemsPerPage);

    const handleEdit = (id) => {
        setSelectedTimeId(id);
        setModalVisible(true);
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    // Show "No data found" message if filteredTimes is empty
    if (filteredTimes.length === 0) {
        return <div className="text-center text-lg text-gray-500">No data found, please add data to see in the list.</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={currentPageUsers}
                columnNames={columnNames}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedTimeId}
                hideDeleteButton={true}
            />
            <TablePagination
                currentPage={currentPage}
                totalItems={filteredTimes.length}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
            />
            {modalVisible && selectedTimeId && (
                <EditTimeCard 
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    timeCardId={selectedTimeId}
                />
            )}
        </div>
    );
};

export default TimeCardList;
