import React, { useState, useEffect } from 'react';

/**
 * NOTE:
 * The user data and world time data are expected to be fetched from the backend.
 * For now, we are using placeholder data.
 * - `user`: Should be passed as a prop or retrieved from a context (e.g., useAuth hook).
 * - `worldTimeData`: Should be fetched from the backend API, likely from `DateTimeController`.
 *   A custom hook like `useWorldTime()` would be appropriate here.
 */

const WelcomeCard = ({ user }) => {
    const [time, setTime] = useState(new Date());
    const [bgImage, setBgImage] = useState('');
    const [imageLoaded, setImageLoaded] = useState(false);

    // Placeholder for world time data, to be replaced with backend data.
    const worldTimeData = {
        english: 'Wednesday, 13 November, 2024, Late Autumn',
        bengali: 'বুধবার, ২৮শে কার্তিক, ১৪৩১ বঙ্গাব্দ, হেমন্ত ঋতু',
        hijri: "Al-arbi 'aa', 11 Jumada 1446 Hijri, Awakh<PERSON><PERSON>",
    };

    useEffect(() => {
        // Set a new random background image on component mount
        const imageUrl = `https://source.unsplash.com/1920x1080/?nature,dark,forest&sig=${Math.random()}`;
        setBgImage(imageUrl);

        // Preload the image to enable a smooth fade-in animation
        const img = new Image();
        img.src = imageUrl;
        img.onload = () => setImageLoaded(true);
    }, []);

    useEffect(() => {
        // Update the clock every second        const timerId = setInterval(() => setTime(new Date()), 1000);
        // Cleanup the interval on component unmount
        return () => clearInterval(timerId);
    }, []);

    // Formats the time to HH : MM AM/PM
    const formatTime = (date) => {
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
        });
    };

    // Use placeholder data if user prop is not provided
    const userName = user ? user.name : 'Team Member';
    const userPhoto = user && user.profile_photo_url ? user.profile_photo_url : `https://ui-avatars.com/api/?name=${userName.replace(' ', '+')}&color=7F9CF5&background=EBF4FF`;


    return (
        <div
            className="relative w-full h-full p-6 rounded-xl text-white overflow-hidden shadow-lg flex flex-col justify-between bg-gray-800"
            style={{
                backgroundImage: `url(${bgImage})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                transition: 'opacity 0.8s ease-in-out',
                opacity: imageLoaded ? 1 : 0,
            }}
        >
            {/* Dark Overlay for better text readability */}
            <div className="absolute inset-0 bg-black bg-opacity-50 z-0"></div>

            {/* Content Container */}
            <div className="relative z-10 flex flex-col h-full">
                {/* Top Section: Welcome Message */}
                <div className="flex-grow">
                    <h2 className="text-2xl font-semibold">Welcome Back 👋</h2>
                    <h1 className="text-4xl font-bold mt-1">{userName}</h1>
                </div>

                {/* Middle Section: Date Info */}
                <div className="mb-6 space-y-1">
                    <p className="text-sm font-medium">{worldTimeData.english}</p>
                    <p className="text-sm font-medium">{worldTimeData.bengali}</p>
                    <p className="text-sm font-medium">{worldTimeData.hijri}</p>
                </div>

                {/* Bottom Section: Profile & Time */}
                <div className="flex items-center justify-between">
                    <img
                        src={userPhoto}
                        alt="Profile"
                        className="w-16 h-16 rounded-full border-2 border-white object-cover"                    />
                    <div className="text-5xl font-bold tabular-nums">
                        {formatTime(time)}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default WelcomeCard;