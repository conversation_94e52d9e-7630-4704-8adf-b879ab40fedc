<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('history_todo', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('todo_id');
            // Foreign key relation
            $table->foreign('todo_id')->references('id')->on('todos')->onDelete('cascade'); 

            $table->string('change_filed');
            $table->string('change_data');
            $table->unsignedBigInteger('updater_id')->nullable();
            // Foreign key relation
            $table->foreign('updater_id')->references('id')->on('users')->onDelete('cascade'); 

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('history_todo');
    }
};
