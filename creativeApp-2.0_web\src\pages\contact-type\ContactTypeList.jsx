import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditContactType from './EditContactType'; // Assuming there's an EditContactType component similar to EditBlood

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const ContactTypeList = () => {
    const [contactTypes, setContactTypes] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [loading, setLoading] = useState(true); // Initially loading is true
    const [selectedContactTypeId, setSelectedContactTypeId] = useState(null);
    const [error, setError] = useState(null);

    // Update column names for Contact Types
    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Contact Type", key: "name" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchContactTypes = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}contact_types`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();

                setContactTypes(data.contact_types.map(contactType => ({
                    id: contactType.id,
                    name: contactType.name,
                    created_by: contactType.created_by,
                    updated_by: contactType.updated_by,
                })));
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchContactTypes();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}contact_types/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete contact type: ' + response.statusText);
            }

            // Update the contact types list after deletion
            setContactTypes(prevContactTypes => prevContactTypes.filter(contactType => contactType.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedContactTypeId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    // Show message when no contact types are available
    if (contactTypes.length === 0) {
        return <div className="text-gray-500">No data available</div>; // Show "No data available" if contactTypes array is empty
    }

    return (
        <div>
            <TableContent
                tableContent={contactTypes}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedContactTypeId}
            />
            {modalVisible && (
                <EditContactType
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    contactTypeId={selectedContactTypeId}
                />
            )}
        </div>
    );
};

export default ContactTypeList;
