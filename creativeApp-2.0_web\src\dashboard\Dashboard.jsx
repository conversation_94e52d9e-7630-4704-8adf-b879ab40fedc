import React from "react";
import WeatherData from "../pages/weatherAndTime/WeatherData";
import { API_URL } from "../common/fetchData/apiConfig";
import { useNavigate } from "react-router-dom";
import Loading from "../common/Loading";
import { getWorldTimeStrings } from "../utils/worldTimeUtils";

import WelcomeCard from "./WelcomeCard";
import ClientTeamsSection from "./ClientTeamsSection";
import ShiftSummarySection from "./ShiftSummarySection";

const isTokenValid = () => {
  const token = localStorage.getItem("token");
  return token !== null && token !== "";
};

const Dashboard = () => {
  const [userData, setUserData] = React.useState(null);
  const [error, setError] = React.useState(null);
  const [filterOptionLoading, setFilterOptionLoading] = React.useState(false);
  const navigate = useNavigate();

  // Real-time date/time strings
  const [dateTimeStrings, setDateTimeStrings] = React.useState(null);

  React.useEffect(() => {
    const token = localStorage.getItem("token");

    if (!isTokenValid()) {
      setError("No valid authentication token found.");
      setFilterOptionLoading(false);
      navigate("/login");
      return;
    }

    const user = localStorage.getItem("user");
    if (user) {
      setUserData(JSON.parse(user));
      return;
    }

    const fetchUserData = async () => {
      setFilterOptionLoading(true);
      try {
        const response = await fetch(`${API_URL}/logged-users`, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) throw new Error("Failed to fetch user data");
        const data = await response.json();
        setUserData(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setFilterOptionLoading(false);
      }
    };

    fetchUserData();
  }, [navigate]);

  // Fetch real-time date/time strings
  React.useEffect(() => {
    const fetchTimeStrings = async () => {
      try {
        const timeStrings = await getWorldTimeStrings();
        setDateTimeStrings(timeStrings);
      } catch (error) {
        console.error('Error fetching time strings:', error);
        // Fallback to static strings if API fails
        setDateTimeStrings({
          english: "Loading...",
          bengali: "লোড হচ্ছে...",
          hijri: "..."
        });
      }
    };

    fetchTimeStrings();
    // Update every minute
    const interval = setInterval(fetchTimeStrings, 60000);
    return () => clearInterval(interval);
  }, []);

  if (filterOptionLoading) return <Loading />;

  return (
    <div className="rounded-xl">
      {/* Top row: Welcome + Weather */}
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-12 lg:col-span-7">
          <WelcomeCard userData={userData} dateTimeStrings={dateTimeStrings} />
        </div>
        <div className="col-span-12 lg:col-span-5 flex flex-wrap border border-gray-300 dark:border-gray-600 rounded-2xl">
          <WeatherData />
        </div>
      </div>

      {/* Clients/Teams grid */}
      <div className="mt-4">
        <ClientTeamsSection />
      </div>

      {/* Shifts summary */}
      <div className="mt-4">
        <ShiftSummarySection />
      </div>

      {error && <div className="mt-4 text-red-500">{error}</div>}
    </div>
  );
};

export default Dashboard;