"use strict";(self.webpackChunkcreativeapp=self.webpackChunkcreativeapp||[]).push([[32],{16032:(e,t,a)=>{a.r(t),a.d(t,{default:()=>w});var s=a(65043),r=a(58786),l=a(13076),i=a(56025),n=a(83003),o=a(47554),d=a(72450),c=a(11238),u=a(32650),m=a(73216),h=a(70579);const f=()=>null!==localStorage.getItem("token"),g="https://creative.sebpo.net/api/",x=e=>{let{isVisible:t,setVisible:a,dataItemsId:r}=e;const[l,n]=(0,s.useState)([]),[o,d]=(0,s.useState)([]),[c,u]=(0,s.useState)(""),[m,x]=(0,s.useState)(""),[p,b]=(0,s.useState)(""),[y,v]=(0,s.useState)(""),[j,w]=(0,s.useState)(""),[N,_]=(0,s.useState)(null),[k,S]=(0,s.useState)(""),[C,$]=(0,s.useState)("");(0,s.useEffect)((()=>{const e=localStorage.getItem("user_id");e&&_(e)}),[]),(0,s.useEffect)((()=>{(async()=>{if(!f())return void S("No authentication token found.");const e=localStorage.getItem("token");try{const t=await fetch(`${g}departments`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch departments");const a=await t.json();d(a.departments)}catch(k){S(k.message)}})(),(async()=>{if(!f())return void S("No authentication token found.");const e=localStorage.getItem("token");try{const t=await fetch(`${g}/teams`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch teams");const a=await t.json();n(a.teams)}catch(k){S(k.message)}})(),(async()=>{if(!r)return;const e=localStorage.getItem("token");if(e)try{const t=await fetch(`${g}schedules/${r}`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch schedule data");const a=await t.json();a?(u(a.shift_name||""),x(a.shift_start||""),b(a.shift_end||""),w(a.department_id||""),v(a.team_id||"")):S("Schedule data is not available.")}catch(k){S(`Error fetching schedule data: ${k.message}`)}else S("No authentication token found.")})()}),[r]);const A=e=>{let[t,a]=e.split(":");t=parseInt(t,10);const s=t>=12?"PM":"AM";return t>12&&(t-=12),0===t&&(t=12),`${t}:${a} ${s}`};return(0,h.jsx)(h.Fragment,{children:t&&(0,h.jsx)("div",{className:"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden",children:(0,h.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative overflow-y-auto h-[80vh] mt-10",children:[(0,h.jsx)("button",{onClick:()=>{a(!1)},className:"absolute top-2 right-2 text-gray-400 hover:text-gray-900",children:"\xd7"}),(0,h.jsx)("h4",{className:"text-xl font-semibold mb-4 py-4",children:"Edit Schedule"}),(0,h.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),S(""),$("");const t=N;if(t)if(c&&m&&p&&y&&j)try{const e=localStorage.getItem("token");if(!e)return void S("Authentication token is missing.");const s=A(m),l=A(p),n=await fetch(`${g}schedules/${r}`,{method:"PUT",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"},body:JSON.stringify({shift_name:c,shift_start:s,shift_end:l,department_id:j,team_id:y,updated_by:t})}),o=await n.json();if(!1===o.status)return S(`Error: ${o.message}`),void(o.errors&&S(JSON.stringify(o.errors)));(0,i.GW)({icon:"success",title:"Success!",text:(null===o||void 0===o?void 0:o.message)||"Office schedule updated successfully."}),setTimeout((()=>{a(!1),$("")}),2e3)}catch(k){(0,i.GW)("error")}else S("Please fill all fields.");else S("User is not logged in.")},className:"text-left",children:[(0,h.jsxs)("div",{className:"mb-4",children:[(0,h.jsx)("label",{htmlFor:"department",className:"block text-sm font-medium text-gray-700 pb-4",children:"Select Team"}),(0,h.jsxs)("select",{id:"department",value:j,onChange:e=>w(e.target.value),required:!0,className:"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50",children:[(0,h.jsx)("option",{value:"",children:"Select a Department"}),o.map((e=>(0,h.jsx)("option",{value:e.id,children:e.name},e.id)))]})]}),(0,h.jsxs)("div",{className:"mb-4",children:[(0,h.jsx)("label",{htmlFor:"team",className:"block text-sm font-medium text-gray-700 pb-4",children:"Select Team"}),(0,h.jsxs)("select",{id:"team",value:y,onChange:e=>v(e.target.value),required:!0,className:"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50",children:[(0,h.jsx)("option",{value:"",children:"Select a Team"}),l.map((e=>(0,h.jsx)("option",{value:e.id,children:e.name},e.id)))]})]}),(0,h.jsxs)("div",{className:"mb-4",children:[(0,h.jsx)("label",{htmlFor:"shiftName",className:"block text-sm font-medium text-gray-700 pb-4",children:"Shift Name"}),(0,h.jsx)("input",{type:"text",id:"shiftName",value:c,onChange:e=>u(e.target.value),required:!0,className:"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"})]}),(0,h.jsxs)("div",{className:"flex flex-row justify-center gap-4",children:[(0,h.jsxs)("div",{className:"mb-4 w-full sm:w-1/2",children:[(0,h.jsx)("label",{htmlFor:"start-time",className:"block mb-2 text-sm font-medium text-gray-900 dark:text-white",children:"Start Time"}),(0,h.jsxs)("div",{className:"relative",children:[(0,h.jsx)("div",{className:"absolute inset-y-0 end-0 top-0 flex items-center pe-3.5 pointer-events-none",children:(0,h.jsx)("svg",{className:"w-4 h-4 text-gray-500 dark:text-gray-400","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 24 24",children:(0,h.jsx)("path",{fillRule:"evenodd",d:"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z",clipRule:"evenodd"})})}),(0,h.jsx)("input",{type:"time",id:"shiftStart",value:m,onChange:e=>x(e.target.value),required:!0,className:"bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"})]})]}),(0,h.jsxs)("div",{className:"mb-4 w-full sm:w-1/2",children:[(0,h.jsx)("label",{htmlFor:"end-time",className:"block mb-2 text-sm font-medium text-gray-900 dark:text-white",children:"End Time"}),(0,h.jsxs)("div",{className:"relative",children:[(0,h.jsx)("div",{className:"absolute inset-y-0 end-0 top-0 flex items-center pe-3.5 pointer-events-none",children:(0,h.jsx)("svg",{className:"w-4 h-4 text-gray-500 dark:text-gray-400","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 24 24",children:(0,h.jsx)("path",{fillRule:"evenodd",d:"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z",clipRule:"evenodd"})})}),(0,h.jsx)("input",{type:"time",id:"shiftEnd",value:p,onChange:e=>b(e.target.value),required:!0,className:"bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"})]})]})]}),(0,h.jsxs)("div",{className:"pb-4 text-center",children:[k&&(0,h.jsx)("p",{className:"text-red-500",children:k}),C&&(0,h.jsx)("p",{className:"text-green-500",children:C})]}),(0,h.jsx)("button",{type:"submit",className:"w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600",children:"Update Schedule"})]})]})})})};var p=a(59749),b=a(17974),y=a(58598);const v="Office Schedule",j=()=>{const[e,t]=(0,s.useState)({}),[a,f]=(0,s.useState)({}),[g,j]=(0,s.useState)(""),[w,N]=(0,s.useState)(""),[_,k]=(0,s.useState)(!1),[S,C]=(0,s.useState)(!1),[$,A]=(0,s.useState)(null),[E,F]=(0,s.useState)(null),[T,O]=(0,s.useState)(null),[I,R]=((0,m.Zp)(),(0,s.useState)(!1)),[P,D]=(0,s.useState)("created_at"),[M,B]=(0,s.useState)("desc"),[V,z]=(0,s.useState)("10"),[U,q]=(0,s.useState)(1),{data:Z,isFetching:G,error:L}=(0,u.PC_)({sort_by:P,order:M,page:U,per_page:V,query:w}),[W,{data:H,error:J}]=(0,u.IaY)(),[Q]=(0,u.tU8)(),Y=e=>{let t=Object.entries(e).reduce(((e,t)=>{let[a,s]=t;if("string"===typeof s)return e+`&${a}=${s}`;if(Array.isArray(s)){return e+`&${a}=${s.map((e=>e.value)).join(",")}`}return e}),"");N(t)},K=e=>{(0,o.$3)(e,["id","team","department","updated_at","updated_by","updater","created_at","creator","created_by","updated_by"]);O(null),k(!0)},X=e=>{O(null),A(e),k(!0)},ee=e=>{(0,i.YU)({onConfirm:()=>{Q(e),O(null)}})};let te=1;const{rolePermissions:ae}=(0,b.h)(),[se,re]=(0,s.useState)((()=>[{id:te++,name:"Action",width:"180px",className:"bg-red-300",cell:e=>(0,h.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>O(e),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>X(e.id),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>K(e),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})},{id:te++,name:"S.No",selector:(e,t)=>(U-1)*V+t+1,width:"80px",omit:!1},{id:te++,name:"Department",selector:e=>{var t;return(null===(t=e.department)||void 0===t?void 0:t.name)||"N/A"},db_title_field:"department.name",db_field:"department_id",sortable:!0,omit:!1,filterable:!0},{id:te++,name:"Team",selector:e=>{var t;return(null===(t=e.team)||void 0===t?void 0:t.name)||"N/A"},db_title_field:"team.name",db_field:"team_id",sortable:!0,omit:!1,filterable:!0},{id:te++,name:"Shift Name",db_field:"shift_name",selector:e=>e.shift_name||"",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Shift start time",db_field:"shift_name",selector:e=>(0,o.CE)(e.shift_start)||"",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Shift end time",db_field:"shift_name",selector:e=>(0,o.CE)(e.shift_end)||"",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created by",selector:e=>{var t,a;return`${(null===(t=e.creator)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.creator)||void 0===a?void 0:a.lname)||""}`},db_field:"created_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Date",selector:e=>(0,y.hb)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Time",selector:e=>(0,y.DF)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!1},{id:te++,name:"Updated by",selector:e=>{var t,a;return`${(null===(t=e.updater)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.updater)||void 0===a?void 0:a.lname)||""}`},db_field:"updated_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Date",selector:e=>(0,y.hb)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Time",selector:e=>(0,y.DF)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!1}]));(0,s.useEffect)((()=>{re((e=>[...e.map((e=>"Action"===e.name?{...e,cell:e=>(0,h.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>O(e),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>X(e.id),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>K(e),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})}:e))]))}),[ae]);const le=(0,n.wA)(),ie=(0,s.useCallback)((async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"group",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"select",l=e.db_field||"title";try{j(l),C(!0);var i=[];const n=await W({type:a.trim(),column:l.trim(),text:s.trim()});if(n.data&&(i=n.data),i.length){if("searchable"===r)return t((e=>({...e,[l]:i}))),i;const a=i.map((t=>{if(e.selector){let a=e.selector(t);return a?(t.total&&t.total>1&&(a+=` (${t.total})`),{label:a,value:t[l]}):null}})).filter(Boolean);return t((t=>({...t,[e.id]:(0,o.eb)(a)}))),a}}catch(E){F(E.message)}finally{C(!1)}}),[]);return(0,h.jsx)("section",{className:"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]",children:(0,h.jsxs)("div",{className:"mx-auto pb-6 ",children:[(0,h.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4",children:[(0,h.jsx)("div",{className:"w-4/12 md:w-10/12 text-start",children:(0,h.jsx)("h2",{className:"text-2xl font-bold ",children:v})}),(0,h.jsxs)("div",{className:"w-8/12 flex items-end justify-end gap-1",children:[(0,h.jsx)(i.DF,{columns:se,setColumns:re}),!G&&Z&&parseInt(Z.total)>0&&(0,h.jsx)(h.Fragment,{children:(0,h.jsxs)("button",{className:"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:async()=>{try{const t=await le(u.AZ1.endpoints.getScheduleData.initiate({sort_by:P,order:M,page:U,per_page:(null===Z||void 0===Z?void 0:Z.total)||10,query:w})).unwrap();if(null===t||void 0===t||!t.total||t.total<1)return!1;var e=1;let a=t.data.map((t=>{if(se.length){let a={};return se.forEach((s=>{!s.omit&&s.selector&&(a[s.name]="S.No"===s.name?e++:s.selector(t)||"")})),a}}));const s=c.Wp.json_to_sheet(a),r=c.Wp.book_new();c.Wp.book_append_sheet(r,s,"Sheet1");const l=c.M9(r,{bookType:"xlsx",type:"array"}),i=new Blob([l],{type:"application/octet-stream"});(0,d.saveAs)(i,`${v.replace(/ /g,"_")}_${a.length}.xlsx`)}catch(E){console.error("Error exporting to Excel:",E)}},children:[G&&(0,h.jsx)(h.Fragment,{children:(0,h.jsx)("span",{className:"material-symbols-outlined animate-spin text-sm me-2",children:"progress_activity"})}),!G&&(0,h.jsx)("span",{className:"material-symbols-outlined text-sm me-2",children:"file_export"}),"Export to Excel (",Z.total,")"]})}),ae.hasManagerRole&&(0,h.jsx)("button",{className:" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:()=>R(!0),children:"Add New"})]})]}),(0,h.jsx)(i.$6,{columns:se,selectedFilterOptions:a,setSelectedFilterOptions:f,fetchDataOptionsForFilterBy:ie,filterOptions:e,filterOptionLoading:S,showFilterOption:g,resetPage:()=>{if(Object.keys(a).length){let e={};Object.keys(a).map((t=>{"string"===typeof a[t]?e[t]="":e[t]=[]})),f({...e}),Y({...e})}q(1)},setCurrentPage:q,buildQueryParams:Y}),L&&(0,h.jsx)("div",{className:"text-red-500",children:E}),G&&(0,h.jsx)(l.A,{}),(0,h.jsx)("div",{className:"border border-gray-200 p-0 pb-1 rounded-lg my-5 ",children:(0,h.jsx)(r.Ay,{columns:se,data:(null===Z||void 0===Z?void 0:Z.data)||[],className:"p-0 scrollbar-horizontal-10",fixedHeader:!0,highlightOnHover:!0,responsive:!0,pagination:!0,paginationServer:!0,paginationPerPage:V,paginationTotalRows:(null===Z||void 0===Z?void 0:Z.total)||0,onChangePage:e=>{e!==U&&q(e)},onChangeRowsPerPage:e=>{e!==V&&(z(e),q(1))},paginationComponentOptions:{selectAllRowsItem:!0,selectAllRowsItemText:"ALL"},sortServer:!0,onSort:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"desc";Object.keys(e).length&&(D(e.db_field||e.name||"created_at"),B(t||"desc"))}})}),I&&(0,h.jsx)(p.A,{isVisible:I,setVisible:R}),_&&(0,h.jsx)(x,{isVisible:_,setVisible:k,dataItemsId:$}),T&&(0,h.jsx)(i.Qg,{item:T,setViewData:O,columns:se,handleEdit:X,handleDelete:ee})]})})},w=()=>(0,h.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-xl",children:(0,h.jsx)(j,{})})}}]);
//# sourceMappingURL=32.21a2f294.chunk.js.map