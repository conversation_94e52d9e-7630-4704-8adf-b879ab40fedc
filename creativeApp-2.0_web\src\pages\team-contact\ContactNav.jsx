import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

// Check if the token is available and valid
const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const ContactNav = ({ onSelectTeam, onShowAllContacts }) => {
    const location = useLocation(); // Track current location to highlight active menu
    const [teamData, setTeamData] = useState([]); // Store fetched teams
    const [error, setError] = useState(null); // Handle error messages
    const [loading, setLoading] = useState(true); // Loading state

    // Fetch teams from API when component mounts
    useEffect(() => {
        const fetchTeams = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token'); // Get token from local storage

            try {
                const response = await fetch(`${API_URL}/teams`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch teams: ' + response.statusText);
                }

                const data = await response.json();
                setTeamData(data.teams); // Set the teams fetched from API
            } catch (error) {
                setError(error.message); // Catch and display errors
            } finally {
                setLoading(false); // Set loading to false after fetching data
            }
        };

        fetchTeams(); // Call the fetchTeams function
    }, []);

    // Handle team click: Invoke the onSelectTeam callback passed as a prop
    const handleTeamClick = (teamId) => {
        onSelectTeam(teamId);
    };

    // Function to determine the active link based on current location
    const isActive = (path) => {
        return location.pathname === path ? 'bg-white text-gray-900' : 'text-gray-700';
    };

    // Show loading state, or error if there's an issue fetching data
    // if (loading) {
    //     return <div className="text-gray-500">Loading...</div>;
    // }

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div className="dark:bg-gray-900 rounded-xl">
            <nav>
                <ul className="flex flex-col text-left">
                    {/* "All Contacts" button */}
                    <li>
                        <button
                            className={`pb-4 hover:text-gray-900 flex items-center gap-4 ${isActive('/contacts/all')}`}
                            onClick={onShowAllContacts}
                        >
                            <span className="material-symbols-rounded text-xl">supervisor_account</span>
                            <div className="text-sm">All Team Members</div>
                        </button>
                    </li>


                    {/* "Frequent Contacts" button */}
                    <li>
                        <button
                            className={`pb-4 hover:text-gray-900 flex items-center gap-4 ${isActive('/contacts/frequent')}`}
                            onClick={() => handleTeamClick('frequent')} // Pass 'frequent' as teamId
                        >
                            <span className="material-symbols-rounded text-xl">manage_accounts</span>
                            <div className="text-sm">Frequent Contacts</div>
                        </button>
                    </li>

                    {/* Dynamically display teams */}
                    {teamData.length > 0 && (
                        <div>
                            <h5 className="w-full text-left font-bold text-sm text-gray-700 pb-2 border-b border-gray-300 mb-3">
                                Team List
                            </h5>
                            {teamData.map((team) => (
                                <li className="py-1" key={team.id}>
                                    <button
                                        className={`pb-4 hover:text-gray-900 flex items-center gap-4 ${isActive(`/contacts/team/${team.id}`)}`}
                                        onClick={() => handleTeamClick(team.id)} // Pass specific team ID
                                    >
                                        <img
                                            src={team.icon.startsWith('images/') ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${team.icon}` : team.icon}
                                            alt="Team Icon"
                                            className="w-4 h-4 object-cover"
                                        />
                                        <div className="text-sm">{team.name}</div>
                                    </button>
                                </li>
                            ))}
                        </div>
                    )}
                </ul>
            </nav>
        </div>
    );
};

export default ContactNav;
