<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\PasswordManager;
use <PERSON>vel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PasswordManagerApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations
        $this->artisan('migrate');
    }

    public function test_unauthenticated_user_cannot_access_password_managers()
    {
        $response = $this->getJson('/api/password-managers');
        $response->assertStatus(401);
    }

    public function test_authenticated_user_can_create_password_manager()
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $data = [
            'user_name' => 'Test Password Manager',
            'password' => 'test-password-123',
        ];

        $response = $this->postJson('/api/password-managers', $data);
        
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'user_name',
                        'password_strength',
                        'created_at',
                        'updated_at'
                    ]
                ])
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Password record created successfully'
                ]);
    }

    public function test_validation_fails_for_missing_required_fields()
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->postJson('/api/password-managers', []);
        
        $response->assertStatus(422)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'errors' => [
                        'user_name',
                        'password'
                    ]
                ])
                ->assertJson([
                    'status' => 'error',
                    'message' => 'Validation failed'
                ]);
    }

    public function test_user_can_view_their_own_password_managers()
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create a password manager for this user
        $passwordManager = PasswordManager::create([
            'user_id' => $user->id,
            'user_name' => 'Test Password',
            'password' => 'test-password-123',
        ]);

        $response = $this->getJson('/api/password-managers');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        '*' => [
                            'id',
                            'user_name',
                            'password_strength',
                            'created_at',
                            'updated_at'
                        ]
                    ],
                    'meta'
                ])
                ->assertJson([
                    'status' => 'success'
                ]);
    }

    public function test_user_can_update_their_own_password_manager()
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create a password manager for this user
        $passwordManager = PasswordManager::create([
            'user_id' => $user->id,
            'user_name' => 'Original Title',
            'password' => 'original-password',
        ]);

        $updateData = [
            'user_name' => 'Updated Title',
            'password' => 'updated-password-123',
        ];

        $response = $this->putJson("/api/password-managers/{$passwordManager->id}", $updateData);
        
        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Password record updated successfully'
                ]);
    }

    public function test_user_can_delete_their_own_password_manager()
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create a password manager for this user
        $passwordManager = PasswordManager::create([
            'user_id' => $user->id,
            'user_name' => 'To Be Deleted',
            'password' => 'delete-me-password',
        ]);

        $response = $this->deleteJson("/api/password-managers/{$passwordManager->id}");
        
        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Password record deleted successfully'
                ]);

        // Verify it's actually deleted
        $this->assertDatabaseMissing('password_managers', [
            'id' => $passwordManager->id
        ]);
    }

    public function test_user_cannot_access_others_password_managers()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        // Create password manager for user2
        $passwordManager = PasswordManager::create([
            'user_id' => $user2->id,
            'user_name' => 'User2 Password',
            'password' => 'user2-password',
        ]);

        // Try to access as user1
        Sanctum::actingAs($user1);

        $response = $this->getJson("/api/password-managers/{$passwordManager->id}");
        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => 'Unauthorized to view this password record'
                ]);
    }

    public function test_user_cannot_update_others_password_managers()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        // Create password manager for user2
        $passwordManager = PasswordManager::create([
            'user_id' => $user2->id,
            'user_name' => 'User2 Password',
            'password' => 'user2-password',
        ]);

        // Try to update as user1
        Sanctum::actingAs($user1);

        $response = $this->putJson("/api/password-managers/{$passwordManager->id}", [
            'user_name' => 'Hacked Title'
        ]);
        
        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => 'Unauthorized. Only the creator can update this password record'
                ]);
    }

    public function test_user_cannot_delete_others_password_managers()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        // Create password manager for user2
        $passwordManager = PasswordManager::create([
            'user_id' => $user2->id,
            'user_name' => 'User2 Password',
            'password' => 'user2-password',
        ]);

        // Try to delete as user1
        Sanctum::actingAs($user1);

        $response = $this->deleteJson("/api/password-managers/{$passwordManager->id}");
        
        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => 'Unauthorized. Only the creator can delete this password record'
                ]);
    }
}
