import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom'

// Function to check if the token is present in localStorage
const isTokenValid = () => {
  const token = localStorage.getItem('token');
  return token !== null && token !== ''; // Ensuring that the token is not empty
};

const API_URL = process.env.REACT_APP_BASE_API_URL;


const FetchLoggedInRole = () => {
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchUserData = async () => {
      const token = localStorage.getItem('token');
      
      if (!token) {
        setError('No valid authentication token found.');
        setLoading(false);
        navigate('/login');
        return;
      }

      const user = localStorage.getItem('user');

      if(user) {
        // setUserData(JSON.parse(user));
        const data = JSON.parse(user);
        const roles = data.roles.map(role => role.name);
        setUserData({ ...data, roles });
        setLoading(false);
        return; // Return early if user data is already available
      }

      try {
        const response = await fetch(`${API_URL}logged-users`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }

        const data = await response.json();

        // Ensure roles are extracted properly
        const roles = data.roles.map(role => role.name);  // Extract 'name' of each role

        setUserData({ ...data, roles });  // Add roles as an array of names

      } catch (error) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  return { userData, loading, error };
};

export default FetchLoggedInRole;
