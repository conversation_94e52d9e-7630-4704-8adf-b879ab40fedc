import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const AddMemberTest = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [users, setUsers] = useState([]);
    const [roles, setRoles] = useState([]);
    const [teams, setTeams] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [designations, setDesignations] = useState([]);
    const [resourceTypes, setResourceTypes] = useState([]);
    const [resourceStatuses, setResourceStatuses] = useState([]);
    const [eid, setEid] = useState('');
    const [email, setEmail] = useState('');
    const [selectedRoles, setSelectedRoles] = useState({});
    const [selectedTeams, setSelectedTeams] = useState({});
    const [selectedDepartments, setSelectedDepartments] = useState([]);
    const [selectedDesignations, setSelectedDesignations] = useState([]);
    const [selectedResourceTypes, setSelectedResourceTypes] = useState([]);
    const [selectedResourceStatuses, setSelectedResourceStatuses] = useState([]);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    const fetchUsers = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/users`, {
                method: 'GET',

                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }

            const data = await response.json();

            setUsers(data);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the roles to select for users
    const fetchRoles = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}roles`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
            console.log(data); // Log to see the structure of data
    
            // Access the roles array from the data object
            const rolesData = data.roles; // Get the roles array
    
            const rolesMap = rolesData.reduce((acc, role) => {
                acc[role.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setRoles(rolesData);
            setSelectedRoles(rolesMap);
        } catch (error) {
            setError(error.message);
        }
    };   

    // Fetching all the teams to select for users
    const fetchTeams = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/teams`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
            console.log(data); // Log to see the structure of data
    
            // Access the teams array from the data object
            const teamsData = data.teams; // Get the teams array
    
            const teamsMap = teamsData.reduce((acc, team) => {
                acc[team.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setTeams(teamsData);
            setSelectedTeams(teamsMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the departments to select for users
    const fetchDepartments = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}departments`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
            console.log(data);
    
            // Access the teams array from the data object
            const departmentsData = data.departments; // Get the teams array
    
            const departmentsMap = departmentsData.reduce((acc, department) => {
                acc[department.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setDepartments(departmentsData);
            setSelectedDepartments(departmentsMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the designations to select for users
    const fetchDesignations = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}designations`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the teams array from the data object
            const designationsData = data.designations; // Get the teams array
    
            const designationsMap = designationsData.reduce((acc, designation) => {
                acc[designation.id] = false;
                return acc;
            }, {});
    
            setDesignations(designationsData);
            setSelectedDesignations(designationsMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the Resource Type (Responsibility Level) to select for users
    const fetchResourceTypes = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}resource_types`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
            console.log('Resource Types:', data);
    
            // Access the teams array from the data object
            const resourceTypesData = data['Resource Types']; // Get the resourceType array
    
            const resourceTypesMap = resourceTypesData.reduce((acc, resourceType) => {
                acc[resourceType.id] = false;
                return acc;
            }, {});
    
            setResourceTypes(resourceTypesData);
            setSelectedResourceTypes(resourceTypesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the resourceStatuses to select for users
    const fetchResourceStatuses = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}resource_statuses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the resourceStatuses array from the data object
            const resourceStatusesData = data.resourceStatuses; // Get the resourceStatuses array
    
            const resourceStatusesMap = resourceStatusesData.reduce((acc, resourceStatus) => {
                acc[resourceStatus.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setResourceStatuses(resourceStatusesData);
            setSelectedResourceStatuses(resourceStatusesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    useEffect(() => {
        fetchUsers();
        fetchRoles();
        fetchTeams();
        fetchDepartments();
        fetchDesignations();
        fetchResourceTypes();
        fetchResourceStatuses();
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedEid = eid.trim();
        const trimmedEmail = email.trim();
    
        if (!trimmedEid || !trimmedEmail) {
            setError('EID and Email are required.');
            return;
        }
    
        const eidExists = users.some(user => typeof user.eid === 'string' && user.eid.toLowerCase().trim() === trimmedEid.toLowerCase());
        const emailExists = users.some(user => typeof user.email === 'string' && user.email.toLowerCase().trim() === trimmedEmail.toLowerCase());
    
        if (eidExists || emailExists) {
            let message = 'The ';
            if (eidExists) message += 'EID ';
            if (emailExists) message += (message.endsWith('The ') ? '' : 'or ') + 'Email ';
            message += 'already exists. Please add a new EID and/or Email.';
            setError(message);
            setTimeout(() => setError(''), 1000);
            return;
        }
    
        setError('');

        // Prepare roles array based on selected roles
        const selectedRoleIds = Object.keys(selectedRoles).filter(roleId => selectedRoles[roleId]);
        const selectedTeamIds = Object.keys(selectedTeams).filter(teamId => selectedTeams[teamId]);
        // Always passed as an array (even if one department is selected)
        const selectedDepartmentIds = Array.isArray(selectedDepartments) ? selectedDepartments : [selectedDepartments];
        const selectedDesignationIds = Array.isArray(selectedDesignations) ? selectedDesignations : [selectedDesignations];
        const selectedResourceTypeIds = Array.isArray(selectedResourceTypes) ? selectedResourceTypes : [selectedResourceTypes];
        const selectedResourceStatusIds = Array.isArray(selectedResourceStatuses) ? selectedResourceStatuses : [selectedResourceStatuses];

        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}/users`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    eid: trimmedEid,
                    email: trimmedEmail,
                    roles: selectedRoleIds.map(Number), // Send the roles array as numbers
                    teams: selectedTeamIds.map(Number), // Send the teams array as numbers
                    departments: selectedDepartmentIds.map(Number), // Send the departments as numbers
                    designations: selectedDesignationIds.map(Number),
                    resourceTypes: selectedResourceTypeIds.map(Number),
                    resourceStatuses: selectedResourceStatusIds.map(Number),
                }),
            });
    
            if (!response.ok) {
                throw new Error('Failed to save user: ' + response.statusText);
            }
    
            const result = await response.json();
            setSuccessMessage(`User with EID "${trimmedEid}" added successfully!`);
            setEid('');
            setEmail('');
            setSelectedRoles({});
            setSelectedTeams({});
            setSelectedDepartments([]);
            setSelectedDesignations([]);
            setSelectedResourceTypes([]);
            setSelectedResourceStatuses([]);
    
            fetchUsers();
        } catch (error) {
            setError(error.message);
        }
    };

    const isModalOpen = location.pathname === '/add-member';

    const handleClose = () => {
        navigate('/member-onboard');
    };

    return (
        <>
            {isModalOpen && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-6xl relative  overflow-y-auto h-[80vh] mt-10">
                        <button onClick={handleClose} className="absolute top-0 right-2 text-gray-400 hover:text-gray-900 text-4xl">
                            &times;
                        </button>
                        <h4 className="text-2xl text-left font-semibold mb-6">Onboard New Team Member</h4>
                        <form onSubmit={handleSubmit}>
                            <div className='flex flex-wrap gap-4'>
                                <div className="mb-4 w-full md:max-w-[23%] text-left">
                                    <label htmlFor="eid" className="block text-sm font-medium text-gray-700 pb-4">
                                        EID
                                    </label>
                                    <input
                                        type="text"
                                        id="eid"
                                        value={eid}
                                        onChange={(e) => setEid(e.target.value)}
                                        placeholder='Add Team Member ID'
                                        required
                                        className="py-3 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    />
                                </div>
                                <div className="mb-4 w-full md:max-w-[23%] text-left">
                                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 pb-4">
                                        Email
                                    </label>
                                    <input
                                        type="email"
                                        id="email"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        placeholder='Add Team Member Email'
                                        required
                                        className="py-3 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    />
                                    {error && <p className="text-red-500 text-sm pt-4">{error}</p>}
                                </div>
                                {/* Select Designations */}
                                <div className="">
                                    <label htmlFor="designation" className="block text-sm font-medium text-gray-700 pb-4">
                                        Designation
                                    </label>
                                    <div className="">
                                        <select
                                            value={selectedDesignations || ''}
                                            onChange={(e) => setSelectedDesignations(e.target.value)} // This updates selectedDesignation state
                                            className="py-3 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                            >
                                            <option value="" disabled>Select a designation</option>
                                            {designations.map((designation) => (
                                                <option key={designation.id} value={designation.id}>
                                                {designation.name}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                </div>

                                {/* Select Responsibility Level (Resource Types) */}
                                <div className="">
                                    <label htmlFor="resource-type" className="block text-sm font-medium text-gray-700 pb-4">
                                        Resource Type
                                    </label>
                                    <div className="">
                                        <select
                                            value={selectedResourceTypes || ''}
                                            onChange={(e) => setSelectedResourceTypes(e.target.value)} // This updates selectedResourceTypes state
                                            className="py-3 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                        >
                                            <option value="" disabled>Select a resource type</option>
                                            {resourceTypes.map((resourceType) => (
                                                <option key={resourceType.id} value={resourceType.id}>
                                                    {resourceType.name}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                </div>

                                {/* Select Roles */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        Roles
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {roles.map(role => (
                                            <label className="inline-flex items-center" key={role.id}>
                                                <input
                                                    type="checkbox"
                                                    checked={selectedRoles[role.id] || false}
                                                    onChange={() => setSelectedRoles(prev => ({ ...prev, [role.id]: !prev[role.id] }))}
                                                    className="form-checkbox my-2"
                                                />
                                                <span className="ml-2">{role.name}</span> {/* Assuming role has a 'name' property */}
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                {/* Select Teams */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        Teams
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {teams.map(team => (
                                            <label className="inline-flex items-center" key={team.id}>
                                                <input
                                                    type="checkbox"
                                                    checked={selectedTeams[team.id] || false}
                                                    onChange={() => setSelectedTeams(prev => ({ ...prev, [team.id]: !prev[team.id] }))}
                                                    className="form-checkbox my-2"
                                                />
                                                <span className="ml-2">{team.name}</span> {/* Assuming role has a 'name' property */}
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                {/* Select Department */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        Department
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {departments.map(department => (
                                            <label className="inline-flex items-center" key={department.id}>
                                                <input
                                                    type="radio"
                                                    name="department"  // Same name attribute ensures only one department can be selected
                                                    value={department.id}
                                                    checked={selectedDepartments === department.id} // Compare selectedDepartments directly to department.id
                                                    onChange={() => setSelectedDepartments(department.id)} // Set the department.id as the selected one
                                                    className="form-radio my-2"
                                                />
                                                <span className="ml-2">{department.name}</span> {/* Assuming department has a 'name' property */}
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                {/* Select Resource Statuses */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        Resource Statuses
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {resourceStatuses.map(resourceStatus => (
                                            <label className="inline-flex items-center" key={resourceStatus.id}>
                                                <input
                                                    type="radio"
                                                    name="resourceStatus"
                                                    value={resourceStatus.id}
                                                    checked={selectedResourceStatuses === resourceStatus.id}
                                                    onChange={() => setSelectedResourceStatuses(resourceStatus.id)}
                                                    className="form-radio my-2"
                                                />
                                                <span className="ml-2">{resourceStatus.name}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>
                            </div>
                            <div className='py-4 text-left'>
                                <button
                                    type="submit"
                                    className="w-56 bg-primary hover:bg-secondary text-white rounded-md py-3 m-0"
                                >
                                    Sent Invitation
                                </button>
                            </div>
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default AddMemberTest;