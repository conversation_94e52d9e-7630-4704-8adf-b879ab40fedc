import React, { useEffect, useState } from 'react';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditNoticeBoardCategory = ({ isVisible, setVisible, categoryId }) => {
    const [categoryName, setCategoryName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    useEffect(() => {
        const fetchCategory = async () => {
            if (categoryId) {
                const token = localStorage.getItem('token');
                try {
                    const response = await fetch(`${API_URL}notice-board-category/${categoryId}`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    });

                    if (!response.ok) {
                        throw new Error('Failed to fetch category: ' + response.statusText);
                    }

                    const data = await response.json();
                    setCategoryName(data.category.name);
                } catch (error) {
                    setError(error.message);
                }
            }
        };

        fetchCategory();
    }, [categoryId]);

    const handleSubmit = async (event) => {
        event.preventDefault();
        const token = localStorage.getItem('token');
        
        if (!token) {
            setError('Authentication token is missing.');
            return;
        }
    
        let firstName = localStorage.getItem('fname');
        let lastName = localStorage.getItem('lname');
    
        if (!firstName || !lastName) {
            console.warn("User first and last name are missing in localStorage. Setting default values.");
            firstName = firstName || "Unknown";
            lastName = lastName || "User";
        }
    
        const fullName = `${firstName} ${lastName}`;
    
        try {
            const response = await fetch(`${API_URL}notice-board-category/${categoryId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: categoryName.trim(),
                    updated_by: fullName,
                }),
            });
    
            if (!response.ok) {
                throw new Error('Failed to update category: ' + response.statusText);
            }
    
            const result = await response.json();
            setSuccessMessage(`Category "${result.name}" updated successfully!`);
    
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage('');
            }, 1000);
            
        } catch (error) {
            setError(error.message);
        }
    };
    
    
    if (!isVisible) return null;

    return (
        <div
            className="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50"
            onClick={() => setVisible(false)}
        >
            <div
                className="relative bg-white rounded-lg shadow-lg max-w-md w-full p-5"
                onClick={(e) => e.stopPropagation()}
            >
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Update Notice Board Category</h3>
                    <button
                        className="text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>
                {error && <div className="text-red-500">{error}</div>}
                {successMessage && <div className="text-green-500">{successMessage}</div>}
                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label htmlFor="name" className="block mb-2">Category Name</label>
                        <input
                            type="text"
                            id="name"
                            value={categoryName}
                            onChange={(e) => setCategoryName(e.target.value)}
                            className="border rounded w-full p-2"
                            required
                        />
                    </div>
                    <button
                        type="submit"
                        className="bg-primary hover:bg-secondary text-white rounded-md px-4 py-2"
                    >
                        Update Category
                    </button>
                </form>
            </div>
        </div>
    );
};

export default EditNoticeBoardCategory;
