import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditBranch from './EditBranch'; // Updated import to your EditBranch component

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const BranchList = () => {
    const [branches, setBranches] = useState([]); // State to store branches
    const [modalVisible, setModalVisible] = useState(false); // Modal visibility state
    const [selectedBranchId, setSelectedBranchId] = useState(null); // State for selected branch ID
    const [error, setError] = useState(null); // State for error message
    const [loading, setLoading] = useState(false); // State for loading spinner

    // Column names for the table
    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Branch Name", key: "name" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    // Fetch branches when component mounts
    useEffect(() => {
        const fetchBranches = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }
    
            const token = localStorage.getItem('token');
            setLoading(true); // Set loading to true when fetch starts
    
            try {
                const response = await fetch(`${API_URL}branches`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }
    
                const data = await response.json();
                console.log("Branches", data);
    
                // Ensure data.branches is an array before calling map
                if (Array.isArray(data.branches)) {
                    setBranches(data.branches.map(branch => ({
                        id: branch.id,
                        name: branch.name,
                        created_by: branch.created_by,
                        updated_by: branch.updated_by,
                    })));
                    setError(null); // Clear any previous errors
                } else {
                    setError('Invalid data format: branches is not an array.');
                }
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false); // Set loading to false when done
            }
        };
    
        fetchBranches();
    }, []);
    

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}branches/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete branch: ' + response.statusText);
            }

            // Update the branches list after deletion
            setBranches(prevBranches => prevBranches.filter(branch => branch.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedBranchId(id);
        setModalVisible(true);
    };

    // Show loading state or error message
    if (loading) {
        return <div>Loading branches...</div>;
    }

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={branches} // Pass branches to the TableContent component
                columnNames={columnNames}
                onDelete={handleDelete} // Handle delete action
                onEdit={handleEdit} // Handle edit action
                setModalVisible={setModalVisible} // Pass modal visibility function
                setSelectedServiceId={setSelectedBranchId} // Pass selected branch ID
            />
            {modalVisible && (
                <EditBranch
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    branchId={selectedBranchId} // Pass selected branch ID for editing
                />
            )}
        </div>
    );
};

export default BranchList;
