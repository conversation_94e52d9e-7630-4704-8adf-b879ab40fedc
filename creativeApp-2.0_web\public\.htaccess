<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /

    # Skip rewriting for existing files and directories
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^ - [L]

    # Skip rewriting for storage, api, and backend paths
    RewriteCond %{REQUEST_URI} ^/(storage|api|backend)/ [NC]
    RewriteRule ^ - [L]

    # Redirect everything else to index.html (React handles routing)
    RewriteRule ^ index.html [QSA,L]
</IfModule>
