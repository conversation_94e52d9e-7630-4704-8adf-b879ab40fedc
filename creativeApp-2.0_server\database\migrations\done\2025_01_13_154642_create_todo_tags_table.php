<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('todo_tags', function (Blueprint $table) {
            $table->id();            
            $table->string('name');
            $table->unsignedBigInteger('creator_id');
            // Foreign key relation
            $table->foreign('creator_id')->references('id')->on('users')->onDelete('cascade');

            $table->unsignedBigInteger('updater_id')->nullable();
            // Foreign key relation
            $table->foreign('updater_id')->references('id')->on('users')->onDelete('cascade'); 
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('todo_tags');
    }
};
