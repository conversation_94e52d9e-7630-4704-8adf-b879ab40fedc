import React, { useEffect, useState } from 'react';
import EditPriority from './EditPriority';
import TableContent from '../../common/table/TableContent';

const isTokenValid = () => {
    const token = localStorage.getItem('token');

    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const PriorityList = () => {
    const [priorities, setPriorities] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedPriorityId, setSelectedPriorityId] = useState(null);
    const [error, setError] = useState(null);

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Product Type Name", key: "name" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchPriorities = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}priorities`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();

                setPriorities(data.priorities.map(priority => ({
                    id: priority.id,
                    department: priority.department,
                    team: priority.team,
                    name: priority.name,
                    created_by: priority.created_by,
                    updated_by: priority.updated_by,
                })));
            } catch (error) {
                setError(error.message);
            }
        };

        fetchPriorities();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}priority/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete priority: ' + response.statusText);
            }

            // Update the priority list after deletion
            setPriorities(prevPriorities => prevPriorities.filter(priority => priority.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedPriorityId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={priorities}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedPriorityId}
            />
            {modalVisible && (
                <EditPriority
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    priorityId={selectedPriorityId}
                />
            )}
        </div>
    );
};

export default PriorityList;
