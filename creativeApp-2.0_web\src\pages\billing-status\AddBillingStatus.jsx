import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const AddBillingStatus = ({isVisible, setVisible}) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [billingStatuses, setBillingStatuses] = useState([]);
    const [billingStatusName, setBillingStatusName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    useEffect(() => {
        const fetchBillingStatuses = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }
    
            const token = localStorage.getItem('token');
    
            try {
                const response = await fetch(`${API_URL}billing_statuses`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }
    
                const data = await response.json();
                // Update to use data['billing statuses']
                setBillingStatuses(data['billing statuses'] || []);
            } catch (error) {
                setError(error.message);
            }
        };
    
        fetchBillingStatuses();
    }, []);
    

    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedStatusName = billingStatusName.trim();

        // Get user_id from localStorage for 'created_by'
        const createdBy = loggedInUser;

        if (!createdBy) {
            setError('User is not logged in.');
            return;
        }
    
        // Check if the billing status already exists
        if (Array.isArray(billingStatuses)) {
            const statusExists = billingStatuses.some(status => {
                const statusNameLower = status.name.toLowerCase().trim();
                return statusNameLower === trimmedStatusName.toLowerCase();
            });
    
            if (statusExists) {
                setError('Billing status already exists. Please add a different status.');
                const timeoutId = setTimeout(() => setError(''), 3000);
                return () => clearTimeout(timeoutId);
            }
        }
    
        setError('');
    
        try {
            const token = localStorage.getItem('token');
    
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }
           
    
            // Create the billing status
            const response = await fetch(`${API_URL}billing_statuses`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedStatusName,
                    created_by: createdBy,
                }),
            });
    
            if (!response.ok) {
                console.error('Failed to save billing status:', response.statusText);
                throw new Error('Failed to save billing status: ' + response.statusText);
            }
    
            const result = await response.json();
           
            //setSuccessMessage(`Billing status "${result.name || trimmedStatusName}" added successfully!`);
            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Billing status added successfully.',
            });

            setBillingStatusName('');
    
            // Refetch billing statuses
            const newBillingStatusesResponse = await fetch(`${API_URL}billing_statuses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!newBillingStatusesResponse.ok) {
                throw new Error('Failed to fetch billing statuses: ' + newBillingStatusesResponse.statusText);
            }
    
            const newBillingStatusesData = await newBillingStatusesResponse.json();
            setBillingStatuses(newBillingStatusesData['billing statuses'] || []);
    
        } catch (error) {
            alertMessage('error');
        }
    };
    
    
    if (!isVisible) return null;

    return (
        <>

            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                <div className="bg-white rounded-lg shadow-md w-full max-w-lg relative">
                    <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                        <h3 className="text-base text-left font-medium text-gray-800">Add New Billing Status</h3>
                        <button
                            className="text-2xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                    </div>
                    <form onSubmit={handleSubmit} className='p-6'>
                        <div className="mb-4">
                            <label htmlFor="billingStatusName" className="block text-sm font-medium text-gray-700 pb-4">
                                Billing Status Name
                            </label>
                            <input
                                type="text"
                                id="billingStatusName"
                                value={billingStatusName}
                                onChange={(e) => setBillingStatusName(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                            {error && <p className="text-red-500 text-sm">{error}</p>}
                        </div>
                        <div className='py-4'>
                            <button
                                type="submit"
                                className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3 hover:bg-blue-800"
                            >
                                Add Billing Status
                            </button>
                        </div>
                        {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                    </form>
                </div>
            </div>

        </>
    );
};

export default AddBillingStatus;
