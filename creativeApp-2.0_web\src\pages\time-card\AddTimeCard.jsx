import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import useFetchApiData from './../../common/fetchData/useFetchApiData.jsx';
import { API_URL } from './../../common/fetchData/apiConfig';
import DatePicker from 'react-datepicker';
import moment from 'moment-timezone';
import { alertMessage } from "../../common/coreui";
import SearchFilterSelect from "../../common/utility/SearchFilterSelect.jsx";
import LoadingIcon from "./../../common/LoadingIcon";
import { ViewSubmitFormData } from './../../common/coreui';
import { timeCardsApi,  useLazyFetchDataOptionsForTimeCardsQuery, useCreateTimeCardMutation, useUpdateTimeCardMutation } from "../../features/api";


const AddTimeCard = ({isVisible, setVisible}) => {
    const [formData, setFormData] = useState({});
     const [confirmation, setConfirmation] = useState(false);
        const [viewData, setViewData] = useState(false);

    const [ticketNumber, setTicketNumber] = useState("");
    const [teams, setTeams] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [schedules, setSchedules] = useState([]);
    const [loggedUsers, setLoggedUsers] = useState([]);
    const [taskDetails, setTaskDetails] = useState([]);
    const [productTypes, setProductTypes] = useState([]);
    const [taskTypes, setTaskTypes] = useState([]);
    const [recordTypes, setRecordTypes] = useState([]);
    const [reviewRelease, setReviewRelease] = useState([]);
    const [selectedReviewRelease, setSelectedReviewRelease] = useState("Client Delivery");
    const [selectedRecordType, setSelectedRecordType] = useState("Work");
    const [revisionTypes, setRevisionTypes] = useState([]);
    const [regions, setRegions] = useState([]);
    const [priorities, setPriorities] = useState([]);
    const [slaAchieves, setSlaAchieves] = useState("Yes");
    const [reporters, setReporters] = useState([]);
    const [hour, setHour] = useState(''); 
    const [hourError, setHourError] = useState('');
    const [entryDate, setEntryDate] = useState("");
    const [accountName, setAccountName] = useState("");
    const [loggedInUserId, setLoggedInUserId] = useState("");
    const [loggedInUsersDepartment, setLoggedInUsersDepartment] = useState("");
    const [loggedInUsersDepartmentId, setLoggedInUsersDepartmentId] = useState("");
    const [loggedInUsersTeamId, setLoggedInUsersTeamId] = useState("");
    const [loggedInUsersteamName, setLoggedInUsersTeam] = useState("");
    const [campaignName, setCampaignName] = useState("");
    const [unit, setUnit] = useState("");
    const [clientError, setClientError] = useState("");
    const [internalError, setInternalError] = useState("");
    const [notes, setNotes] = useState("");

    const [selectedTeamId, setSelectedTeamId] = useState(null);
    const [selectedTaskUnit, setSelectedTaskUnit] = useState(null);
    const [productTypeId, setProductTypeId] = useState(null);
    const [taskTypeId, setTaskTypeId] = useState(null);
    const [recordTypeId, setRecordTypeId] = useState(null);
    const [reviewReleaseId, setReviewReleaseId] = useState(null);
    const [revisionTypeId, setRevisionTypeId] = useState(null);
    const [regionId, setRegionId] = useState(null);
    const [priorityId, setPriorityId] = useState(null);
    const [reporterId, setReporterId] = useState(null);
    const [ticketNumbers, setTicketNumbers] = useState([]);
    const [selectedTaskType, setSelectedTaskType] = useState("");

    //const [startDate, setStartDate] = useState(new Date());
    const [startDate, setStartDate] = useState(null);


    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [errorMessage, setErrorMessage] = useState('');
    const [successMessage, setSuccessMessage] = useState("");
    const navigate = useNavigate();
    const location = useLocation();


    const token = localStorage.getItem('token');

    // Fetching data using the custom hook
    const { data: teamsData } = useFetchApiData(`${API_URL}/teams`, token);
    const { data: reportersData } = useFetchApiData(`${API_URL}/users`, token);
    const { data: schedulesData } = useFetchApiData(`${API_URL}/schedules`, token);
    const { data: loggedUsersData } = useFetchApiData(`${API_URL}/logged-users`, token);
    const { data: taskDetailsData } = useFetchApiData(`${API_URL}/task-filter-dates`, token);
    const { data: departmentsData } = useFetchApiData(`${API_URL}/departments`, token);
    const { data: productTypeData } = useFetchApiData(`${API_URL}/product-types`, token);
    const { data: taskTypeData } = useFetchApiData(`${API_URL}/task-types`, token);
    const { data: recordTypeData } = useFetchApiData(`${API_URL}/record-types`, token);
    const { data: reviewReleaseData } = useFetchApiData(`${API_URL}/reviews`, token);
    const { data: revisionTypeData } = useFetchApiData(`${API_URL}/revision-types`, token);
    const { data: regionData } = useFetchApiData(`${API_URL}/regions`, token);
    const { data: priorityData } = useFetchApiData(`${API_URL}/priorities`, token);
    const { data: reporterData } = useFetchApiData(`${API_URL}/reporters`, token);

    useEffect(() => {
        if (teamsData && teamsData.teams) {
            setTeams(teamsData.teams || []);
        }
    
        if (Array.isArray(reporterData)) {
            console.log('Reporters Data on Add Time', reporterData.reporters);
            setReporters(reporterData.filter(reporter => reporter.fname || reporter.lname) || []); // Only reporters with fname or lname
        } else {
            console.error('Invalid data: reporterData is not an array', reporterData);
        }        
    
        if (schedulesData) {
            setSchedules(schedulesData.schedules || []);
        }
    
        if (loggedUsersData) {
            const user = loggedUsersData;
            const departmentId = user.departments && user.departments.length > 0 ? user.departments[0].id : '';
            const departmentName = user.departments && user.departments.length > 0 ? user.departments[0].name : '';
            const loggedInUsersTeamIds = user.teams ? user.teams.map(team => team.id) : []; // Multiple team IDs
            const loggedInUsersteamNames = user.teams ? user.teams.map(team => team.name) : [];

            const loggedInUserId = user.id;

            setLoggedInUserId(loggedInUserId);
            setLoggedInUsersDepartmentId(departmentId);
            setLoggedInUsersDepartment(departmentName);
            setLoggedInUsersTeamId(loggedInUsersTeamIds);
            setLoggedInUsersTeam(loggedInUsersteamNames);

            setLoggedUsers(loggedUsersData.users || []);
        }
    
        if (taskDetailsData && Array.isArray(taskDetailsData.taskDetails)) {

            console.log('taskDetailsData:', taskDetailsData);

            // Set task details if taskDetailsData exists and is an array
            setTaskDetails(taskDetailsData.taskDetails || []);
            
            // Find the selected task based on ticket number
            const selectedTask = taskDetailsData.taskDetails.find(task => task.ticket_number === ticketNumber);
        
            if (selectedTask) {
                // Set the unit for the selected task, defaulting to 0 if no unit is provided
                setSelectedTaskUnit(selectedTask.unit || 0);
            }
        
            // Check if there are any task details and set the default values
            if (taskDetailsData.taskDetails.length > 0) {
                const firstTask = taskDetailsData.taskDetails[0]; // Get the first task
        
                setAccountName(firstTask.account_name || "");
                setCampaignName(firstTask.campaign_name || "");
                setProductTypeId(firstTask.product_type_id || "");
                setTaskTypeId(firstTask.task_type_id || "");
                setRevisionTypeId(firstTask.revision_type_id || "");
                setPriorityId(firstTask.priority_id || "");
                setRegionId(firstTask.region_id || "");
                setReporterId(firstTask.reporter_id || "");
            }
        }
        
    
        if (selectedTeamId && Array.isArray(teamsData?.teams) && Array.isArray(taskDetailsData?.taskDetails)) {
            const selectedTeam = teamsData.teams.find(team => team.id === Number(selectedTeamId));
        
            if (selectedTeam) {
                const teamName = selectedTeam.name;
        
                // Filter task details based on the team name
                const filteredTaskDetails = taskDetailsData.taskDetails.filter(task => task.team === teamName);
        
                // Extract unique ticket numbers using a Set
                const uniqueTicketNumbers = [...new Set(filteredTaskDetails.map(task => task.ticket_number))];
        
                // Set the ticket numbers to state
                setTicketNumbers(uniqueTicketNumbers);
        
                // If ticketNumber is already selected, update task details
                if (ticketNumber) {
                    const selectedTask = filteredTaskDetails.find(task => task.ticket_number === ticketNumber);
        
                    if (selectedTask) {
                        // Set task details, including productTypeId
                        setAccountName(selectedTask.account_name || "");
                        setCampaignName(selectedTask.campaign_name || "");
                        setProductTypeId(selectedTask.product_type_id || "");  // Unified setting for productTypeId
                        setTaskTypeId(selectedTask.task_type_id || "");
                        setRevisionTypeId(selectedTask.revision_type_id || "");
                        setPriorityId(selectedTask.priority_type_id || "");
                        setRegionId(selectedTask.region_id || "");
                        setReporterId(selectedTask.reporter_id || "");
                    }
                }
            }
        }
        

        // Select Account Name, Campaign Name, Product Type and All the Formations by selecting the Ticket Number
        if (ticketNumber && Array.isArray(taskDetails)) {
            const selectedTask = taskDetails.find(task => task.ticket_number === ticketNumber);
    
            if (selectedTask) {
                setAccountName(selectedTask.account_name || "");
                setCampaignName(selectedTask.campaign_name || "");
                setProductTypeId(selectedTask.product_type_id || "");
                setTaskTypeId(selectedTask.task_type_id || "");
                setRevisionTypeId(selectedTask.revision_type_id || "");
                setPriorityId(selectedTask.priority_id || "");
                setRegionId(selectedTask.region_id || "");
                setReporterId(selectedTask.reporter_id || "");
            }
        }
    
        if (departmentsData) {
            setDepartments(departmentsData.departments || []);
            setLoading(false)
        }

        if (productTypeData) {
            setProductTypes(productTypeData.productTypes || []);
        }

        if (taskTypeData) {
            setTaskTypes(taskTypeData.taskTypes || []);
        }

        if (revisionTypeData) {
            setRevisionTypes(revisionTypeData.revisionTypes || []);
        }

        if (priorityData) {
            setPriorities(priorityData.priorities || []);
        }

        if (regionData) {
            setRegions(regionData.regions || []);
        }

        if (reporterData) {
            setReporters(reporterData.reporters || []);
        }

        if (recordTypeData) {
            console.log('Record Types:', recordTypeData);
            setRecordTypes(recordTypeData.recordTypes || []);
        }

        if (reviewReleaseData && Array.isArray(reviewReleaseData.reviews)) {
            setReviewRelease(reviewReleaseData.reviews);
        } else {
            setReviewRelease([]); // ensure it's always an array
        }

    }, [teamsData, reportersData, schedulesData, loggedUsersData, taskDetailsData, taskDetails, departmentsData, selectedTeamId, ticketNumber, productTypeData, taskTypeData, revisionTypeData, priorityData, regionData, reporterData, recordTypeData, reviewReleaseData]);


    const filteredTeams = teams.filter(team => 
        loggedInUsersTeamId.includes(team.id) // Check if the team ID is in the logged-in user's assigned teams
    );
    
    // Filter schedules based on the selected team ID
    const filteredSchedules = schedules.filter(schedule => {
        const scheduleTeamIds = schedule.teams ? schedule.teams.map(team => team.id) : [];
        const isScheduleForSelectedTeam = scheduleTeamIds.includes(Number(selectedTeamId));
    
        return isScheduleForSelectedTeam;
    });


    // Date Formations
    const currentDate = new Date();
    const formattedCurrentDate = currentDate.toISOString().split('T')[0];

    // Date 3 days ago for min value in input
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(currentDate.getDate() - 3);
    const formattedThreeDaysAgo = threeDaysAgo.toISOString().split('T')[0];

    // Set initial entry date when taskDetailsData is available
    useEffect(() => {
        if (taskDetailsData && taskDetailsData.taskDetails && taskDetailsData.taskDetails.length > 0) {
            const taskDate = taskDetailsData.taskDetails[0].date || formattedCurrentDate;
            const formattedTaskDate = new Date(taskDate).toISOString().split('T')[0];
            setEntryDate(formattedTaskDate);
        } else {
            setEntryDate(formattedCurrentDate);
        }

        const formattedHour = currentDate.toISOString().split('T')[1].substring(0, 5);
        // setHour(formattedHour);
        setHour("00:00"); // Initialize hour to 0

    }, [taskDetailsData]);

    // Check if revisionTypeId is valid for the selected task
    const isRevisionTaskType = () => {
        const selectedTaskTypeObj = taskTypes.find((task) => task.id === parseInt(taskTypeId)); // Use taskTypeId here
        return selectedTaskTypeObj && selectedTaskTypeObj.name === 'Revision'; // Check if task type is "Revision"
    };
    
    // 2. Set revisionTypeDisabled based on whether the task type is "Revision"
    const revisionTypeDisabled = !isRevisionTaskType(); 
    
      

    // Handle unit change and validation
    const handleUnitChange = (e) => {
        const value = Number(e.target.value);

        // If selectedTaskUnit is null, we skip validation (can also add a fallback condition)
        if (selectedTaskUnit !== null && value > selectedTaskUnit) {
            setErrorMessage(`Unit cannot exceed ${selectedTaskUnit}`);
        } else {
            setErrorMessage(''); // Clear any error message if the input is valid
            setUnit(value); // Update the unit state with the valid input
        }
    };
    
    
    // Handle select change to store the selected record type as an integer
    const handleSelectChange = (event) => {
        setSelectedRecordType(parseInt(event.target.value, 10));
    };

    const handleReviewReleaseChange = (event) => {
        setSelectedReviewRelease(parseInt(event.target.value, 10));
    };
    
    const handleChange = (date) => {
        
        if (date) {
            // Get the hours and minutes
            const hours = date.getHours();
            const minutes = date.getMinutes().toString().padStart(2, '0');  // Ensure two digits for minutes

            // Format the time as HH:mm (24-hour format)
            const time = `${hours.toString().padStart(2, '0')}:${minutes}`;

            setHour(time);  // Set the formatted hour in HH:mm format
            setStartDate(date);  // Store the full date object (if necessary for other purposes)
        } else {
            setHour('');  // Clear if no date is selected
            setStartDate(null);  // Reset date if cleared
        }
    };
    
    

    // Handle form submission
    const [createTimeCard] = useCreateTimeCardMutation();
    const handleSubmit = async (event) => {
        if(event) event.preventDefault();

        if (!startDate) {
            setHourError('Time is required');
            return;
        }
    
        setHourError('');

        // Ensure selectedRecordType is an integer, and handle cases where it's null or empty
        const intRecordTypes = selectedRecordType ? parseInt(selectedRecordType, 10) : null;
        const intReviewRelease = selectedReviewRelease ? parseInt(selectedReviewRelease, 10) : null;

        
        const intProductTypes = parseInt(productTypeId, 10);
        const intTaskTypes = parseInt(taskTypeId, 10);
        const intRevisionTypes = parseInt(revisionTypeId, 10);
        const intPriorities = parseInt(priorityId, 10);
        const intRegions = parseInt(regionId, 10);
        const intReporters = parseInt(reporterId, 10);
        
        setFormData({
            date: entryDate,
            team_id: event?.target.team.value || formData?.team_id || "",
            shift_id: event?.target.shift.value || formData?.shift_id || "",
            department_id:loggedInUsersDepartmentId,
            ticket: ticketNumber,
            user_id: loggedInUserId,
            product_type_id: intProductTypes,
            record_type_id: intRecordTypes,
            review_id: intReviewRelease,
            task_type_id: intTaskTypes,
            revision_type_id: intRevisionTypes,
            priority_id: intPriorities,
            region_id: intRegions,
            reporter_id: intReporters,
            sla: slaAchieves,
            unit: unit,
            hour: hour,
            account: accountName,
            campaign: campaignName, 
            client_error: clientError,
            internal_error: internalError,
            notes: notes,
            created_by: loggedInUserId,
         });


        if(!confirmation){
            setViewData({
                "Date": entryDate, 
                "Ticket Number": ticketNumber, 
                "Unit": unit, 
                "Duration": hour, 
                "Client Error": clientError, 
                "Internal Error": internalError

            })
            return false;
        }

        console.log("Form Data being submitted:", { intRecordTypes, ...formData });
        
        try {
            setViewData(null);
            const response = await createTimeCard({
                ...formData,
                });

             if (response && response?.error) {
                 setError(response.error.data || "Failed to create time card.");
                return;
            }

            // const response = await fetch(`${API_URL}time-card`, {
            //     method: "POST",
            //     headers: {
            //         Authorization: `Bearer ${token}`,
            //         "Content-Type": "application/json",
            //     },
            //     body: JSON.stringify(formData),
            // });

            // if (!response.ok) {
            //     const data = await response.json();
            //     //setError(data.error || "Failed to create task.");
            //     alertMessage('error');
            //     return;
            // }

            // const data = await response.json();
            //setSuccessMessage("Hour added successfully!");

            // ✅ Success alert
            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: response?.message || 'Hour added successfully.',
            });

            setTimeout(() => {
                setVisible(false);  // Close the modal after success
                setSuccessMessage('');
            }, 2000); 

            // setUnit("");
            // setClientError("");
            // setInternalError("");
            // setNotes("");
            // setAccountName("");
            // setCampaignName("");

        } catch (error) {
            //setError(error.message);
            alertMessage('error');
        }
    };

    if (!isVisible) return null;

    return (
        <>
            
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-neutral-950 bg-opacity-80 flex justify-center items-center z-50 overflow-hidden">
                <div className="bg-neutral-50 rounded-xl overflow-hidden shadow-md w-full max-w-4xl relative">
                    <div className="flex justify-between items-center border-b border-neutral-200 bg-neutral-100 px-4 py-2">
                        <h4 className="text-base text-left font-medium text-slate-800">Add Work Hour </h4>
                        <button
                            className="text-3xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                        
                    </div>
                   
                            
                            {loading && <LoadingIcon />}
                    <form onSubmit={handleSubmit}>
                        <div className="flex flex-wrap gap-6 text-left p-6 overflow-y-auto max-h-[70vh] scrollbar-vertical">
                            {/* Date */}
                          
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="entryDate" className="block text-sm text-gray-400 pb-2">
                                    Date <span className="text-red-600">*</span>
                                </label>
                                <input
                                    id="entryDate"
                                    type="date"
                                    name="entryDate"
                                    value={entryDate}  // Bind entryDate state as the value of the input
                                    onChange={(e) => setEntryDate(e.target.value)}  // Update the state when a new date is selected
                                    min={formattedThreeDaysAgo}  // Set the min date to 3 days ago
                                    max={formattedCurrentDate}  // Set the max date to today
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>

                            {/* Assigned Department */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="department" className="block text-sm text-gray-400 pb-2">
                                    Department <span className="text-red-600">*</span>
                                </label>
                                <select
                                    id="department"
                                    name="department_id"
                                    className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                    value={loggedInUsersDepartmentId} // Set this to department ID to maintain the selected department
                                >
                                    <option value="" disabled>Select a department</option>
                                    {/* Show the logged-in user's department name */}
                                    {loggedInUsersDepartmentId && (
                                        <option value={loggedInUsersDepartmentId}>
                                            {loggedInUsersDepartment} {/* Display department name */}
                                        </option>
                                    )}
                                </select>
                            </div>
                            {/* Teams */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="team" className="block text-sm text-gray-400 pb-2">
                                    Team <span className="text-red-600">*</span>
                                </label>
                                <select
                                    id="team"
                                    name="team_id"
                                    className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    onChange={(e) => setSelectedTeamId(e.target.value)}
                                    value={selectedTeamId || ""}
                                    required
                                >
                                    <option value="">Select a team</option>
                                    {filteredTeams.map((team) => (
                                        <option key={team.id} value={team.id}>
                                            {team.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            {/* Task Ticket Numbers */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="ticket" className="block text-sm text-gray-400 pb-2">
                                    Ticket Number <span className="text-red-600">*</span>
                                </label>
                                {/* <select
                                    id="ticket"
                                    name="ticket"
                                    className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                    value={ticketNumber}
                                    onChange={(e) => setTicketNumber(e.target.value)}
                                >
                                    <option value="">Select a Ticket</option>
                                    {ticketNumbers.map((ticket, index) => (
                                        <option key={index} value={ticket}>
                                            {ticket}
                                        </option>
                                    ))}
                                </select>                               */}
                                <SearchFilterSelect
                                    id="ticket"
                                    options={ticketNumbers}
                                    value={ticketNumber}
                                    onChange={(e) => setTicketNumber(e.target.value)}
                                    placeholder="Search and select a ticket..."
                                />

                            </div>

                            {/* Shift */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="shift" className="block text-sm text-gray-400 pb-2">
                                    Shift <span className="text-red-600">*</span>
                                </label>
                                <select
                                    id="shift"
                                    name="shift"
                                    className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    {filteredSchedules.map((schedule) => (
                                        <option key={schedule.id} value={schedule.id}>
                                            {schedule.shift_name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            {/* Task Type */}   
                            {ticketNumber && (
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="taskType" className="block text-sm text-gray-400 pb-2">
                                        Task Type <span className="text-red-600">*</span>
                                    </label>
                                    <select
                                        id="taskType"
                                        name="taskType"
                                        className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                        value={taskTypeId || ''}
                                        onChange={(e) => setTaskTypeId(e.target.value)} // Set the selected task type here
                                    >
                                        <option value="">Select task type</option>
                                        {taskDetails
                                            .filter(task => task.ticket_number === ticketNumber) // Filter task details based on selected ticket number
                                            .map(task => 
                                                // Filter the taskTypes
                                                taskTypes.filter(taskType => taskType.id === task.task_type_id)
                                                    .map(taskType => (
                                                        <option key={taskType.id} value={taskType.id}>
                                                            {taskType.name}
                                                        </option>
                                                    ))
                                            )}
                                    </select>
                                </div>
                            )}

                            {/* Unit */}
                            {ticketNumber !== "N/A" && (
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="unit" className="block text-sm text-gray-400 pb-2">
                                        Unit <span className="text-red-600">*</span>
                                    </label>
                                    <input
                                        id="unit"
                                        type="number"
                                        name="unit"
                                        value={unit}
                                        onChange={handleUnitChange}
                                        placeholder="Add unit"
                                        required
                                        className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    />
                                    {/* Error Message */}
                                    {errorMessage && <p className="text-red-500 text-sm mt-1">{errorMessage}</p>}
                                </div>
                            )}

                            {/* Hour */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="hour" className="block text-sm font-medium text-gray-700 pb-4">
                                    Duration <span className="text-red-600">*</span>
                                </label>
                                <DatePicker
                                    selected={startDate}
                                    onChange={handleChange}
                                    showTimeSelect
                                    showTimeSelectOnly
                                    timeFormat="HH:mm"
                                    timeIntervals={5}
                                    dateFormat="HH:mm"
                                    value={hour} //stop passing the value on the above ussEffect with custom edit
                                    timeCaption="Time"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    placeholderText="Select time"
                                />
                            </div>
                            {/* <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="hour" className="block text-sm text-gray-400 pb-2">
                                    Duration <span className="text-red-600">*</span>
                                </label>
                                <DatePicker
                                    selected={startDate}
                                    onChange={(date) => setStartDate(date)}
                                    showTimeSelect
                                    showTimeSelectOnly
                                    timeFormat="HH:mm"
                                    timeIntervals={15}
                                    dateFormat="HH:mm"
                                    timeCaption="Time"
                                    placeholderText="Select time"
                                    className={`block w-full bg-stone-50 border rounded-md shadow-sm ${
                                        hourError ? 'border-red-500' : 'border-stone-200'
                                    }`}
                                />
                                {hourError && (
                                    <p className="mt-1 text-sm text-red-600">{hourError}</p>
                                )}
                            </div> */}
                            
                            {/* Client Error */}
                            {ticketNumber !== "N/A" && (
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="client_error" className="block text-sm text-gray-400 pb-2">
                                        Client Error 
                                    </label>
                                    <input
                                        id="client_error"
                                        type="number"
                                        value={clientError || ""}
                                        onChange={(e) => setClientError(e.target.value)}
                                        placeholder="Add client error"
                                        className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    />
                                </div>
                            )}

                            {/* Internal Error */}
                            {ticketNumber !== "N/A" && (
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="internal_error" className="block text-sm text-gray-400 pb-2">
                                        Internal Error 
                                    </label>
                                    <input
                                        id="internal_error"
                                        type="number"
                                        value={internalError || ""}
                                        onChange={(e) => setInternalError(e.target.value)}
                                        placeholder="Add internal error"
                                        className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    />
                                </div>
                            )}

                            {/* Account Name */}
                            {ticketNumber !== "N/A" && (

                                <div className="w-full flex flex-wrap gap-6">
                                    {/* Account Name */}
                                    <div className="mb-4 w-full sm:w-[47%]">
                                        <label htmlFor="account_name" className="block text-sm text-gray-400 pb-2">
                                            Account Name <span className="text-red-600">*</span>
                                        </label>
                                        <input
                                            id="account_name"
                                            type="text"
                                            value={accountName || ''}
                                            onChange={(e) => setAccountName(e.target.value)}
                                            required
                                            className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                            readOnly // Make it read-only since it's automatically filled based on ticket number
                                        />
                                    </div>

                                    {/* Campaign Name */}
                                    <div className="mb-4 w-full sm:w-[47%]">
                                        <label htmlFor="campaign_name" className="block text-sm text-gray-400 pb-2">
                                            Campaign Name <span className="text-red-600">*</span>
                                        </label>
                                        <input
                                            id="campaign_name"
                                            type="text"
                                            value={campaignName || ''}
                                            onChange={(e) => setCampaignName(e.target.value)}
                                            required
                                            className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                            readOnly // Make it read-only since it's automatically filled based on ticket number
                                        />
                                    </div>

                                    {/* Product Type */}
                                    <div className="mb-4 w-full sm:w-[47%]">
                                        <label htmlFor="productType" className="block text-sm text-gray-400 pb-2">
                                            Product Type <span className="text-red-600">*</span>
                                        </label>
                                        <select
                                            id="productType"
                                            name="productType"
                                            className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                            value={productTypeId || ''}
                                            onChange={(e) => setProductTypeId(e.target.value)} // Set the selected product type here
                                            required
                                        >
                                            <option value="">Select product type</option>
                                            {taskDetails
                                                .filter(task => task.ticket_number === ticketNumber) // Filter task details based on selected ticket number
                                                .map(task => 
                                                    // Filter the productTypes based on the relevant task's product_type_id
                                                    productTypes.filter(productType => productType.id === task.product_type_id)
                                                        .map(productType => (
                                                            <option key={productType.id} value={productType.id}>
                                                                {productType.name}
                                                            </option>
                                                        ))
                                                )}
                                        </select>
                                    </div>  

                                    {/* Revision Type */}
                                    <div className="mb-4 w-full sm:w-[47%]">
                                        <label htmlFor="revisionType" className="block text-sm text-gray-400 pb-2">
                                            Revision Type
                                        </label>
                                        <select
                                            id="revisionType"
                                            name="revisionType"
                                            className={`block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 ${revisionTypeDisabled ? 'bg-gray-100' : ''}`} // 4. Added Tailwind class for disabling style
                                            value={revisionTypeId || ''}
                                            onChange={(e) => setRevisionTypeId(e.target.value)} // Set the selected revision type here
                                            disabled={revisionTypeDisabled} // 5. Disabled if not a "Revision" task type
                                        >
                                            <option value="">Select revision type</option>
                                            {taskDetails
                                                .filter(task => task.ticket_number === ticketNumber) // Filter task details based on selected ticket number
                                                .map(task =>
                                                    // Filter the revisionTypes
                                                    revisionTypes.filter(revisionType => revisionType.id === task.revision_type_id)
                                                        .map(revisionType => (
                                                            <option key={revisionType.id} value={revisionType.id}>
                                                                {revisionType.name}
                                                            </option>
                                                        ))
                                                )}
                                        </select>
                                    </div>

                                    {/* Priority */}
                                    <div className="mb-4 w-full sm:w-[47%]">
                                        <label htmlFor="priority" className="block text-sm text-gray-400 pb-2">
                                            Priority <span className="text-red-600">*</span>
                                        </label>
                                        <select
                                            id="priority"
                                            name="priority"
                                            className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                            value={priorityId || ''}
                                            onChange={(e) => setPriorityId(e.target.value)} // Set the selected priority here
                                            required
                                        >
                                            <option value="">Select priority</option>
                                            {taskDetails
                                                .filter(task => task.ticket_number === ticketNumber) // Filter task details based on selected ticket number
                                                .map(task =>
                                                    // Filter the priorities based on the relevant task's priority_id
                                                    priorities.filter(priority => priority.id === task.priority_id)
                                                        .map(priority => (
                                                            <option key={priority.id} value={priority.id}>
                                                                {priority.name}
                                                            </option>
                                                        ))
                                                )}
                                        </select>
                                    </div>

                                    {/* Region */}
                                    <div className="mb-4 w-full sm:w-[47%]">
                                        <label htmlFor="region" className="block text-sm text-gray-400 pb-2">
                                            Region <span className="text-red-600">*</span>
                                        </label>
                                        <select
                                            id="region"
                                            name="region"
                                            className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                            value={regionId || ''}
                                            onChange={(e) => setRegionId(e.target.value)} // Set the selected region here
                                            required
                                        >
                                            <option value="">Select region</option>
                                            {taskDetails
                                                .filter(task => task.ticket_number === ticketNumber) // Filter task details based on selected ticket number
                                                .map(task =>
                                                    // Filter the regions based on the relevant task's region_id
                                                    regions.filter(region => region.id === task.region_id)
                                                        .map(region => (
                                                            <option key={region.id} value={region.id}>
                                                                {region.name}
                                                            </option>
                                                        ))
                                                )}
                                        </select>
                                    </div>

                                    {/* Reporter */}
                                    <div className="mb-4 w-full sm:w-[47%]">
                                        <label htmlFor="reporter" className="block text-sm text-gray-400 pb-2">
                                            Reporter <span className="text-red-600">*</span>
                                        </label>
                                        <select
                                            id="reporter"
                                            name="reporter"
                                            className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                            value={reporterId || ''}
                                            onChange={(e) => setReporterId(e.target.value)} // Set the selected reporter here
                                            required
                                        >
                                            <option value="">Select reporter</option>
                                            {taskDetails
                                                .filter(task => task.ticket_number === ticketNumber) // Filter task details based on selected ticket number
                                                .map(task =>
                                                    // Filter the reporters based on the relevant task's reporter_id
                                                    reporters.filter(reporter => reporter.id === task.reporter_id)
                                                        .map(reporter => (
                                                            <option key={reporter.id} value={reporter.id}>
                                                                {reporter.name}
                                                            </option>
                                                        ))
                                                )}
                                        </select>
                                    </div>

                                    {/* Review & Release */}
                                    <div className="mb-4 w-full sm:w-[47%]">
                                        <label htmlFor="reviewRelease" className="block text-sm text-gray-400 pb-2">
                                            Review & Release
                                        </label>
                                        <select
                                            id="reviewRelease"
                                            value={selectedReviewRelease !== null ? selectedReviewRelease : ''}
                                            onChange={handleReviewReleaseChange}
                                            className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                        >
                                            <option value="">Select a Review & Release</option>
                                            {reviewRelease.length === 0 ? (
                                                <option disabled>No review & release available</option>
                                            ) : (
                                                reviewRelease.map((review) => (
                                                    <option key={review.id} value={review.id}>
                                                        {review.name}
                                                    </option>
                                                ))
                                            )}
                                        </select>
                                    </div>
                                </div>
                            )}

                            {/* SLA Achieves */}
                            {ticketNumber !== "N/A" && (
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="slaAchieves" className="block text-sm text-gray-400 pb-2">
                                        SLA Achieved <span className="text-red-600">*</span>
                                    </label>
                                    <select
                                        id="slaAchieves"
                                        name="slaAchieves"
                                        value={slaAchieves} // This should be your state variable to hold the selected value
                                        onChange={(e) => setSlaAchieves(e.target.value)} // This updates the state when the selection changes
                                        
                                        className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    >
                                        <option value="">Select</option> {/* Optional, if you want to allow no selection */}
                                        <option value="Yes" selected>Yes</option>
                                        <option value="No">No</option>
                                    </select>
                                </div>
                            )}
                            
                            {/* Record Type */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="recordType" className="block text-sm text-gray-400 pb-2">
                                    Record Type <span className="text-red-600">*</span>
                                </label>
                                <select
                                    id="recordType"
                                    value={selectedRecordType !== null ? selectedRecordType : ''} // Ensure the value is either the selected type or an empty string
                                    onChange={handleSelectChange}
                                    className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select a Record Type</option>
                                    {recordTypes.length === 0 ? (
                                        <option disabled>No record types available</option>
                                    ) : (
                                        recordTypes.map((recordType) => (
                                            <option key={recordType.id} value={recordType.id}>
                                                {recordType.name}
                                            </option>
                                        ))
                                    )}
                                </select>
                            </div>

                            {/* Notes */}
                            <div className="mb-4 w-full sm:w-[100%]">
                                <label htmlFor="notes" className="block text-sm text-gray-400 pb-2">
                                    Notes
                                </label>
                                <textarea
                                    id="notes"
                                    name="notes"
                                    value={notes}
                                    onChange={(e) => setNotes(e.target.value)}
                                    placeholder="Note/Comment"
                                    className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>

                        </div>

                        <div>
                            {error && <p className="text-red-500 text-sm">{error}</p>}
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </div>
                        
                        <div className="flex justify-center p-6">
                            <button type="submit" className="w-full bg-primary hover:bg-secondary text-white py-3 rounded-xl flex flex-row gap-4 items-center justify-center m-auto">Add Hour</button>
                        </div>
                    </form>
                </div>
            </div>
            {viewData && (
                            <ViewSubmitFormData
                                item={viewData}
                                setViewData={setViewData}
                                setConfirmation={setConfirmation}
                                handleSubmit={handleSubmit}
                            />
                        )}
            
        </>
    );
};

export default AddTimeCard;
