import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditSchedule from './EditSchedule';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

// Helper function to convert 24-hour time to 12-hour AM/PM format
const formatTo12HourAMPM = (time24hr) => {
    const [hour, minute] = time24hr.split(':');
    let hour12 = parseInt(hour, 10);
    const ampm = hour12 >= 12 ? 'PM' : 'AM';
    hour12 = hour12 % 12;
    hour12 = hour12 ? hour12 : 12; // the hour '0' should be '12'
    return `${hour12}:${minute} ${ampm}`;
};

const ScheduleList = () => {
    const [schedules, setSchedules] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [loading, setLoading] = useState(true); // Initially loading is true
    const [selectedScheduleId, setSelectedScheduleId] = useState(null);
    const [error, setError] = useState(null);

    // Update column names for Schedule table
    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Team", key: "team" },
        { label: "Shift Name", key: "shift_name" },
        { label: "Shift Start", key: "shift_start" },
        { label: "Shift End", key: "shift_end" },
    ];

    useEffect(() => {
        const fetchSchedules = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}schedules`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();

                setSchedules(data.schedules.map(schedule => ({
                    id: schedule.id,
                    team: schedule.teams.length > 0 ? schedule.teams[0].name : '',
                    shift_name: schedule.shift_name,
                    shift_start: formatTo12HourAMPM(schedule.shift_start), // Format to 12-hour AM/PM
                    shift_end: formatTo12HourAMPM(schedule.shift_end),     // Format to 12-hour AM/PM
                })));
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchSchedules();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}schedules/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete schedule: ' + response.statusText);
            }

            // Update the schedule list after deletion
            setSchedules(prevSchedules => prevSchedules.filter(schedule => schedule.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedScheduleId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    // Show message when no schedules are available
    if (schedules.length === 0) {
        return <div className="text-gray-500">No data available</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={schedules}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedScheduleId}
            />
            {modalVisible && (
                <EditSchedule
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    scheduleId={selectedScheduleId}
                />
            )}
        </div>
    );
};

export default ScheduleList;
