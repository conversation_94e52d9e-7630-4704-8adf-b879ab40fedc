import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const AddNoticeBoardCategory = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [categories, setCategories] = useState([]);
    const [categoryName, setCategoryName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    useEffect(() => {
        const fetchCategories = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}notice-board-category`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                setCategories(data.categories);
            } catch (error) {
                setError(error.message);
            }
        };

        fetchCategories();
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedCategoryName = categoryName.trim();
    
        const categoryExists = categories.some((category) => {
            return category.name.toLowerCase().trim() === trimmedCategoryName.toLowerCase();
        });
    
        if (categoryExists) {
            setError('Category already exists. Please add a different category.');
            setTimeout(() => setError(''), 3000);
            return;
        }
    
        setError('');
    
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }
    
            let firstName = localStorage.getItem('fname');
            let lastName = localStorage.getItem('lname');
    
            // Check if first name or last name is missing, and provide a fallback
            if (!firstName || !lastName) {
                console.warn("User first and/or last name is missing in localStorage. Setting default values.");
                firstName = firstName || "Unknown";
                lastName = lastName || "User";
            }
    
            const fullName = `${firstName} ${lastName}`;
    
            const response = await fetch(`${API_URL}notice-board-category`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedCategoryName,
                    created_by: fullName,
                    updated_by: fullName,
                }),
            });
    
            if (!response.ok) {
                throw new Error('Failed to save category: ' + response.statusText);
            }
    
            const result = await response.json();
            setSuccessMessage(`Category "${result.name || trimmedCategoryName}" added successfully!`);
            setCategoryName('');
    
            // Fetch the updated categories list
            const newCategoriesResponse = await fetch(`${API_URL}notice-board-category`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!newCategoriesResponse.ok) {
                throw new Error('Failed to fetch categories: ' + newCategoriesResponse.statusText);
            }
    
            const newCategoriesData = await newCategoriesResponse.json();
            setCategories(newCategoriesData.categories);
    
        } catch (error) {
            setError(error.message);
        }
    };
    

    const isModalOpen = location.pathname === '/add-notice-category';

    const handleClose = () => {
        navigate('/settings');
    };

    return (
        <>
            {isModalOpen && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative">
                        <button onClick={handleClose} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                            &times;
                        </button>
                        <h4 className="text-xl font-semibold mb-4 py-4">Add New Notice Board Category</h4>
                        <form onSubmit={handleSubmit}>
                            <div className="mb-4">
                                <label htmlFor="categoryName" className="block text-sm font-medium text-gray-700 pb-4">
                                    Category Name
                                </label>
                                <input
                                    type="text"
                                    id="categoryName"
                                    value={categoryName}
                                    onChange={(e) => setCategoryName(e.target.value)}
                                    required
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                                {error && <p className="text-red-500 text-sm">{error}</p>}
                            </div>
                            <div className='py-4'>
                                <button
                                    type="submit"
                                    className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                                >
                                    Add Category
                                </button>
                            </div>
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default AddNoticeBoardCategory;
