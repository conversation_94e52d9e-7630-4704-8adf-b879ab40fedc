{"version": 3, "file": "static/js/104.60c98b75.chunk.js", "mappings": "qQAGA,MAAMA,EAAUC,kCA+IhB,EA7IyBC,IAA6C,IAA5C,UAAEC,EAAS,WAAEC,EAAU,YAAEC,GAAaH,EAC5D,MAAOI,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAS,KAClDC,EAAOC,IAAYF,EAAAA,EAAAA,UAAS,KAC5BG,EAAgBC,IAAqBJ,EAAAA,EAAAA,UAAS,KAC9CK,EAAcC,IAAmBN,EAAAA,EAAAA,UAAS,OAGjDO,EAAAA,EAAAA,YAAU,KACN,MAAMC,EAASC,aAAaC,QAAQ,WAChCF,GACAF,EAAgBE,EACpB,GACD,KAEHD,EAAAA,EAAAA,YAAU,KACoBI,WACtB,GAAId,EAAa,CACb,MAAMe,EAAQH,aAAaC,QAAQ,SACnC,IACI,MAAMG,QAAiBC,MAAM,GAAGtB,oBAA0BK,IAAe,CACrEkB,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUJ,IAC3B,eAAgB,sBAIxB,IAAKC,EAASI,GACV,MAAM,IAAIC,MAAM,kCAAoCL,EAASM,YAGjE,MAAMC,QAAaP,EAASQ,OAC5BtB,EAAoBqB,EAAKE,cAAcC,KAC3C,CAAE,MAAOtB,GACLC,EAASD,EAAMuB,QACnB,CACJ,GAGJC,EAAmB,GACpB,CAAC5B,IAuDJ,OAAKF,GAGD+B,EAAAA,EAAAA,KAAA,OACIC,UAAU,sGACVC,QAASA,IAAMhC,GAAW,GAAOiC,UAEjCC,EAAAA,EAAAA,MAAA,OACIH,UAAU,6DACVC,QAAUG,GAAMA,EAAEC,kBAAmBH,SAAA,EAErCC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,yCAAwCE,SAAA,EACnDH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBE,SAAC,0BACtCH,EAAAA,EAAAA,KAAA,UACIC,UAAU,oCACVC,QAASA,IAAMhC,GAAW,GAAOiC,SACpC,YAIJ5B,IAASyB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcE,SAAE5B,IACxCE,IAAkBuB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBE,SAAE1B,KACpD2B,EAAAA,EAAAA,MAAA,QAAMG,SA3EGtB,UACjBuB,EAAMC,iBACN,MAAMvB,EAAQH,aAAaC,QAAQ,SAE7B0B,EAAY/B,EAElB,GAAK+B,EAKL,GAAKxB,EAKL,IACI,MAAMC,QAAiBC,MAAM,GAAGtB,oBAA0BK,IAAe,CACrEkB,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUJ,IAC3B,eAAgB,oBAEpByB,KAAMC,KAAKC,UAAU,CACjBhB,KAAMzB,EAAiB0C,OACvBC,WAAYL,MAIpB,IAAKvB,EAASI,GACV,MAAM,IAAIC,MAAM,mCAAqCL,EAASM,YAGlE,MAAMuB,QAAe7B,EAASQ,QAG9BsB,EAAAA,EAAAA,IAAa,CACTC,KAAM,UACNC,MAAO,WACPC,MAAY,OAANJ,QAAM,IAANA,OAAM,EAANA,EAAQlB,UAAW,6CAI7BuB,YAAW,KACPnD,GAAW,GACXQ,EAAkB,GAAG,GACtB,IAEP,CAAE,MAAOH,IACL0C,EAAAA,EAAAA,IAAa,QACjB,MAtCIzC,EAAS,yCALTA,EAAS,yBA2Cb,EAyBqC2B,SAAA,EACzBC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,OAAME,SAAA,EACjBH,EAAAA,EAAAA,KAAA,SAAOsB,QAAQ,OAAOrB,UAAU,aAAYE,SAAC,wBAC7CH,EAAAA,EAAAA,KAAA,SACIuB,KAAK,OACLC,GAAG,OACHC,MAAOrD,EACPsD,SAAWrB,GAAMhC,EAAoBgC,EAAEsB,OAAOF,OAC9CxB,UAAU,4BACV2B,UAAQ,QAGhB5B,EAAAA,EAAAA,KAAA,UACIuB,KAAK,SACLtB,UAAU,gEAA+DE,SAC5E,iCArCM,IA0Cb,E,qCCrHd,MAAM0B,EAAc,qBAygBpB,EAtgB4BC,KAE1B,MAAOC,EAAeC,IAAoB1D,EAAAA,EAAAA,UAAS,CAAC,IAC7C2D,EAAuBC,IAA4B5D,EAAAA,EAAAA,UAAS,CAAC,IAC7D6D,EAAkBC,IAAuB9D,EAAAA,EAAAA,UAAS,KAClD+D,EAAaC,IAAkBhE,EAAAA,EAAAA,UAAS,KACxCiE,EAAcC,IAAmBlE,EAAAA,EAAAA,WAAS,IAC1CmE,EAAqBC,IAA0BpE,EAAAA,EAAAA,WAAS,IACxDH,EAAawE,IAAkBrE,EAAAA,EAAAA,UAAS,OACxCC,EAAOC,IAAYF,EAAAA,EAAAA,UAAS,OAC5BsE,EAAUC,IAAevE,EAAAA,EAAAA,UAAS,OAElCwE,EAAiBC,KADPC,EAAAA,EAAAA,OAC6B1E,EAAAA,EAAAA,WAAS,KAIhD2E,EAAYC,IAAiB5E,EAAAA,EAAAA,UAAS,eACtC6E,EAAeC,IAAoB9E,EAAAA,EAAAA,UAAS,SAC5C+E,EAASC,IAAchF,EAAAA,EAAAA,UAAS,OAChCiF,EAAaC,IAAkBlF,EAAAA,EAAAA,UAAS,IAGvCoB,KAAM+D,EAAS,WAAEC,EAAYnF,MAAOoF,IAAeC,EAAAA,EAAAA,KAA4B,CAAEC,QAASZ,EAAYa,MAAOX,EAAeY,KAAMR,EAAaS,SAAUX,EAASY,MAAO5B,KAE1K6B,GAAwBxE,KAAMyE,EAAW5F,MAAO6F,KAAoBC,EAAAA,EAAAA,QAEpEC,IAAsBC,EAAAA,EAAAA,OAGvBC,EAAoBC,IACxB,IAAIC,EAAIC,OAAOC,QAAQH,GAAiBI,QAAO,CAACC,EAAG9G,KAAoB,IAAjB+G,EAAKtD,GAAMzD,EAC/D,GAAqB,kBAAVyD,EACT,OAAOqD,EAAM,IAAIC,KAAOtD,IAE1B,GAAIuD,MAAMC,QAAQxD,GAAQ,CAExB,OAAOqD,EAAM,IAAIC,KADJtD,EAAMyD,KAAKC,GAAMA,EAAE1D,QAAO2D,KAAK,MAE9C,CACA,OAAON,CAAG,GACT,IAEHxC,EAAeoC,EAAE,EAGbW,EAAc3F,KAEE4F,EAAAA,EAAAA,IAAW5F,EADV,CAAC,KAAM,OAAQ,aAAc,aAAc,aAAc,UAAW,aAAc,UAAW,aAAc,eAEhImD,EAAY,MACZL,GAAgB,EAAK,EAGjB+C,EAAc/D,IAClBqB,EAAY,MACZF,EAAenB,GACfgB,GAAgB,EAAK,EAGjBgD,GAAgBhE,KACpBiE,EAAAA,EAAAA,IAAkB,CAACC,UAAWA,KAE1BpB,EAAmB9C,GACnBqB,EAAY,KAAK,GAChB,EAIP,IAAI8C,GAAe,EAEnB,MAAM,gBAAEC,KAAoBC,EAAAA,EAAAA,MAGrBC,GAASC,KAAczH,EAAAA,EAAAA,WAAS,IAAM,CAC3C,CACIkD,GAAImE,KACN9F,KAAM,SACNmG,MAAO,QACP/F,UAAW,aACXgG,KAAOC,IACL9F,EAAAA,EAAAA,MAAA,OAAKH,UAAU,sCAAqCE,SAAA,EAElDH,EAAAA,EAAAA,KAAA,UACEC,UAAU,wLACVC,QAASA,IAAM2C,EAAYqD,GAAM/F,UAEjCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,kBAItC,OAAfyF,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAChBnG,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAMqF,EAAWW,EAAK1E,IAAIrB,UAEnCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,mBAKxC,OAAfyF,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAChBnG,EAAAA,EAAAA,KAAA,UACEC,UAAU,sLACVC,QAASA,IAAMmF,EAAWa,GAAM/F,UAEhCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,oBAKxC,OAAfyF,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAChBnG,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAMsF,GAAaU,EAAK1E,IAAIrB,UAErCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,iBAM9D,CACIqB,GAAImE,KACN9F,KAAM,OACNuG,SAAUA,CAACC,EAAKC,KAAW/C,EAAc,GAAKF,EAAUiD,EAAQ,EAChEN,MAAO,OACPO,MAAM,GAER,CACI/E,GAAImE,KACN9F,KAAM,uBACN2G,SAAU,OACVJ,SAAWC,GAAQA,EAAIxG,MAAQ,GAC/B0G,MAAM,EACNE,UAAU,EACVC,YAAY,GAEd,CACIlF,GAAImE,KACJ9F,KAAM,aACNuG,SAAWC,IAAG,IAAAM,EAAAC,EAAA,MAAK,IAAc,QAAXD,EAAAN,EAAIQ,eAAO,IAAAF,OAAA,EAAXA,EAAaG,QAAS,OAAiB,QAAXF,EAAAP,EAAIQ,eAAO,IAAAD,OAAA,EAAXA,EAAaG,QAAS,IAAI,EAC5EP,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEhB,CACElF,GAAImE,KACJ9F,KAAM,eACNuG,SAAWC,IAAQW,EAAAA,EAAAA,IAAkBX,EAAIY,YACzCT,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEd,CACIlF,GAAImE,KACJ9F,KAAM,eACNuG,SAAWC,IAAQa,EAAAA,EAAAA,IAAmBb,EAAIY,YAC1CT,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEd,CACElF,GAAImE,KACJ9F,KAAM,aACNuG,SAAWC,IAAG,IAAAc,EAAAC,EAAA,MAAK,IAAc,QAAXD,EAAAd,EAAIgB,eAAO,IAAAF,OAAA,EAAXA,EAAaL,QAAS,OAAiB,QAAXM,EAAAf,EAAIgB,eAAO,IAAAD,OAAA,EAAXA,EAAaL,QAAS,IAAI,EAC5EP,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEhB,CACElF,GAAImE,KACJ9F,KAAM,eACNuG,SAAWC,IAAQW,EAAAA,EAAAA,IAAkBX,EAAIiB,YACzCd,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEd,CACIlF,GAAImE,KACJ9F,KAAM,eACNuG,SAAWC,IAAQa,EAAAA,EAAAA,IAAmBb,EAAIiB,YAC1Cd,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,OAIlB7H,EAAAA,EAAAA,YAAU,KAERkH,IAAYwB,GAAgB,IACvBA,EAAYrC,KAAKsC,GACD,WAAbA,EAAI3H,KAEC,IACF2H,EACHvB,KAAOC,IACL9F,EAAAA,EAAAA,MAAA,OAAKH,UAAU,sCAAqCE,SAAA,EAClDH,EAAAA,EAAAA,KAAA,UACEC,UAAU,wLACVC,QAASA,IAAM2C,EAAYqD,GAAM/F,UAEjCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,kBAEtC,OAAfyF,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAChBnG,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAMqF,EAAWW,EAAK1E,IAAIrB,UAEnCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,mBAIxC,OAAfyF,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAClBnG,EAAAA,EAAAA,KAAA,UACEC,UAAU,sLACVC,QAASA,IAAMmF,EAAWa,GAAM/F,UAEhCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,oBAItC,OAAfyF,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAClBnG,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAMsF,GAAaU,EAAK1E,IAAIrB,UAErCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,iBAOvDqH,MAET,GACD,CAAC5B,KAKJ,MAkBM6B,IAAWC,EAAAA,EAAAA,MAqDXC,IAA8BC,EAAAA,EAAAA,cAClC3I,iBAKM,IAJJ4I,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACdvG,EAAIuG,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,QACPG,EAASH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACZI,EAASJ,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,SAGRK,EAAeN,EAAWrB,UAAY,QAE1C,IACEpE,EAAoB+F,GACpBzF,GAAuB,GAEvB,IAAIyB,EAAY,GAEhB,MAAMhF,QAAiB+E,EAAqB,CAAE3C,KAAMA,EAAKT,OAAQsH,OAAQD,EAAarH,OAAQM,KAAM6G,EAAUnH,SAM9G,GAJI3B,EAASO,OACXyE,EAAYhF,EAASO,MAGnByE,EAAU4D,OAAQ,CAEpB,GAAkB,eAAdG,EAMF,OALAlG,GAAkBqG,IAAI,IACjBA,EACH,CAACF,GAAehE,MAGXA,EAGT,MAAMmE,EAAmBnE,EACtBe,KAAKgB,IACJ,GAAG2B,EAAWzB,SAAS,CACrB,IAAImC,EAAQV,EAAWzB,SAASF,GAEhC,OAAGqC,GACGrC,EAAKsC,OAAStC,EAAKsC,MAAQ,IAC7BD,GAAS,KAAKrC,EAAKsC,UAGd,CAAED,QAAO9G,MAAOyE,EAAKiC,KAGzB,IACP,KACCM,OAAOC,SAOZ,OALA1G,GAAkBqG,IAAI,IACjBA,EACH,CAACR,EAAWrG,KAAKmH,EAAAA,EAAAA,IAAYL,OAGxBA,CACT,CACF,CAAE,MAAO/J,GACPC,EAASD,EAAMuB,QACjB,CAAC,QACC4C,GAAuB,EACzB,CACF,GACA,IAGF,OACE1C,EAAAA,EAAAA,KAAA,WAASC,UAAU,gEAA+DE,UAChFC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,gBAAeE,SAAA,EAE5BC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,iGAAgGE,SAAA,EAC7GH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BE,UAC3CH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sBAAqBE,SAAE0B,OAEvCzB,EAAAA,EAAAA,MAAA,OAAKH,UAAU,0CAAyCE,SAAA,EAEtDH,EAAAA,EAAAA,KAAC4I,EAAAA,GAAa,CAAC9C,QAASA,GAASC,WAAYA,MAG1CrC,GAAcD,GAAaoF,SAASpF,EAAU+E,OAAS,IACxDxI,EAAAA,EAAAA,KAAA8I,EAAAA,SAAA,CAAA3I,UACEC,EAAAA,EAAAA,MAAA,UACEH,UAAU,oZACVC,QAvIMjB,UACpB,IAEE,MAAM+B,QAAeyG,GACnBsB,EAAAA,IAAoBC,UAAUC,oBAAoBC,SAAS,CACzDrF,QAASZ,EACTa,MAAOX,EACPY,KAAMR,EACNS,UAAmB,OAATP,QAAS,IAATA,OAAS,EAATA,EAAW+E,QAAS,GAC9BvE,MAAO5B,KAET8G,SAEF,GAAW,OAANnI,QAAM,IAANA,IAAAA,EAAQwH,OAASxH,EAAOwH,MAAQ,EACnC,OAAO,EAGT,IAAIY,EAAK,EAET,IAAIC,EAAcrI,EAAOtB,KAAKwF,KAAKgB,IACjC,GAAIJ,GAAQiC,OAAQ,CAClB,IAAIuB,EAAM,CAAC,EAMX,OALAxD,GAAQyD,SAASnB,KACVA,EAAO7B,MAAQ6B,EAAOhC,WACzBkD,EAAIlB,EAAOvI,MAAwB,SAAhBuI,EAAOvI,KAAkBuJ,IAAOhB,EAAOhC,SAASF,IAAS,GAC9E,IAEKoD,CACT,KAIF,MAAME,EAAYC,EAAAA,GAAWC,cAAcL,GACrCM,EAAWF,EAAAA,GAAWG,WAC5BH,EAAAA,GAAWI,kBAAkBF,EAAUH,EAAW,UAGlD,MAAMM,EAAcL,EAAAA,GAAWE,EAAU,CACvCI,SAAU,OACVxI,KAAM,UAEFyI,EAAO,IAAIC,KAAK,CAACH,GAAc,CAAEvI,KAAM,8BAC7C2I,EAAAA,EAAAA,QAAOF,EAAM,GAAGnI,EAAYsI,QAAQ,KAAK,QAAQd,EAAYtB,cAC/D,CAAE,MAAOxJ,GACP6L,QAAQ7L,MAAM,4BAA6BA,EAC7C,GA0FqC4B,SAAA,CAEtBuD,IACC1D,EAAAA,EAAAA,KAAA8I,EAAAA,SAAA,CAAA3I,UACEH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sDAAqDE,SAAC,yBAKxEuD,IACA1D,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yCAAwCE,SAAC,gBAGzD,oBACgBsD,EAAU+E,MAAM,SAKvC5C,GAAgBO,iBACfnG,EAAAA,EAAAA,KAAA,UACEC,UAAU,gYAEVC,QAASA,IAAM6C,GAAmB,GAAM5C,SACzC,mBAQPH,EAAAA,EAAAA,KAACqK,EAAAA,GAAY,CACTvE,QAASA,GACT7D,sBAAuBA,EACvBC,yBAA0BA,EAC1ByF,4BAA6BA,GAC7B5F,cAAeA,EACfU,oBAAqBA,EACrBN,iBAAkBA,EAClBmI,UAlMQA,KAChB,GAAI3F,OAAO4F,KAAKtI,GAAuB8F,OAAQ,CAC7C,IAAIyC,EAAS,CAAC,EACd7F,OAAO4F,KAAKtI,GAAuBiD,KAAKH,IACI,kBAA/B9C,EAAsB8C,GAC/ByF,EAAOzF,GAAO,GAEdyF,EAAOzF,GAAO,EAChB,IAEF7C,EAAyB,IAAKsI,IAC9BhG,EAAiB,IAAKgG,GACxB,CACAhH,EAAe,EAAE,EAsLTA,eAAgBA,EAChBgB,iBAAkBA,IAIrBb,IAAc3D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcE,SAAE5B,IAE7CmF,IAAc1D,EAAAA,EAAAA,KAACyK,EAAAA,EAAO,KAKvBzK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mDAAkDE,UAC/DH,EAAAA,EAAAA,KAAC0K,EAAAA,GAAS,CACR5E,QAASA,GACTpG,MAAe,OAAT+D,QAAS,IAATA,OAAS,EAATA,EAAW/D,OAAQ,GACzBO,UAAU,8BACV0K,aAAW,EAEXC,kBAAgB,EAChBC,YAAU,EACVC,YAAU,EACVC,kBAAgB,EAChBC,kBAAmB3H,EACnB4H,qBAA8B,OAATxH,QAAS,IAATA,OAAS,EAATA,EAAW+E,QAAS,EACzC0C,aAAenH,IACTA,IAASR,GACXC,EAAeO,EACjB,EAEFoH,oBAAsBC,IACjBA,IAAe/H,IAChBC,EAAW8H,GACX5H,EAAe,GACjB,EAEF6H,2BAA4B,CAC1BC,mBAAmB,EACnBC,sBAAuB,OAEzBC,YAAU,EACVC,OAAQ,SAACrD,GAAkC,IAA1BjF,EAAa2E,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,OAC1BnD,OAAO4F,KAAKnC,GAAQL,SACrB7E,EAAckF,EAAO5B,UAAY4B,EAAOvI,MAAQ,cAChDuD,EAAiBD,GAAiB,QAEtC,MAKHL,IACG9C,EAAAA,EAAAA,KAAC0L,EAAAA,EAAe,CACZzN,UAAW6E,EACX5E,WAAY6E,IAKnBR,IACCvC,EAAAA,EAAAA,KAAC2L,EAAgB,CACf1N,UAAWsE,EACXrE,WAAYsE,EACZrE,YAAaA,IAIhByE,IAEC5C,EAAAA,EAAAA,KAAC4L,EAAAA,GAAS,CAAC1F,KAAMtD,EAAUC,YAAaA,EAAaiD,QAASA,GAASP,WAAYA,EAAYC,aAAcA,SAIzG,EClhBd,EARqBqG,KAEjB7L,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCE,UACrDH,EAAAA,EAAAA,KAAC8B,EAAmB,K", "sources": ["pages/member-status/EditMemberStatus.jsx", "pages/member-status/MemberStatusDataList.jsx", "dashboard/settings/MemberStatus.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { alertMessage } from '../../common/coreui';\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst EditMemberStatus = ({ isVisible, setVisible, dataItemsId }) => {\r\n    const [memberStatusName, setMemberStatusName] = useState('');\r\n    const [error, setError] = useState('');\r\n    const [successMessage, setSuccessMessage] = useState('');\r\n    const [loggedInUser, setLoggedInUser] = useState(null);\r\n\r\n    // Fetch logged-in user data (user_id)\r\n    useEffect(() => {\r\n        const userId = localStorage.getItem('user_id');\r\n        if (userId) {\r\n            setLoggedInUser(userId);\r\n        }\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        const fetchMemberStatus = async () => {\r\n            if (dataItemsId) {\r\n                const token = localStorage.getItem('token');\r\n                try {\r\n                    const response = await fetch(`${API_URL}member_statuses/${dataItemsId}`, {\r\n                        method: 'GET',\r\n                        headers: {\r\n                            'Authorization': `Bearer ${token}`,\r\n                            'Content-Type': 'application/json',\r\n                        },\r\n                    });\r\n\r\n                    if (!response.ok) {\r\n                        throw new Error('Failed to fetch Member Status: ' + response.statusText);\r\n                    }\r\n\r\n                    const data = await response.json();\r\n                    setMemberStatusName(data.member_status.name); // Assuming response contains member_status.name\r\n                } catch (error) {\r\n                    setError(error.message);\r\n                }\r\n            }\r\n        };\r\n\r\n        fetchMemberStatus();\r\n    }, [dataItemsId]); // Re-fetch when dataItemsId changes\r\n\r\n    const handleSubmit = async (event) => {\r\n        event.preventDefault(); // Prevent default form submission behavior\r\n        const token = localStorage.getItem('token');\r\n\r\n        const updatedBy = loggedInUser;\r\n\r\n        if (!updatedBy) {\r\n            setError('User is not logged in.');\r\n            return;\r\n        }\r\n        \r\n        if (!token) {\r\n            setError('Authentication token is missing.');\r\n            return; // Exit if token is not available\r\n        }\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}member_statuses/${dataItemsId}`, {\r\n                method: 'PUT',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify({\r\n                    name: memberStatusName.trim(), // The updated Member Status name\r\n                    updated_by: updatedBy,\r\n                }),\r\n            });\r\n    \r\n            if (!response.ok) {\r\n                throw new Error('Failed to update Member Status: ' + response.statusText);\r\n            }\r\n    \r\n            const result = await response.json();\r\n            //setSuccessMessage(`Member status \"${result.name}\" updated successfully!`);\r\n\r\n            alertMessage({\r\n                icon: 'success',\r\n                title: 'Success!',\r\n                text: result?.message || 'Team member status updated successfully.',\r\n            });\r\n    \r\n            // Close the modal after a short delay\r\n            setTimeout(() => {\r\n                setVisible(false);\r\n                setSuccessMessage(''); // Clear the success message\r\n            }, 1000);\r\n            \r\n        } catch (error) {\r\n            alertMessage('error');\r\n        }\r\n    };\r\n\r\n    if (!isVisible) return null; // Don't render the modal if not visible\r\n\r\n    return (\r\n        <div\r\n            className=\"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50\"\r\n            onClick={() => setVisible(false)}\r\n        >\r\n            <div\r\n                className=\"relative bg-white rounded-lg shadow-lg max-w-md w-full p-5\"\r\n                onClick={(e) => e.stopPropagation()} // Prevent click from closing the modal\r\n            >\r\n                <div className=\"flex justify-between items-center mb-4\">\r\n                    <h3 className=\"text-lg font-semibold\">Update Member Status</h3>\r\n                    <button\r\n                        className=\"text-gray-500 hover:text-gray-800\"\r\n                        onClick={() => setVisible(false)}\r\n                    >\r\n                        &times;\r\n                    </button>\r\n                </div>\r\n                {error && <div className=\"text-red-500\">{error}</div>}\r\n                {successMessage && <div className=\"text-green-500\">{successMessage}</div>}\r\n                <form onSubmit={handleSubmit}>\r\n                    <div className=\"mb-4\">\r\n                        <label htmlFor=\"name\" className=\"block mb-2\">Member Status Name</label>\r\n                        <input\r\n                            type=\"text\"\r\n                            id=\"name\"\r\n                            value={memberStatusName}\r\n                            onChange={(e) => setMemberStatusName(e.target.value)}\r\n                            className=\"border rounded w-full p-2\"\r\n                            required\r\n                        />\r\n                    </div>\r\n                    <button\r\n                        type=\"submit\"\r\n                        className=\"bg-primary hover:bg-secondary text-white rounded-md px-4 py-2\"\r\n                    >\r\n                        Update Member Status\r\n                    </button>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default EditMemberStatus;\r\n", "import React, { useState, useCallback, useEffect } from \"react\";\r\n\r\n// DataTable component for rendering tabular data with features like pagination and sorting\r\nimport DataTable from \"react-data-table-component\";\r\n\r\n// Loading spinner component to show while data is loading\r\nimport Loading from \"./../../common/Loading\";\r\n\r\nimport {confirmation<PERSON><PERSON>t, ManageColumns, SearchFilter, TableView} from './../../common/coreui';\r\n\r\n\r\nimport { useDispatch } from \"react-redux\";\r\nimport { defaultDateTimeFormat, removeKeys, sortByLabel } from \"./../../utils\";\r\n\r\n// Libraries for exporting data to Excel\r\nimport { saveAs } from \"file-saver\";\r\nimport * as XLSX from \"xlsx\";\r\nimport { teamMemberStatusApi, useDeleteMemberStatusMutation, useGetMemberStatusDataQuery, useLazyFetchDataOptionsForMemberStatusQuery } from \"./../../features/api\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport EditMemberStatus from \"./EditMemberStatus\";\r\nimport AddMemberStatus from \"./AddMemberStatus\";\r\nimport { useRoleBasedAccess } from \"./../../common/useRoleBasedAccess\";\r\nimport { DateTimeFormatDay, DateTimeFormatHour } from \"../../common/DateTimeFormatTable\";\r\n\r\n// API endpoint and configuration constants\r\nconst MODULE_NAME = \"Team Member Status\";\r\n\r\n// Main component for listing Product Type List\r\nconst MemberSatusDataList = () => {\r\n  // State variables for data items, filters, search text, modals, and loading status\r\n  const [filterOptions, setFilterOptions] = useState({});\r\n  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});\r\n  const [showFilterOption, setShowFilterOption] = useState(\"\");\r\n  const [queryString, setQueryString] = useState(\"\");\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [filterOptionLoading, setFilterOptionLoading] = useState(false);\r\n  const [dataItemsId, setDataItemsId] = useState(null);\r\n  const [error, setError] = useState(null);\r\n  const [viewData, setViewData] = useState(null);\r\n  const navigate = useNavigate();\r\n  const [addModalVisible, setAddModalVisible] = useState(false);\r\n\r\n  \r\n  // Sorting and pagination state\r\n  const [sortColumn, setSortColumn] = useState(\"created_at\");\r\n  const [sortDirection, setSortDirection] = useState(\"desc\");\r\n  const [perPage, setPerPage] = useState(\"10\");\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n\r\n  \r\n  const { data: dataItems, isFetching, error: fetchError } = useGetMemberStatusDataQuery({ sort_by: sortColumn, order: sortDirection, page: currentPage, per_page: perPage, query: queryString });\r\n\r\n  const [triggerFilterByFetch, { data: groupData, error: groupDataError }] = useLazyFetchDataOptionsForMemberStatusQuery();\r\n       \r\n  const [deleteMemberStatus] = useDeleteMemberStatusMutation();\r\n\r\n  // Build query parameters from selected filters\r\n  const buildQueryParams = (selectedFilters) => {\r\n    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {\r\n      if (typeof value === \"string\") {\r\n        return acc + `&${key}=${value}`;\r\n      }\r\n      if (Array.isArray(value)) {\r\n        const vals = value.map((i) => i.value).join(\",\");\r\n        return acc + `&${key}=${vals}`;\r\n      }\r\n      return acc;\r\n    }, \"\")\r\n\r\n    setQueryString(q);\r\n  }\r\n\r\n  const handleCopy = (data) => {\r\n    const keysToRemove = [\"id\", \"team\", \"department\", \"updated_at\", \"updated_by\", \"updater\", \"created_at\", \"creator\", \"created_by\", \"updated_by\"];\r\n    const cleanedData = removeKeys(data, keysToRemove);\r\n    setViewData(null)\r\n    setModalVisible(true);\r\n  }\r\n\r\n  const handleEdit = (id) => {\r\n    setViewData(null)\r\n    setDataItemsId(id); \r\n    setModalVisible(true);\r\n  }\r\n\r\n  const handleDelete = (id) => {\r\n    confirmationAlert({onConfirm: () => \r\n      {        \r\n        deleteMemberStatus(id);\r\n        setViewData(null);\r\n      }});  \r\n  }\r\n \r\n\r\n  let columnSerial = 1;\r\n\r\n  const { rolePermissions } = useRoleBasedAccess();\r\n\r\n  // Define columns dynamically based on rolePermissions\r\n  const [columns, setColumns] = useState(() => [\r\n    {\r\n        id: columnSerial++,\r\n      name: \"Action\",\r\n      width: \"180px\",\r\n      className: \"bg-red-300\",\r\n      cell: (item) => (\r\n        <div className=\"flex gap-1 mx-2 !min-w-[200px] pl-3\">\r\n          {/* View Button */}\r\n          <button\r\n            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n            onClick={() => setViewData(item)}\r\n          >\r\n            <span className=\"material-symbols-outlined text-lg\">visibility</span>\r\n          </button>\r\n  \r\n          {/* Conditionally render Edit Button based on rolePermissions */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleEdit(item.id)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\r\n            </button>\r\n          )}\r\n  \r\n          {/* Copy Button */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleCopy(item)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-lg\">content_copy</span>\r\n            </button>\r\n          )}\r\n  \r\n          {/* Delete Button */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleDelete(item.id)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-sm\">delete</span>\r\n            </button>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n      name: \"S.No\",\r\n      selector: (row, index) => (currentPage - 1) * perPage + index + 1,\r\n      width: \"80px\",\r\n      omit: false,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n      name: \"Resource Status Name\",\r\n      db_field: \"name\",\r\n      selector: (row) => row.name || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Created by\",\r\n        selector: (row) => `${row.creator?.fname || \"\"} ${row.creator?.lname || \"\"}`,\r\n        db_field: \"created_by\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Created Date\",\r\n      selector: (row) => DateTimeFormatDay(row.created_at),\r\n      db_field: \"created_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Created Time\",\r\n        selector: (row) => DateTimeFormatHour(row.created_at),\r\n        db_field: \"created_at\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: false,\r\n      },\r\n      {\r\n        id: columnSerial++,\r\n        name: \"Updated by\",\r\n        selector: (row) => `${row.updater?.fname || \"\"} ${row.updater?.lname || \"\"}`,\r\n        db_field: \"updated_by\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: true,\r\n      },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Updated Date\",\r\n      selector: (row) => DateTimeFormatDay(row.updated_at),\r\n      db_field: \"updated_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Updated Time\",\r\n        selector: (row) => DateTimeFormatHour(row.updated_at),\r\n        db_field: \"updated_at\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: false,\r\n    },\r\n  ]);\r\n  \r\n  useEffect(() => {\r\n    // Recalculate or update columns if rolePermissions change\r\n    setColumns((prevColumns) => [\r\n      ...prevColumns.map((col) => {\r\n        if (col.name === \"Action\") {\r\n          // Update the \"Action\" column dynamically\r\n          return {\r\n            ...col,\r\n            cell: (item) => (\r\n              <div className=\"flex gap-1 mx-2 !min-w-[200px] pl-3\">\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => setViewData(item)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-lg\">visibility</span>\r\n                </button>\r\n                {rolePermissions?.hasManagerRole && (\r\n                  <button\r\n                    className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                    onClick={() => handleEdit(item.id)}\r\n                  >\r\n                    <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\r\n                  </button>\r\n                )}\r\n\r\n                {rolePermissions?.hasManagerRole && (\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => handleCopy(item)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-lg\">content_copy</span>\r\n                </button>\r\n                )}\r\n\r\n                {rolePermissions?.hasManagerRole && (\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => handleDelete(item.id)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-sm\">delete</span>\r\n                </button>\r\n                )}\r\n              </div>\r\n            ),\r\n          };\r\n        }\r\n        return col;\r\n      }),\r\n    ]);\r\n  }, [rolePermissions]); // Dependency array ensures this is updated whenever rolePermissions changes\r\n  \r\n  \r\n\r\n  // Resets the pagination and clear-all filter state\r\n  const resetPage = () => {\r\n    if (Object.keys(selectedFilterOptions).length) {\r\n      let newObj = {};\r\n      Object.keys(selectedFilterOptions).map((key) => {\r\n        if (typeof selectedFilterOptions[key] === \"string\") {\r\n          newObj[key] = \"\";\r\n        } else {\r\n          newObj[key] = [];\r\n        }\r\n      });\r\n      setSelectedFilterOptions({ ...newObj });\r\n      buildQueryParams({ ...newObj })\r\n    }\r\n    setCurrentPage(1);\r\n  };\r\n\r\n\r\n  // Export the fetched data into an Excel file\r\n  const dispatch = useDispatch();\r\n  const exportToExcel = async () => {\r\n    try {\r\n      // Fetch all data items for Excel export\r\n      const result = await dispatch(\r\n        teamMemberStatusApi.endpoints.getMemberStatusData.initiate({\r\n          sort_by: sortColumn,\r\n          order: sortDirection,\r\n          page: currentPage,\r\n          per_page: dataItems?.total || 10, // Fallback value to avoid undefined issues\r\n          query: queryString,\r\n        })\r\n      ).unwrap(); // Wait for the API response\r\n  \r\n      if (!result?.total || result.total < 1) {\r\n        return false;\r\n      }\r\n  \r\n      var sl = 1;\r\n  \r\n      let prepXlsData = result.data.map((item) => {\r\n        if (columns.length) {\r\n          let obj = {};\r\n          columns.forEach((column) => {\r\n            if (!column.omit && column.selector) {\r\n              obj[column.name] = column.name === \"S.No\" ? sl++ : column.selector(item) || \"\";\r\n            }\r\n          });\r\n          return obj;\r\n        }\r\n      });\r\n  \r\n      // Create a worksheet from the JSON data and append to a new workbook\r\n      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);\r\n      const workbook = XLSX.utils.book_new();\r\n      XLSX.utils.book_append_sheet(workbook, worksheet, \"Sheet1\");\r\n  \r\n      // Convert workbook to a buffer and create a Blob to trigger a file download\r\n      const excelBuffer = XLSX.write(workbook, {\r\n        bookType: \"xlsx\",\r\n        type: \"array\",\r\n      });\r\n      const blob = new Blob([excelBuffer], { type: \"application/octet-stream\" });\r\n      saveAs(blob, `${MODULE_NAME.replace(/ /g,\"_\")}_${prepXlsData.length}.xlsx`);\r\n    } catch (error) {\r\n      console.error(\"Error exporting to Excel:\", error);\r\n    }\r\n  };\r\n  \r\n\r\n  /**\r\n   * Fetch filter options from API for a specific field.\r\n   */\r\n  const fetchDataOptionsForFilterBy = useCallback(\r\n    async (\r\n      itemObject = {},\r\n      type = \"group\",\r\n      searching = \"\",\r\n      fieldType = \"select\"\r\n    ) => {\r\n\r\n      let groupByField = itemObject.db_field || \"title\";\r\n\r\n      try {\r\n        setShowFilterOption(groupByField);\r\n        setFilterOptionLoading(true);\r\n\r\n        var groupData = [];\r\n\r\n        const response = await triggerFilterByFetch({ type: type.trim(), column: groupByField.trim(), text: searching.trim() });\r\n        \r\n        if (response.data) {\r\n          groupData = response.data;\r\n        }\r\n\r\n        if (groupData.length) {\r\n\r\n          if (fieldType === \"searchable\") {\r\n            setFilterOptions((prev) => ({\r\n              ...prev,\r\n              [groupByField]: groupData,\r\n            }));\r\n\r\n            return groupData;\r\n          }\r\n\r\n          const optionsForFilter = groupData\r\n            .map((item) => {\r\n              if(itemObject.selector){\r\n                let label = itemObject.selector(item);\r\n\r\n                if(label){\r\n                  if (item.total && item.total > 1) {\r\n                    label += ` (${item.total})`;\r\n                  }\r\n\r\n                  return { label, value: item[groupByField] };\r\n                }\r\n\r\n              return null;\r\n              }\r\n            }).filter(Boolean);\r\n\r\n          setFilterOptions((prev) => ({\r\n            ...prev,\r\n            [itemObject.id]: sortByLabel(optionsForFilter),\r\n          }));\r\n\r\n          return optionsForFilter;\r\n        }\r\n      } catch (error) {\r\n        setError(error.message);\r\n      } finally {\r\n        setFilterOptionLoading(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  return (\r\n    <section className=\"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\">\r\n      <div className=\"mx-auto pb-6 \">\r\n        {/* Header section with title and action buttons */}\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4\">\r\n          <div className=\"w-4/12 md:w-10/12 text-start\">\r\n            <h2 className=\"text-2xl font-bold \">{MODULE_NAME}</h2>\r\n          </div>\r\n          <div className=\"w-8/12 flex items-end justify-end gap-1\">\r\n            {/* Manage Columns dropdown */}\r\n            <ManageColumns columns={columns} setColumns={setColumns} />\r\n            \r\n            {/* Export to Excel button, only shown if data exists */}\r\n            { !isFetching && dataItems && parseInt(dataItems.total) > 0 && (\r\n              <>\r\n                <button\r\n                  className=\"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\"\r\n                  onClick={exportToExcel}\r\n                >\r\n                  {isFetching && (\r\n                    <>\r\n                      <span className=\"material-symbols-outlined animate-spin text-sm me-2\">\r\n                        progress_activity\r\n                      </span>\r\n                    </>\r\n                  )}\r\n                  {!isFetching && (\r\n                    <span className=\"material-symbols-outlined text-sm me-2\">\r\n                    file_export\r\n                    </span>\r\n                  )}\r\n                  Export to Excel ({dataItems.total})\r\n                </button>\r\n              </>\r\n            )}\r\n            {/* Button to open modal for adding a new formation */}\r\n            {rolePermissions.hasManagerRole && (\r\n              <button\r\n                className=\" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\"\r\n\r\n                onClick={() => setAddModalVisible(true)}\r\n              >\r\n                Add New\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Filter fieldset for global search and field-specific filtering */}\r\n        <SearchFilter\r\n            columns={columns}\r\n            selectedFilterOptions={selectedFilterOptions}\r\n            setSelectedFilterOptions={setSelectedFilterOptions}\r\n            fetchDataOptionsForFilterBy={fetchDataOptionsForFilterBy}\r\n            filterOptions={filterOptions}\r\n            filterOptionLoading={filterOptionLoading}\r\n            showFilterOption={showFilterOption}\r\n            resetPage={resetPage}\r\n            setCurrentPage={setCurrentPage}\r\n            buildQueryParams={buildQueryParams}\r\n        />\r\n\r\n        {/* Display error message if any error occurs */}\r\n        {fetchError && <div className=\"text-red-500\">{error}</div>}\r\n        {/* Show loading spinner when data is being fetched */}\r\n        {isFetching && <Loading />}\r\n\r\n        {/* If no data is available, display an alert message */}\r\n        \r\n        {/* Render the DataTable with the fetched data */}\r\n        <div className=\"border border-gray-200 p-0 pb-1 rounded-lg my-5 \">\r\n          <DataTable\r\n            columns={columns}\r\n            data={dataItems?.data || []}\r\n            className=\"p-0 scrollbar-horizontal-10\"\r\n            fixedHeader\r\n            \r\n            highlightOnHover\r\n            responsive\r\n            pagination\r\n            paginationServer\r\n            paginationPerPage={perPage}\r\n            paginationTotalRows={dataItems?.total || 0}\r\n            onChangePage={(page) => {\r\n              if (page !== currentPage) {\r\n                setCurrentPage(page);\r\n              }\r\n            }}\r\n            onChangeRowsPerPage={(newPerPage) => {\r\n              if(newPerPage !== perPage){\r\n                setPerPage(newPerPage);\r\n                setCurrentPage(1);\r\n              }\r\n            }}\r\n            paginationComponentOptions={{\r\n              selectAllRowsItem: true,\r\n              selectAllRowsItemText: \"ALL\",\r\n            }}\r\n            sortServer\r\n            onSort={(column, sortDirection=\"desc\") => {\r\n              if(Object.keys(column).length){\r\n                setSortColumn(column.db_field || column.name || \"created_at\");\r\n                setSortDirection(sortDirection || \"desc\");\r\n              }\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Add Modal */}\r\n        {addModalVisible && (\r\n            <AddMemberStatus\r\n                isVisible={addModalVisible}\r\n                setVisible={setAddModalVisible}\r\n            />\r\n        )}\r\n\r\n        {/* Conditionally render the Edit modal */}\r\n        {modalVisible && (\r\n          <EditMemberStatus\r\n            isVisible={modalVisible}\r\n            setVisible={setModalVisible}\r\n            dataItemsId={dataItemsId}\r\n          />\r\n        )}\r\n\r\n        {viewData && (\r\n          // <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\r\n          <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\r\n        )}\r\n       \r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\n\r\nexport default MemberSatusDataList;\r\n", "import React from 'react';\r\nimport MemberSatusDataList from '../../pages/member-status/MemberStatusDataList';\r\n\r\nconst MemberStatus = () => {\r\n  return (\r\n    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>\r\n      <MemberSatusDataList />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MemberStatus;\r\n"], "names": ["API_URL", "process", "_ref", "isVisible", "setVisible", "dataItemsId", "memberStatusName", "setMemberStatusName", "useState", "error", "setError", "successMessage", "setSuccessMessage", "loggedInUser", "setLoggedInUser", "useEffect", "userId", "localStorage", "getItem", "async", "token", "response", "fetch", "method", "headers", "ok", "Error", "statusText", "data", "json", "member_status", "name", "message", "fetchMemberStatus", "_jsx", "className", "onClick", "children", "_jsxs", "e", "stopPropagation", "onSubmit", "event", "preventDefault", "updatedBy", "body", "JSON", "stringify", "trim", "updated_by", "result", "alertMessage", "icon", "title", "text", "setTimeout", "htmlFor", "type", "id", "value", "onChange", "target", "required", "MODULE_NAME", "MemberSatusDataList", "filterOptions", "setFilterOptions", "selectedFilterOptions", "setSelectedFilterOptions", "showFilterOption", "setShowFilterOption", "queryString", "setQueryString", "modalVisible", "setModalVisible", "filterOptionLoading", "setFilterOptionLoading", "setDataItemsId", "viewData", "setViewData", "addModalVisible", "setAddModalVisible", "useNavigate", "sortColumn", "setSortColumn", "sortDirection", "setSortDirection", "perPage", "setPerPage", "currentPage", "setCurrentPage", "dataItems", "isFetching", "fetchError", "useGetMemberStatusDataQuery", "sort_by", "order", "page", "per_page", "query", "triggerFilterByFetch", "groupData", "groupDataError", "useLazyFetchDataOptionsForMemberStatusQuery", "deleteMemberStatus", "useDeleteMemberStatusMutation", "buildQueryParams", "selectedFilters", "q", "Object", "entries", "reduce", "acc", "key", "Array", "isArray", "map", "i", "join", "handleCopy", "<PERSON><PERSON><PERSON><PERSON>", "handleEdit", "handleDelete", "<PERSON><PERSON><PERSON><PERSON>", "onConfirm", "columnSerial", "rolePermissions", "useRoleBasedAccess", "columns", "setColumns", "width", "cell", "item", "hasManagerRole", "selector", "row", "index", "omit", "db_field", "sortable", "filterable", "_row$creator", "_row$creator2", "creator", "fname", "lname", "DateTimeFormatDay", "created_at", "DateTimeFormatHour", "_row$updater", "_row$updater2", "updater", "updated_at", "prevColumns", "col", "dispatch", "useDispatch", "fetchDataOptionsForFilterBy", "useCallback", "itemObject", "arguments", "length", "undefined", "searching", "fieldType", "groupByField", "column", "prev", "optionsForFilter", "label", "total", "filter", "Boolean", "sortByLabel", "ManageColumns", "parseInt", "_Fragment", "teamMemberStatusApi", "endpoints", "getMemberStatusData", "initiate", "unwrap", "sl", "prepXlsData", "obj", "for<PERSON>ach", "worksheet", "XLSX", "json_to_sheet", "workbook", "book_new", "book_append_sheet", "excelBuffer", "bookType", "blob", "Blob", "saveAs", "replace", "console", "SearchFilter", "resetPage", "keys", "newObj", "Loading", "DataTable", "fixedHeader", "highlightOnHover", "responsive", "pagination", "paginationServer", "paginationPerPage", "paginationTotalRows", "onChangePage", "onChangeRowsPerPage", "newPerPage", "paginationComponentOptions", "selectAllRowsItem", "selectAllRowsItemText", "sortServer", "onSort", "AddMemberStatus", "EditMemberStatus", "TableView", "MemberStatus"], "sourceRoot": ""}