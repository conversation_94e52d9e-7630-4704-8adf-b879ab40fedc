<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('team_shift_plans', function (Blueprint $table) {
            $table->id();
            $table->integer('department')->nullable();
            $table->integer('team')->nullable();
            $table->string('week')->nullable();
            $table->integer('shift')->nullable();
            $table->integer('eid')->nullable();
            $table->string('created_by')->nullable();
            $table->string('updated_by')->nullable();
            $table->timestamps();


        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('team_shift_plans');
    }
};
