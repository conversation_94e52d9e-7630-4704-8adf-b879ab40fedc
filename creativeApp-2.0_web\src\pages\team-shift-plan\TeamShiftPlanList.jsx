import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import { useNavigate } from 'react-router-dom';
import EditTeamShiftPlan from './EditTeamShiftPlan';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const getToken = () => localStorage.getItem('token');

const isTokenValid = () => getToken() !== null;

const TeamShiftPlanList = () => {
    const [shiftPlans, setShiftPlans] = useState([]);
    const [error, setError] = useState(null);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [selectedShiftPlanId, setSelectedShiftPlanId] = useState(null);

    const navigate = useNavigate();

    const columnNames = [
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Week", key: "week" },
        { label: "Shift", key: "shift" },
        { label: "Employee ID", key: "eid" },
        { label: "Day of Week", key: "day_of_week" },
        { label: "Days", key: "days" },
    ];

    useEffect(() => {
        const fetchShiftPlans = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}team-shift-plans`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${getToken()}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`Network response was not ok: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Team Shift Plans', data);

                if (data.shiftPlans) {
                    const formattedData = data.shiftPlans.map((plan) => ({
                        id: plan.id,
                        department: plan.department,
                        team: plan.team,
                        week: plan.week,
                        shift: plan.shift,
                        eid: plan.eid,
                        day_of_week: plan.day_of_week,
                        days: plan.days,
                    }));
                    setShiftPlans(formattedData);
                } else {
                    setError("Unexpected data format received.");
                }
            } catch (error) {
                setError(error.message);
            }
        };

        fetchShiftPlans();
    }, []);

    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        try {
            const response = await fetch(`${API_URL}team-shift-plans/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${getToken()}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`Failed to delete shift plan: ${response.statusText}`);
            }

            setShiftPlans((prev) => prev.filter((plan) => plan.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    const handleEdit = (id) => {
        setSelectedShiftPlanId(id);
        setIsModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={shiftPlans}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
            />
            {isModalVisible && (
                <EditTeamShiftPlan
                    isVisible={isModalVisible}
                    setVisible={setIsModalVisible}
                    shiftPlanId={selectedShiftPlanId}
                />
            )}
        </div>
    );
};

export default TeamShiftPlanList;
