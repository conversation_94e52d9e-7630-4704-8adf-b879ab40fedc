import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditMemberStatus from './EditMemberStatus'; // Assuming you have an EditMemberStatus component

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const MemberStatusList = () => {
    const [memberStatuses, setMemberStatuses] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [loading, setLoading] = useState(true); // Initially loading is true
    const [selectedMemberStatusId, setSelectedMemberStatusId] = useState(null);
    const [error, setError] = useState(null);

    // Update column names for Member Statuses
    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Member Status", key: "name" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchMemberStatuses = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}member_statuses`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();


                setMemberStatuses(data.member_statuses.map(status => ({
                    id: status.id,
                    name: status.name,
                    created_by: status.created_by,
                    updated_by: status.updated_by,
                })));
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchMemberStatuses();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}member_statuses/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete member status: ' + response.statusText);
            }

            // Update the member statuses list after deletion
            setMemberStatuses(prevStatuses => prevStatuses.filter(status => status.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedMemberStatusId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    // Show message when no member statuses are available
    if (memberStatuses.length === 0) {
        return <div className="text-gray-500">No data available</div>; // Show "No data available" if memberStatuses array is empty
    }

    return (
        <div>
            <TableContent
                tableContent={memberStatuses}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedMemberStatusId}
            />
            {modalVisible && (
                <EditMemberStatus
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    memberStatusId={selectedMemberStatusId}
                />
            )}
        </div>
    );
};

export default MemberStatusList;
