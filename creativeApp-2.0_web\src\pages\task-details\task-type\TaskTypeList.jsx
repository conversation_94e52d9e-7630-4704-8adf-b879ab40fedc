import React, { useEffect, useState } from 'react';
import TableContent from '../../../common/table/TableContent';
import EditProductType from './EditTaskType';

const isTokenValid = () => {
    const token = localStorage.getItem('token');

    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const TaskTypeList = () => {
    const [productTypes, setProductTypes] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedProductTypeId, setSelectedProductTypeId] = useState(null);
    const [error, setError] = useState(null);

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Product Type Name", key: "name" },
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchProductTypes = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}product-types`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();


                setProductTypes(data.productTypes.map(productType => ({
                    id: productType.id,
                    name: productType.name,
                    department: productType.department,
                    team: productType.team,
                    created_by: productType.created_by,
                    updated_by: productType.updated_by,
                })));
            } catch (error) {
                setError(error.message);
            }
        };

        fetchProductTypes();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}product-type/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete product type: ' + response.statusText);
            }

            // Update the product types list after deletion
            setProductTypes(prevProductTypes => prevProductTypes.filter(productType => productType.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedProductTypeId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={productTypes}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedProductTypeId}
            />
            {modalVisible && (
                <EditProductType
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    productTypeId={selectedProductTypeId}
                />
            )}
        </div>
    );
};

export default TaskTypeList;
