
export default function FetchTeamData(){
    const [holidaycalenders, setholidaycalenders] = useState([]);
    const [error, setError] = useState(null);

    const API_URL = process.env.REACT_APP_BASE_API_URL;

    useEffect(() => {
        const fetchholidaycalenders = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}holidaycalenders`, { // Update the API endpoint
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                
                setholidaycalenders(data.holidaycalenders); // Assuming the API returns an array of holidaycalenders
            } catch (error) {
                setError(error.message);
            }
        };

        fetchholidaycalenders();
    }, []);
}