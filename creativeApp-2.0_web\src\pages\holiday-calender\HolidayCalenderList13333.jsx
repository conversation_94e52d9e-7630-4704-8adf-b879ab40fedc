import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import { useNavigate } from 'react-router-dom';
import EditHolidayCalender from './EditHolidayCalender';



const API_URL = process.env.REACT_APP_BASE_API_URL;

const getToken = () => localStorage.getItem('token');

const isTokenValid = () => getToken() !== null;

const HolidayCalendarList1 = () => {
    const [holidayCalendars, setHolidayCalendars] = useState([]);
    const [error, setError] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedHolidayId, setSelectedHolidayId] = useState(null);

    const navigate = useNavigate();

    const columnNames = [
        { label: "Office Location", key: "office_location" },
        { label: "Holiday Name", key: "holiday_name" },
        { label: "Holiday Department", key: "holiday_department" },
        { label: "Holiday Start Date", key: "holiday_start_date" },
        { label: "Holiday End Date", key: "holiday_end_date" },
        { label: "Day of Week", key: "day_of_week" },
        { label: "Days", key: "days" },
        
    ];

    useEffect(() => {
        const fetchHolidayCalendars = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}holidaycalenders`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${getToken()}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`Network response was not ok: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Holiday Calendar', data);

                if (data.holidaycalender) {
                    const formattedData = data.holidaycalender.map((calendar) => ({
                        id: calendar.id,
                        office_location: calendar.office_location,
                        holiday_name: calendar.holiday_name,
                        holiday_start_date: calendar.holiday_start_date,
                        holiday_end_date: calendar.holiday_end_date,
                        day_of_week: calendar.day_of_week,
                        days: calendar.days,
                        holiday_department: calendar.holiday_department || "N/A",
                    }));
                    setHolidayCalendars(formattedData);
                } else {
                    setError("Unexpected data format received.");
                }
            } catch (error) {
                setError(error.message);
            }
        };

        fetchHolidayCalendars();
    }, []);

    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        try {
            const response = await fetch(`${API_URL}holidaycalenders/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${getToken()}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`Failed to delete holiday calendar: ${response.statusText}`);
            }

            setHolidayCalendars((prev) => prev.filter((calendar) => calendar.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    const handleEdit = (id) => {
        setSelectedHolidayId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={holidayCalendars}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedHolidayId}
            />
            {modalVisible && (
                <EditHolidayCalender
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    holidayId={selectedHolidayId}
                />
            )}
        </div>
    );
};



export default HolidayCalendarList1;
