import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const AddMemberTest = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [users, setUsers] = useState([]);
    const [roles, setRoles] = useState([]);
    const [teams, setTeams] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [schedules, setSchedules] = useState([]);
    const [designations, setDesignations] = useState([]);
    const [resourceTypes, setResourceTypes] = useState([]);
    const [resourceStatuses, setResourceStatuses] = useState([]);
    const [billingStatuses, setBillingStatuses] = useState([]);
    const [contactTypes, setContactTypes] = useState([]);
    const [availableStatuses, setAvailableStatuses] = useState([]);
    const [memberStatuses, setMemberStatuses] = useState([]);
    const [branches, setBranches] = useState([]);
    const [onsiteStatuses, setOnsiteStatuses] = useState([]);
    const [eid, setEid] = useState('');
    const [email, setEmail] = useState('');
    const [selectedRoles, setSelectedRoles] = useState({});
    const [selectedTeams, setSelectedTeams] = useState({});
    const [selectedDepartments, setSelectedDepartments] = useState([]);
    const [selectedSchedules, setSelectedSchedules] = useState([]);
    const [selectedDesignations, setSelectedDesignations] = useState('');
    const [selectedResourceTypes, setSelectedResourceTypes] = useState('');
    const [selectedResourceStatuses, setSelectedResourceStatuses] = useState([]);
    const [selectedBillingStatuses, setSelectedBillingStatuses] = useState([]);
    const [selectedContactTypes, setSelectedContactTypes] = useState([]);
    const [selectedAvailableStatuses, setSelectedAvailableStatuses] = useState([]);
    const [selectedMemberStatuses, setSelectedMemberStatuses] = useState([]);
    const [selectedBranches, setSelectedBranches] = useState([]);
    const [selectedOnsiteStatuses, setSelectedOnsiteStatuses] = useState([]);

    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    const fetchUsers = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/users`, {
                method: 'GET',

                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }

            const data = await response.json();

            setUsers(data);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the roles to select for users
    const fetchRoles = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}roles`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the roles array from the data object
            const rolesData = data.roles; // Get the roles array
    
            const rolesMap = rolesData.reduce((acc, role) => {
                acc[role.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setRoles(rolesData);
            setSelectedRoles(rolesMap);
        } catch (error) {
            setError(error.message);
        }
    };   

    // Fetching all the teams to select for users
    const fetchTeams = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/teams`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the teams array from the data object
            const teamsData = data.teams; // Get the teams array
    
            const teamsMap = teamsData.reduce((acc, team) => {
                acc[team.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setTeams(teamsData);
            setSelectedTeams(teamsMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the departments to select for users
    const fetchDepartments = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}departments`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the teams array from the data object
            const departmentsData = data.departments; // Get the teams array
    
            const departmentsMap = departmentsData.reduce((acc, department) => {
                acc[department.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setDepartments(departmentsData);
            setSelectedDepartments(departmentsMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the designations to select for users
    const fetchDesignations = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}designations`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the teams array from the data object
            const designationsData = data.designations; // Get the teams array
    
            const designationsMap = designationsData.reduce((acc, designation) => {
                acc[designation.id] = false;
                return acc;
            }, {});
    
            setDesignations(designationsData);
            setSelectedDesignations(designationsMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the Resource Type (Responsibility Level) to select for users
    const fetchResourceTypes = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}resource_types`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the teams array from the data object
            const resourceTypesData = data['Resource Types']; // Get the resourceType array
    
            const resourceTypesMap = resourceTypesData.reduce((acc, resourceType) => {
                acc[resourceType.id] = false;
                return acc;
            }, {});
    
            setResourceTypes(resourceTypesData);
            setSelectedResourceTypes(resourceTypesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the schedules to select for users
    const fetchSchedules = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}schedules`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();

    
            // Access the schedule array from the data object
            const schedulesData = data.schedules; // Get the schedule array
    
            const schedulesMap = schedulesData.reduce((acc, schedule) => {
                acc[schedule.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setSchedules(schedulesData);
            setSelectedSchedules(schedulesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the resourceStatuses to select for users
    const fetchResourceStatuses = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}resource_statuses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();

    
            // Access the resourceStatuses array from the data object
            const resourceStatusesData = data.resource_status; // Get the resourceStatuses array
    
            const resourceStatusesMap = resourceStatusesData.reduce((acc, resourceStatus) => {
                acc[resourceStatus.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setResourceStatuses(resourceStatusesData);
            setSelectedResourceStatuses(resourceStatusesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the billing statuses to select for users
    const fetchBillingStatuses = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}billing_statuses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the teams array from the data object
            const billingStatusesData = data['billing statuses']; // Get the teams array
    
            const billingStatusesMap = billingStatusesData.reduce((acc, billingStatus) => {
                acc[billingStatus.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setBillingStatuses(billingStatusesData);
            setSelectedBillingStatuses(billingStatusesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the contact types to select for users
    const fetchContactTypes = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}contact_types`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the teams array from the data object
            const contactTypesData = data.contact_types; // Get the teams array
    
            const contactTypesMap = contactTypesData.reduce((acc, contactType) => {
                acc[contactType.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setContactTypes(contactTypesData);
            setSelectedContactTypes(contactTypesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the available status to select for users
    const fetchAvailableStatuses = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}available_statuses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the teams array from the data object
            const availableStatusesData = data.available_statuses; // Get the teams array
    
            const availableStatusesMap = availableStatusesData.reduce((acc, availableStatus) => {
                acc[availableStatus.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setAvailableStatuses(availableStatusesData);
            setSelectedAvailableStatuses(availableStatusesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the team member status to select for users
    const fetchMemberStatuses = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}member_statuses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the teams array from the data object
            const memberStatusesData = data.member_statuses; // Get the teams array
    
            const memberStatusesMap = memberStatusesData.reduce((acc, memberStatus) => {
                acc[memberStatus.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setMemberStatuses(memberStatusesData);
            setSelectedMemberStatuses(memberStatusesMap);
        } catch (error) {
            setError(error.message);
        }
    };
    
    // Fetching all the branch to select for users
    const fetchBranches = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}branches`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the teams array from the data object
            const branchesData = data.branches; // Get the teams array
    
            const branchesMap = branchesData.reduce((acc, branch) => {
                acc[branch.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setBranches(branchesData);
            setSelectedBranches(branchesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the branch to select for users
    const fetchOnsiteStatuses = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}onsite_statuses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the teams array from the data object
            const onsiteStatusesData = data.onsite_statuses; // Get the teams array
    
            const onsiteStatusesMap = onsiteStatusesData.reduce((acc, onsiteStatus) => {
                acc[onsiteStatus.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setOnsiteStatuses(onsiteStatusesData);
            setSelectedOnsiteStatuses(onsiteStatusesMap);
        } catch (error) {
            setError(error.message);
        }
    };
    
    
    

    useEffect(() => {
        fetchUsers();
        fetchRoles();
        fetchTeams();
        fetchDepartments();
        fetchSchedules();
        fetchDesignations();
        fetchResourceTypes();
        fetchResourceStatuses();
        fetchBillingStatuses();
        fetchContactTypes();
        fetchAvailableStatuses();
        fetchMemberStatuses();
        fetchBranches();
        fetchOnsiteStatuses();
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedEid = eid.trim();
        const trimmedEmail = email.trim();
    
        if (!trimmedEid || !trimmedEmail) {
            setError('EID and Email are required.');
            return;
        }
    
        const eidExists = users.some(user => typeof user.eid === 'string' && user.eid.toLowerCase().trim() === trimmedEid.toLowerCase());
        const emailExists = users.some(user => typeof user.email === 'string' && user.email.toLowerCase().trim() === trimmedEmail.toLowerCase());
    
        if (eidExists || emailExists) {
            let message = 'The ';
            if (eidExists) message += 'EID ';
            if (emailExists) message += (message.endsWith('The ') ? '' : 'or ') + 'Email ';
            message += 'already exists. Please add a new EID and/or Email.';
            setError(message);
            setTimeout(() => setError(''), 1000);
            return;
        }
    
        setError('');

        // Prepare roles array based on selected roles
        const selectedRoleIds = Object.keys(selectedRoles).filter(roleId => selectedRoles[roleId]);
        const selectedTeamIds = Object.keys(selectedTeams).filter(teamId => selectedTeams[teamId]);
        // Always passed as an array (even if one department is selected)
        const selectedDepartmentIds = Array.isArray(selectedDepartments) ? selectedDepartments : [selectedDepartments];
        const selectedScheduleIds = Array.isArray(selectedSchedules) ? selectedSchedules : [selectedSchedules];
        // Single select: directly pass the selected value, not in an array
        const selectedDesignationIds = selectedDesignations ? [selectedDesignations] : [];
        const selectedResourceTypeIds = selectedResourceTypes ? [selectedResourceTypes] : [];
        const selectedResourceStatusIds = Array.isArray(selectedResourceStatuses) ? selectedResourceStatuses : [selectedResourceStatuses];
        const selectedBillingStatusIds = Array.isArray(selectedBillingStatuses) ? selectedBillingStatuses : [selectedBillingStatuses];
        const selectedContactTypeIds = Array.isArray(selectedContactTypes) ? selectedContactTypes : [selectedContactTypes];
        const selectedAvailableStatusIds = Array.isArray(selectedAvailableStatuses) ? selectedAvailableStatuses : [selectedAvailableStatuses];
        const selectedMemberStatusIds = Array.isArray(selectedMemberStatuses) ? selectedMemberStatuses : [selectedMemberStatuses];
        const selectedBranchIds = Array.isArray(selectedBranches) ? selectedBranches : [selectedBranches];
        const selectedOnsiteStatusIds = Array.isArray(selectedOnsiteStatuses) ? selectedOnsiteStatuses : [selectedOnsiteStatuses];

        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}/users`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    eid: trimmedEid,
                    email: trimmedEmail,
                    roles: selectedRoleIds.map(Number), // Send the roles array as numbers
                    teams: selectedTeamIds.map(Number), // Send the teams array as numbers
                    departments: selectedDepartmentIds.map(Number), // Send the departments as numbers
                    schedules: selectedScheduleIds.map(Number),
                    designations: selectedDesignationIds.map(Number),
                    resource_types: selectedResourceTypeIds.map(Number),
                    resource_statuses: selectedResourceStatusIds.map(Number),
                    billing_statuses: selectedBillingStatusIds.map(Number),
                    contact_types: selectedContactTypeIds.map(Number),
                    available_statuses: selectedAvailableStatusIds.map(Number),
                    member_statuses: selectedMemberStatusIds.map(Number),
                    branches: selectedBranchIds.map(Number),
                    onsite_statuses: selectedOnsiteStatusIds.map(Number),
                }),
            });
    
            if (!response.ok) {
                const errorText = await response.text();
                console.error('Error response:', errorText);
                throw new Error('Failed to save user: ' + response.statusText);
            }
            
    
            const result = await response.json();
            setSuccessMessage(`User with EID "${trimmedEid}" added successfully!`);
            setEid('');
            setEmail('');
            setSelectedRoles({});
            setSelectedTeams({});
            setSelectedDepartments([]);
            setSelectedSchedules([]);
            setSelectedDesignations('');
            setSelectedResourceTypes('');
            setSelectedResourceStatuses([]);
            setSelectedBillingStatuses([]);
            setSelectedContactTypes([]);
            setSelectedAvailableStatuses([]);
            setSelectedMemberStatuses([]);
            setSelectedBranches([]);
            setSelectedOnsiteStatuses([]);
    
            fetchUsers();
        } catch (error) {
            setError(error.message);
        }
    };

    const isModalOpen = location.pathname === '/add-member';

    const handleClose = () => {
        navigate('/member-onboard');
    };

    return (
        <>
            {isModalOpen && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-6xl relative  overflow-y-auto h-[80vh] mt-10">
                        <button onClick={handleClose} className="absolute top-0 right-2 text-gray-400 hover:text-gray-900 text-4xl">
                            &times;
                        </button>
                        <h4 className="text-2xl text-left font-semibold mb-6">Onboard New Team Member</h4>
                        <form onSubmit={handleSubmit}>
                            <div className='flex flex-wrap gap-4'>
                                <div className="mb-4 w-full md:max-w-[23%] text-left">
                                    <label htmlFor="eid" className="block text-sm font-medium text-gray-700 pb-4">
                                        EID
                                    </label>
                                    <input
                                        type="text"
                                        id="eid"
                                        value={eid}
                                        onChange={(e) => setEid(e.target.value)}
                                        placeholder='Add Team Member ID'
                                        required
                                        className="py-3 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    />
                                </div>
                                <div className="mb-4 w-full md:max-w-[23%] text-left">
                                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 pb-4">
                                        Email
                                    </label>
                                    <input
                                        type="email"
                                        id="email"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        placeholder='Add Team Member Email'
                                        required
                                        className="py-3 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    />
                                </div>
                                {/* Select Designations */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left">
                                    <label htmlFor="designation" className="block text-sm font-medium text-gray-700 pb-4">
                                        Designation
                                    </label>
                                    <div className="">
                                        <select
                                            value={selectedDesignations || ''} // Handle single value
                                            onChange={(e) => setSelectedDesignations(e.target.value)} // Update with selected value
                                            className="py-3 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                        >
                                            <option value="" disabled>Select a designation</option>
                                            {designations.map((designation) => (
                                                <option key={designation.id} value={designation.id}>
                                                    {designation.name}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                </div>
                                {/* Select Responsibility Level (Resource Types) */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left">
                                    <label htmlFor="resource-type" className="block text-sm font-medium text-gray-700 pb-4">
                                        Resource Type
                                    </label>
                                    <div className="">
                                        <select
                                            value={selectedResourceTypes || ''}
                                            onChange={(e) => setSelectedResourceTypes(e.target.value)} // This updates selectedResourceTypes state
                                            className="py-3 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                        >
                                            <option value="" disabled>Select a resource type</option>
                                            {resourceTypes.map((resourceType) => (
                                                <option key={resourceType.id} value={resourceType.id}>
                                                    {resourceType.name}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                </div>

                                {/* Select Roles */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        Roles
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {roles.map(role => (
                                            <label className="inline-flex items-center" key={role.id}>
                                                <input
                                                    type="checkbox"
                                                    checked={selectedRoles[role.id] || false}
                                                    onChange={() => setSelectedRoles(prev => ({ ...prev, [role.id]: !prev[role.id] }))}
                                                    className="form-checkbox my-2"
                                                />
                                                <span className="ml-2">{role.name}</span> {/* Assuming role has a 'name' property */}
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                {/* Select Teams */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        Teams
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {teams.map(team => (
                                            <label className="inline-flex items-center" key={team.id}>
                                                <input
                                                    type="checkbox"
                                                    checked={selectedTeams[team.id] || false}
                                                    onChange={() => setSelectedTeams(prev => ({ ...prev, [team.id]: !prev[team.id] }))}
                                                    className="form-checkbox my-2"
                                                />
                                                <span className="ml-2">{team.name}</span> {/* Assuming role has a 'name' property */}
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                {/* Select Department */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        Department
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {departments.map(department => (
                                            <label className="inline-flex items-center" key={department.id}>
                                                <input
                                                    type="radio"
                                                    name="department"  // Same name attribute ensures only one department can be selected
                                                    value={department.id}
                                                    checked={selectedDepartments === department.id} // Compare selectedDepartments directly to department.id
                                                    onChange={() => setSelectedDepartments(department.id)} // Set the department.id as the selected one
                                                    className="form-radio my-2"
                                                />
                                                <span className="ml-2">{department.name}</span> {/* Assuming department has a 'name' property */}
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                {/* Select Schedules */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        Schedules
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {schedules.map(schedule => (
                                            <label className="inline-flex items-center" key={schedule.id}>
                                                <input
                                                    type="radio"
                                                    name="schedule"
                                                    value={schedule.id}
                                                    checked={selectedSchedules === schedule.id}
                                                    onChange={() => setSelectedSchedules(schedule.id)}
                                                    className="form-radio my-2"
                                                />
                                                <span className="ml-2">{schedule.shift_name}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                {/* Select Resource Statuses */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        Resource Statuses
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {resourceStatuses.map(resourceStatus => (
                                            <label className="inline-flex items-center" key={resourceStatus.id}>
                                                <input
                                                    type="radio"
                                                    name="resourceStatus"
                                                    value={resourceStatus.id}
                                                    checked={selectedResourceStatuses === resourceStatus.id}
                                                    onChange={() => setSelectedResourceStatuses(resourceStatus.id)}
                                                    className="form-radio my-2"
                                                />
                                                <span className="ml-2">{resourceStatus.name}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                {/* Select Billing Statuses */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        Billing Statuses
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {billingStatuses.map(billingStatus => (
                                            <label className="inline-flex items-center" key={billingStatus.id}>
                                                <input
                                                    type="radio"
                                                    name="billingStatus"
                                                    value={billingStatus.id}
                                                    checked={selectedBillingStatuses === billingStatus.id}
                                                    onChange={() => setSelectedBillingStatuses(billingStatus.id)}
                                                    className="form-radio my-2"
                                                />
                                                <span className="ml-2">{billingStatus.name}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                {/* Select Contact Types */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        Contact Types
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {contactTypes.map(contactType => (
                                            <label className="inline-flex items-center" key={contactType.id}>
                                                <input
                                                    type="radio"
                                                    name="contactType"
                                                    value={contactType.id}
                                                    checked={selectedContactTypes === contactType.id}
                                                    onChange={() => setSelectedContactTypes(contactType.id)}
                                                    className="form-radio my-2"
                                                />
                                                <span className="ml-2">{contactType.name}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                {/* Select Available Statuses */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        Available Statuses
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {availableStatuses.map(availableStatus => (
                                            <label className="inline-flex items-center" key={availableStatus.id}>
                                                <input
                                                    type="radio"
                                                    name="availableStatus"
                                                    value={availableStatus.id}
                                                    checked={selectedAvailableStatuses === availableStatus.id}
                                                    onChange={() => setSelectedAvailableStatuses(availableStatus.id)}
                                                    className="form-radio my-2"
                                                />
                                                <span className="ml-2">{availableStatus.name}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                {/* Select Team Member Statuses */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        Team Member Statuses
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {memberStatuses.map(memberStatus => (
                                            <label className="inline-flex items-center" key={memberStatus.id}>
                                                <input
                                                    type="radio"
                                                    name="memberStatus"
                                                    value={memberStatus.id}
                                                    checked={selectedMemberStatuses === memberStatus.id}
                                                    onChange={() => setSelectedMemberStatuses(memberStatus.id)}
                                                    className="form-radio my-2"
                                                />
                                                <span className="ml-2">{memberStatus.name}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                {/* Select Branch */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        Branch
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {branches.map(branch => (
                                            <label className="inline-flex items-center" key={branch.id}>
                                                <input
                                                    type="radio"
                                                    name="branch"
                                                    value={branch.id}
                                                    checked={selectedBranches === branch.id}
                                                    onChange={() => setSelectedBranches(branch.id)}
                                                    className="form-radio my-2"
                                                />
                                                <span className="ml-2">{branch.name}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                {/* Select On-Site Status */}
                                <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                    <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                        On-Site Status
                                    </label>
                                    <div className="flex flex-col p-4">
                                        {onsiteStatuses.map(onsiteStatus => (
                                            <label className="inline-flex items-center" key={onsiteStatus.id}>
                                                <input
                                                    type="radio"
                                                    name="onsiteStatus"
                                                    value={onsiteStatus.id}
                                                    checked={selectedOnsiteStatuses === onsiteStatus.id}
                                                    onChange={() => setSelectedOnsiteStatuses(onsiteStatus.id)}
                                                    className="form-radio my-2"
                                                />
                                                <span className="ml-2">{onsiteStatus.name}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>
                            </div>
                            {error && <p className="text-red-500 text-sm pt-4">{error}</p>}
                            <div className='py-4 text-left'>
                                <button
                                    type="submit"
                                    className="w-56 bg-primary hover:bg-secondary text-white rounded-md py-3 m-0"
                                >
                                    Sent Invitation
                                </button>
                            </div>
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default AddMemberTest;