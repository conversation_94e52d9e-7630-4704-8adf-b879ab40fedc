import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditAvailableStatus from './EditAvailableStatus'; 

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const AvailableStatusList = () => {
    const [statuses, setStatuses] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [loading, setLoading] = useState(true); // Initially loading is true
    const [selectedStatusId, setSelectedStatusId] = useState(null);
    const [error, setError] = useState(null);

    // Update column names for Statuses
    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Availability Status", key: "name" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchStatuses = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}available_statuses`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();

                setStatuses(data.available_statuses.map(status => ({
                    id: status.id,
                    name: status.name,
                    created_by: status.created_by,
                    updated_by: status.updated_by,
                })));
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchStatuses();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}available_statuses/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete status: ' + response.statusText);
            }

            // Update the statuses list after deletion
            setStatuses(prevStatuses => prevStatuses.filter(status => status.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedStatusId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    // Show message when no statuses are available
    if (statuses.length === 0) {
        return <div className="text-gray-500">No data available</div>; // Show "No data available" if statuses array is empty
    }

    return (
        <div>
            <TableContent
                tableContent={statuses}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedStatusId}
            />
            {modalVisible && (
                <EditAvailableStatus
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    availableStatusId={selectedStatusId}
                />
            )}
        </div>
    );
};

export default AvailableStatusList;
