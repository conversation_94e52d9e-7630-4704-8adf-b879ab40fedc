<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use App\Models\Team;
use App\Models\Department;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

use Illuminate\Support\Facades\Log;


class TeamController extends Controller
{
     /**
     * Show all team with relevant relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $teams = Team::with([
                'users' => function($query) {
                    $query->select('users.id', 'users.fname', 'users.lname')
                          ->with(['designations:id,name']);
                },
                'departments:id,name'
            ])->get();

            // Process teams to add calculated fields
            $processedTeams = $teams->map(function ($team) {
                // Calculate total members
                $totalMembers = $team->users->count();

                // Find team lead from users with team_lead designation or manager role
                $teamLead = $team->users->first(function ($user) {
                    return $user->designations->contains(function ($designation) {
                        return in_array(strtolower($designation->name), [
                            'team lead', 'manager', 'team leader', 'lead'
                        ]);
                    });
                });

                // If no team lead found by designation, use the team_lead field or first user
                if (!$teamLead && $team->team_lead) {
                    $teamLeadName = $team->team_lead;
                } else if ($teamLead) {
                    $teamLeadName = $teamLead->fname . ' ' . $teamLead->lname;
                } else {
                    $teamLeadName = $team->users->first() ?
                        $team->users->first()->fname . ' ' . $team->users->first()->lname :
                        'Not Assigned';
                }

                // Calculate billable hours from time cards for this team
                $billableHours = \App\Models\TimeCard::where('team_id', $team->id)
                    ->where('hour', '!=', '00:00:00')
                    ->get()
                    ->sum(function ($timeCard) {
                        $parts = explode(':', $timeCard->hour);
                        $hours = (int) $parts[0];
                        $minutes = (int) $parts[1];
                        return $hours + ($minutes / 60);
                    });

                return [
                    'id' => $team->id,
                    'name' => $team->name,
                    'logo' => $team->logo,
                    'icon' => $team->icon,
                    'poc' => $team->poc,
                    'manager' => $team->manager,
                    'team_lead' => $team->team_lead,
                    'teamLead' => $teamLeadName,
                    'totalMembers' => $totalMembers,
                    'billableHours' => round($billableHours, 1),
                    'workday' => $team->workday,
                    'launch' => $team->launch,
                    'departments' => $team->departments,
                    'created_at' => $team->created_at,
                    'updated_at' => $team->updated_at
                ];
            });

            // Log the teams retrieved
            Log::info('All Teams Retrieved:', ['teams_count' => $processedTeams->count()]);

            return response()->json(['teams' => $processedTeams], 200);

        } catch (\Exception $e) {
            Log::error('Error fetching teams:', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to fetch teams data'], 500);
        }
    }

    // Filter logic for data table
    public function teamData(Request $request)
    {
        $query = Team::with(['creator', 'updater']);
    
        // Decode all input parameters to handle URL-encoded values
        $decodedTeamName = $request->filled('name') ? urldecode($request->input('name')) : null;
        $decodedCreatedAt = $request->filled('created_at') ? urldecode($request->input('created_at')) : null;
        $decodedUpdatedAt = $request->filled('updated_at') ? urldecode($request->input('updated_at')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;
    
        // Filtering by name
        if ($decodedTeamName) {
            $names = explode(',', $decodedTeamName);
            $query->where(function ($q) use ($names) {
                foreach ($names as $name) {
                    $q->orWhere('name', 'like', '%' . trim($name) . '%');
                }
            });
        }

        // Filtering by created_at
        if ($decodedCreatedAt) {
            $decodedCreatedAts = explode(',', $decodedCreatedAt);
            $query->where(function ($q) use ($decodedCreatedAts) {
                foreach ($decodedCreatedAts as $decodedCreatedAt) {
                    $q->orWhere('created_at', '=', trim($decodedCreatedAt));
                }
            });
        }
    
        // Filtering by updated_at
        if ($decodedUpdatedAt) {
            $decodedUpdatedAts = explode(',', $decodedUpdatedAt);
            $query->where(function ($q) use ($decodedUpdatedAts) {
                foreach ($decodedUpdatedAts as $decodedUpdated) {
                    $q->orWhere('updated_at', '=', trim($decodedUpdatedAt));
                }
            });
        }

        // Filtering by created_by
        if ($decodedCreatedBy) {
            $decodedCreateds = explode(',', $decodedCreatedBy);
            $query->where(function ($q) use ($decodedCreateds) {
                foreach ($decodedCreateds as $decodedCreated) {
                    $q->orWhere('created_by', '=', trim($decodedCreated));
                }
            });
        }
    
        // Filtering by updated_by
        if ($decodedUpdatedBy) {
            $decodedUpdateds = explode(',', $decodedUpdatedBy);
            $query->where(function ($q) use ($decodedUpdateds) {
                foreach ($decodedUpdateds as $decodedUpdated) {
                    $q->orWhere('updated_by', '=', trim($decodedUpdated));
                }
            });
        }
    
        // Global search logic
        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;
        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('name', 'like', '%' . $globalSearch . '%')
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('lname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    });

            });
        }
    
        // Sorting: Use query parameters 'sort_by' and 'order'
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');
    
        // Validate order parameter
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';
    
        $query->orderBy($sortBy, $order);
    
        // Pagination: Accept a 'per_page' parameter, defaulting to 15 if not provided
        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $productTypes = $query->paginate($perPage, ['*'], 'page', $page);
    
        return response()->json($productTypes, 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'column' and 'text' parameters from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }

        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the specified column
        $results = Team::with(['creator', 'updater']);

        if (strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);
            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        } else {
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }

    
    public function group(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
        
        // Build the query: Select the group column and the count of records in each group.
        $results = Team::with(['creator','updater']);
        $results->select($column, $column. ' as teams', \DB::raw("COUNT(*) as total"));
        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    }
    

    /**
     * Display the specified team.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the team by ID
        $team = Team::with([
            'users',
            'departments'
        ])->findOrFail($id);

        if (!$team) {
            return response()->json(['error' => 'Team not found.'], 404);
        }

        // Log the team retrieved
        Log::info('Team Retrieved:', ['team' => $team]);

        return response()->json(['team' => $team], 200);
    }


        /**
     * Create a new team by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createTeam(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();
    
        // Log the authenticated user's details
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);
    
        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255',
            'icon' => 'required|image|mimes:jpeg,png,jpg,gif,svg,webp|max:2000',
            'logo' => 'required|image|mimes:jpeg,png,jpg,gif,svg,webp|max:2000',
            'poc' => 'required|string|max:255',
            'manager' => 'required|string|max:255',
            'team_lead' => 'required|string|max:255',
            'launch' => 'nullable|string|max:255',
            'workday' => 'required|string',
            'department_id' => 'required|exists:departments,id',
        ]);

        // Process workday - decode JSON string to array for validation
        $workdayArray = [];
        if ($request->workday) {
            try {
                $workdayArray = json_decode($request->workday, true);
                if (!is_array($workdayArray)) {
                    throw new \Exception('Invalid workday format');
                }

                // Validate each day
                $validDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                foreach ($workdayArray as $day) {
                    if (!in_array($day, $validDays)) {
                        throw new \Exception('Invalid workday: ' . $day);
                    }
                }

                if (empty($workdayArray)) {
                    throw new \Exception('At least one workday must be selected');
                }
            } catch (\Exception $e) {
                return response()->json(['error' => 'Invalid workday data: ' . $e->getMessage()], 422);
            }
        }
    
        // Log the request data
        Log::info('Create Team Request:', ['request' => $request->all()]);
    
        // Allow multiple teams with the same name under different departments
        // Removed uniqueness restriction to allow multiple teams per department
    
        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            
            // Handle the logo and icon file upload
            $logoPath = null;
            $iconPath = null;
    
            // Handle logo upload
            if ($request->hasFile('logo')) {
                $logoPath = $request->file('logo')->store('images', 'public'); // Store the logo in the 'images' folder under public disk
            }
    
            // Handle icon upload
            if ($request->hasFile('icon')) {
                $iconPath = $request->file('icon')->store('images', 'public'); // Store the icon in the 'images' folder under public disk
            }
    
            // Create a new team with the full name and uploaded logo and icon file paths
            $team = Team::create([
                'name' => $request->name,
                'icon' => $iconPath, // Store the file path of the uploaded icon
                'logo' => $logoPath, // Store the file path of the uploaded logo
                'poc' => $request->poc,
                'manager' => $request->manager,
                'team_lead' => $request->team_lead,
                'workday' => $workdayArray, // Store as array (will be cast to JSON)
                'launch' => $request->launch,
                'created_by' => $authUser->id,
            ]);
    
            // Attach the team to the department via the pivot table `department_team`
            $department = Department::find($request->department_id);
            $department->teams()->attach($team->id);
    
            // Log team creation
            Log::info('Team Created:', ['team' => $team, 'department_id' => $request->department_id]);
    
            // Return the success response with team data
            return response()->json(['message' => 'Team created and associated with department successfully.', 'team' => $team], 201);
        }
    
        // Deny access for other roles
        Log::warning('Unauthorized Team Creation Attempt:', ['user_id' => $authUser->id]);
        return response()->json(['error' => 'You do not have permission to create a team.'], 403);
    }
    
    /**
     * Update an existing team by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateTeam(Request $request, $id)
    {
        try {
            // Get the authenticated user
            $authUser = $request->user();
            Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);
    
        // Log request files and input fields
        Log::info('Request Files:', ['files' => $request->files->all()]);
        Log::info('Name from input:', ['name' => $request->input('name')]);
        Log::info('Icon from input:', ['icon' => $request->input('icon')]);  // Base64 encoded icon
        Log::info('Logo from input:', ['logo' => $request->input('logo')]);  // Base64 encoded logo
        Log::info('POC from input:', ['poc' => $request->input('poc')]);
        Log::info('Manager from input:', ['manager' => $request->input('manager')]);
        Log::info('Team Lead from input:', ['team_lead' => $request->input('team_lead')]);
        Log::info('Launch from input:', ['launch' => $request->input('launch')]);
        Log::info('Department ID from input:', ['department_id' => $request->input('department_id')]);
    
        // Decode the base64 files (if provided)
        $logoPath = $this->decodeBase64Image($request->input('logo'), 'logo');
        $iconPath = $this->decodeBase64Image($request->input('icon'), 'icon');
    
        // Log the decoded file paths
        Log::info('Decoded Logo Path:', ['logo_path' => $logoPath]);
        Log::info('Decoded Icon Path:', ['icon_path' => $iconPath]);
    
        // Process workday - decode JSON string to array for validation
        $workdayArray = [];
        if ($request->workday) {
            try {
                $workdayArray = json_decode($request->workday, true);
                if (!is_array($workdayArray)) {
                    throw new \Exception('Invalid workday format');
                }

                // Validate each day
                $validDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                foreach ($workdayArray as $day) {
                    if (!in_array($day, $validDays)) {
                        throw new \Exception('Invalid workday: ' . $day);
                    }
                }
            } catch (\Exception $e) {
                return response()->json(['error' => 'Invalid workday data: ' . $e->getMessage()], 422);
            }
        }

        // Validate incoming request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:teams,name,' . $id,
            'icon' => 'nullable|string',  // Changed to string to handle Base64 input
            'logo' => 'nullable|string',  // Changed to string to handle Base64 input
            'poc' => 'nullable|string|max:255',
            'manager' => 'nullable|string|max:255',
            'team_lead' => 'nullable|string|max:255',
            'workday' => 'nullable|string',
            'launch' => 'nullable|string|max:255',
            'department_id' => 'required|exists:departments,id',
        ]);
    
        // Check if the user has the appropriate role
        if (!$authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            Log::warning('Unauthorized Team Update Attempt:', ['user_id' => $authUser->id]);
            return response()->json(['error' => 'You do not have permission to update this team.'], 403);
        }
    
        // Retrieve the team by ID
        $team = Team::find($id);
        if (!$team) {
            Log::error('Team Not Found:', ['team_id' => $id]);
            return response()->json(['error' => 'Team not found.'], 404);
        }
    
        // Log existing team data before update
        Log::info('Existing Team Data:', ['team' => $team->toArray()]);
    
        // Handle file uploads (if provided)
        $logoPath = $logoPath ?: $team->logo;  // Use the existing path if no new logo is provided
        $iconPath = $iconPath ?: $team->icon;  // Use the existing path if no new icon is provided
    
        // Update team data
        $team->update([
            'name' => $request->input('name', $team->name),
            'icon' => $iconPath,
            'logo' => $logoPath,
            'poc' => $request->input('poc', $team->poc),
            'manager' => $request->input('manager', $team->manager),
            'team_lead' => $request->input('team_lead', $team->team_lead),
            'workday' => !empty($workdayArray) ? $workdayArray : $team->workday,
            'launch' => $request->input('launch', $team->launch),
            'updated_by' => $authUser->id,
        ]);
    
        // Log the updated team data
        Log::info('Updated Team:', ['team' => $team->toArray()]);
    
        // Attach the team to the department if not already attached
        $department = Department::find($request->department_id);
        if (!$department->teams->contains($team->id)) {
            $department->teams()->attach($team->id);
            Log::info('Team Attached to Department:', ['department_id' => $department->id, 'team_id' => $team->id]);
        }
    
        // Return success response with updated team data
        return response()->json([
            'message' => 'Team updated and attached to department successfully.',
            'team' => $team
        ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Team Update Validation Error:', ['errors' => $e->errors(), 'team_id' => $id]);
            return response()->json(['error' => 'Validation failed.', 'details' => $e->errors()], 422);
        } catch (\Exception $e) {
            Log::error('Team Update Error:', ['error' => $e->getMessage(), 'team_id' => $id, 'userId' => $authUser->id ?? null]);
            return response()->json(['error' => 'Failed to update team.', 'message' => $e->getMessage()], 500);
        }
    }
    
    // Helper function to decode base64 image data and store it
    protected function decodeBase64Image($base64String, $type)
    {
        if (!$base64String) {
            return null; // No file to handle
        }
    
        // Match and extract the image data and type
        if (preg_match('#^data:image/(\w+);base64,#i', $base64String, $matches)) {
            $imageType = $matches[1]; // Extracted image type (e.g., 'jpeg', 'png')
    
            // Remove the base64 header to get raw image data
            $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64String));
    
            // Check if decoding was successful
            if ($imageData === false) {
                return null; // If base64 decoding fails, return null
            }
    
            // Generate a unique file name with the appropriate extension
            $fileName = $type . '-' . uniqid() . '.' . $imageType; // Use dynamic extension based on image type
            $filePath = storage_path('app/public/images/' . $fileName);
    
            // Save the file to storage
            file_put_contents($filePath, $imageData);
    
            return 'images/' . $fileName;
        }
    
        return null; // If the base64 string does not match the expected format
    }
    

    /**
     * Delete a team by Super Admin or Admin.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteTeam($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the team
            $team = Team::findOrFail($id);

            // Delete the team
            $team->delete();

            return response()->json(['message' => 'Team deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete this team.'], 403);
    }

}
