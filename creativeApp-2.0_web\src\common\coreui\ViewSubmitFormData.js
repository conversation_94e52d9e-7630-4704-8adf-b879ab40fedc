import React from "react";

export const ViewSubmitFormData = ({ item,  setViewData, setConfirmation, handleSubmit, title="" }) => {
  return (
    <div
      className="fixed inset-0 z-[500] flex items-center justify-center bg-gray-800 bg-opacity-50"
      // onClick={() => setViewData(null)}
    >
      <div
        className="relative bg-white shadow-lg max-w-[50%] w-full   rounded-xl"
        onClick={(e) => e.stopPropagation()}
      >
        <table className="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 ">
        <thead>
            <tr className=" dark:border-gray-700 hover:bg-gray-50 sentence-case dark:hover:bg-gray-800 dark:hover:text-white">
              <th
                colSpan={2}
                scope="row"
                className="pe-3 py-3 font-bold uppercase justify-center text-center text-gray-900 whitespace-nowrap bg-gray-200 rounded-t-xl  dark:text-white dark:bg-gray-800"
              >
                <div className="flex justify-center px-4">
                    <span className="text-lg font-semibold">
                    {title || "Confirm Submission Data"} 
                    </span>
                    <button
                        className="absolute top-0 right-2 flex w-[20px] h-[20px] ms-2 text-center my-3 justify-center items-center  text-sm font-medium focus:outline-none bg-white rounded-full border border-red-600 text-red-600 hover:bg-red-500 hover:text-white focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                        onClick={() => setViewData(null)}
                    >
                        <span className="material-symbols-outlined text-sm">
                        close
                        </span>
                    </button>
                </div>
              </th>
            </tr>
          </thead>
          <tbody >
          <div className="max-h-[80vh] w-full overflow-y-auto">
            {item && Object.keys(item).map(
              (column, index) =>
                (
                  <tr key={"view-form-submit-tr-"+index} className="w-full inline-table border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 sentence-case dark:hover:bg-gray-800 dark:hover:text-white">
                    <th
                      scope="row"
                      className="w-4/12 px-6 py-4 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800"
                    >
                      {column}
                    </th>
                    <td className="w-auto px-6 py-4 sentence-case ">
                    {item[column] ? item[column].toString() : ""}
                    </td>
                  </tr>
                )
            )}
            </div>
          </tbody>
          
          <tfoot>
            <tr className=" dark:border-gray-700 hover:bg-gray-50 sentence-case dark:hover:bg-gray-800 dark:hover:text-white ">
              <th
                colSpan={2}
                scope="row"
                className="rounded-b-xl  pe-3 py-4 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800"
              >
                <div className="flex justify-start px-4">
                  {handleSubmit && 
                  <button
                    className="flex w-auto h-[30px] text-center my-3 justify-center items-center py-1 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-primary hover:bg-secondary text-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                    onClick={() => {
                      setConfirmation(true);
                      handleSubmit()
                    }}
                  >
                    <span className="material-symbols-outlined me-1  text-sm">
                    save
                    </span>
                    Confirm
                  </button>
                  }

                  {handleSubmit && 
                  <button
                    className="flex w-auto h-[30px] text-center ms-2  my-3 justify-center items-center py-1 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                    onClick={() => setViewData(null)}
                  >
                    <span className="material-symbols-outlined me-1  text-sm">
                    stylus_note
                    </span>
                    Edit
                  </button>
                  }
                  
                  
                </div>
              </th>
            </tr>
          </tfoot>
        
        </table>
      </div>
    </div>
  );
};
