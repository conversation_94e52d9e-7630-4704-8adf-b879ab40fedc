<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('give_feedbacks', function (Blueprint $table) {
            $table->id();
            $table->string('subject'); // Subject field
            $table->text('message');   // Message field
            $table->string('created_by');
            $table->string('updated_by')->nullable();
            $table->timestamps(); // Created_at and updated_at fields
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('give_feedbacks');
    }
};
