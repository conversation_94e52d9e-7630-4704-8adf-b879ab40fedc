import React, { useEffect, useState } from 'react';
import { alertMessage } from '../../common/coreui';
import Select from 'react-select';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const isTokenValid = () => {
  const token = localStorage.getItem('token');
  return token !== null;
};

const AddTeam = ({ isVisible, setVisible }) => {
  const [users, setUsers] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [teams, setTeams] = useState([]);
  const [teamName, setTeamName] = useState('');
  const [icon, setIcon] = useState(null);
  const [logo, setLogo] = useState(null);
  const [poc, setPoc] = useState('');
  const [manager, setManager] = useState('');
  const [teamLead, setTeamLead] = useState('');
  const [launch, setLaunch] = useState('');
  const [workday, setWorkday] = useState([]);
  const [departmentId, setDepartmentId] = useState('');
  const [error, setError] = useState('');
  const [loggedInUser, setLoggedInUser] = useState(null);
  const [loading, setLoading] = useState(false);

  // React Select states
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [selectedPoc, setSelectedPoc] = useState(null);
  const [selectedManager, setSelectedManager] = useState(null);
  const [selectedTeamLead, setSelectedTeamLead] = useState(null);

  // Days of the week for multi-select
  const daysOfWeek = [
    { value: 'Monday', label: 'Monday' },
    { value: 'Tuesday', label: 'Tuesday' },
    { value: 'Wednesday', label: 'Wednesday' },
    { value: 'Thursday', label: 'Thursday' },
    { value: 'Friday', label: 'Friday' },
    { value: 'Saturday', label: 'Saturday' },
    { value: 'Sunday', label: 'Sunday' }
  ];

  // Handle workday selection
  const handleWorkdayChange = (selectedOptions) => {
    const selectedValues = selectedOptions ? selectedOptions.map(option => option.value) : [];
    setWorkday(selectedValues);
  };

  // Create options for React Select dropdowns
  const departmentOptions = departments.map(dept => ({
    value: dept.id,
    label: dept.name
  }));

  // Filter users based on responsibility levels
  const getUserOptions = (allowedRoles) => {
    return users
      .filter(user => {
        // Check if user has any of the allowed roles
        const hasValidResponsibility = user.resource_types?.some(rt => 
          allowedRoles.includes(rt)
        );
        // Ensure user has a valid name
        const hasValidName = user.fullName && user.fullName.trim() !== '';
        return hasValidResponsibility && hasValidName;
      })
      .map(user => ({
        value: user.fullName,
        label: user.fullName
      }));
  };

  // Updated filtering based on your requirements
  const pocOptions = getUserOptions(['Team Lead', 'Manager', 'HOD']);
  const managerOptions = getUserOptions(['Manager', 'HOD']);
  const teamLeadOptions = getUserOptions(['Team Lead', 'Manager', 'HOD']);

  useEffect(() => {
    const fetchData = async () => {
      if (!isVisible || !isTokenValid()) {
        return;
      }

      const token = localStorage.getItem('token');

      try {
        setLoading(true);
        setError('');

        // Fetch Users
        const usersResponse = await fetch(`${API_URL}/users`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!usersResponse.ok) {
          throw new Error('Failed to fetch users');
        }

        const usersData = await usersResponse.json();
        const processedUsers = usersData.map(user => ({
          id: user.id,
          fullName: `${(user.fname || '').trim()} ${(user.lname || '').trim()}`.trim(),
          roles: Array.isArray(user.roles) ? user.roles.map(r => (r.name || '').trim()) : [],
          resource_types: Array.isArray(user.resource_types) ? user.resource_types.map(rt => (rt.name || '').trim()) : [],
        }));
        setUsers(processedUsers);

        // Fetch Departments
        const departmentsResponse = await fetch(`${API_URL}/departments`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!departmentsResponse.ok) {
          throw new Error('Failed to fetch departments');
        }

        const departmentsData = await departmentsResponse.json();
        setDepartments(departmentsData.departments || []);
        
        // Fetch Teams for duplicate checking
        const teamsResponse = await fetch(`${API_URL}/teams`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!teamsResponse.ok) {
          throw new Error('Failed to fetch teams');
        }

        const teamsData = await teamsResponse.json();
        setTeams(teamsData.teams || []);
      } catch (error) {
        setError(error.message);
        console.error('Error fetching ', error);
      } finally {
        setLoading(false);
      }
    };

    if (isVisible) {
      fetchData();
    }
  }, [isVisible]);

  // Fetch logged-in user data (user_id)
  useEffect(() => {
    const userId = localStorage.getItem('user_id');
    if (userId) {
      setLoggedInUser(userId);
    }
  }, []);

  const handleSubmit = async (event) => {
    event.preventDefault();

    // Get user_id from localStorage for 'created_by'
    const createdBy = loggedInUser;

    if (!createdBy) {
      setError('User is not logged in.');
      return;
    }

    const trimmedTeamName = teamName.trim();

    // Validate required fields
    if (!trimmedTeamName) {
      setError('Team name is required.');
      return;
    }

    if (!selectedDepartment?.value) {
      setError('Department is required.');
      return;
    }

    if (!icon) {
      setError('Icon is required.');
      return;
    }

    if (!logo) {
      setError('Logo is required.');
      return;
    }

    if (!selectedPoc?.value) {
      setError('Point of Contact is required.');
      return;
    }

    if (!selectedManager?.value) {
      setError('Manager is required.');
      return;
    }

    if (!selectedTeamLead?.value) {
      setError('Team Lead is required.');
      return;
    }

    // Validate workdays
    if (workday.length === 0) {
      setError('At least one workday must be selected.');
      return;
    }

    // Check if the team already exists (case insensitive)
    const teamExists = teams.some(team => 
      team.name.toLowerCase().trim() === trimmedTeamName.toLowerCase()
    );

    if (teamExists) {
      setError('Team already exists. Please add a different team.');
      setTimeout(() => setError(''), 3000);
      return;
    }

    setError(''); // Clear any previous error
    setLoading(true);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token is missing.');
      }

      const formData = new FormData();
      formData.append('name', trimmedTeamName);
      formData.append('icon', icon);
      formData.append('logo', logo);
      formData.append('poc', selectedPoc?.value || poc);
      formData.append('manager', selectedManager?.value || manager);
      formData.append('team_lead', selectedTeamLead?.value || teamLead);
      formData.append('workday', JSON.stringify(workday));
      formData.append('launch', launch || ''); // Allow empty launch date
      formData.append('department_id', selectedDepartment?.value || departmentId);
      formData.append('created_by', createdBy);

      const response = await fetch(`${API_URL}/teams`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        let errorMessage = `Failed to save team: ${response.status} ${response.statusText}`;
        try {
          // Clone the response to avoid "body stream already read" error
          const responseClone = response.clone();
          const errorData = await responseClone.json();
          errorMessage = errorData.error || errorData.message || errorMessage;
        } catch (parseError) {
          // If response is not JSON, get text from original response
          try {
            const errorText = await response.text();
            errorMessage = errorText || 'Server returned an error';
          } catch (textError) {
            console.error('Could not parse error response:', textError);
          }
        }
        throw new Error(errorMessage);
      }

      await response.json();
      alertMessage('success');

      // Reset form
      setTeamName('');
      setIcon(null);
      setLogo(null);
      setPoc('');
      setManager('');
      setTeamLead('');
      setLaunch('');
      setWorkday([]);
      setDepartmentId('');
      setSelectedDepartment(null);
      setSelectedPoc(null);
      setSelectedManager(null);
      setSelectedTeamLead(null);

      // Close modal
      setVisible(false);
    } catch (error) {
      setError(error.message || 'Failed to add team.');
      console.error('Error adding team:', error);
    } finally {
      setLoading(false);
    }
  };

  // Convert workday array to Select options for display
  const workdayOptions = workday.map(day => ({
    value: day,
    label: day
  }));

  if (!isVisible) return null;

  return (
    <>
      <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
        <div className="bg-white rounded-lg shadow-md w-full max-w-3xl relative overflow-y-auto h-[80vh] mt-10 scrollbar-vertical">
          <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
            <h4 className="text-xl text-left font-medium text-gray-800">Add New Team</h4>
            <button 
              onClick={() => setVisible(false)}
              className="text-3xl text-gray-500 hover:text-gray-800"
            >
              &times;
            </button>
          </div>
          
          <form onSubmit={handleSubmit} className="text-left p-6">
            <div className="mb-4">
              <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">
                Department *
              </label>
              <Select
                options={departmentOptions}
                value={selectedDepartment}
                onChange={(option) => {
                  setSelectedDepartment(option);
                  setDepartmentId(option?.value || '');
                }}
                placeholder="Select Department"
                className="w-full"
                isSearchable
                isDisabled={loading}
              />
            </div>
            
            <div className="mb-4">
              <label htmlFor="teamName" className="block text-sm font-medium text-gray-700 pb-4">
                Team Name *
              </label>
              <input
                type="text"
                id="teamName"
                value={teamName}
                onChange={(e) => {
                  setTeamName(e.target.value);
                  if (error) setError(''); // Clear error when user starts typing
                }}
                required
                disabled={loading}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="icon" className="block text-sm font-medium text-gray-700 pb-4">
                Icon *
              </label>
              <input
                type="file"
                id="icon"
                onChange={(e) => setIcon(e.target.files[0])}
                accept="image/*"
                required
                disabled={loading}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
              />
              {icon && (
                <div className="mt-2">
                  <img 
                    src={URL.createObjectURL(icon)} 
                    alt="Icon Preview" 
                    className="w-16 h-16 object-contain"
                  />
                </div>
              )}
            </div>

            <div className="mb-4">
              <label htmlFor="logo" className="block text-sm font-medium text-gray-700 pb-4">
                Logo *
              </label>
              <input
                type="file"
                id="logo"
                onChange={(e) => setLogo(e.target.files[0])}
                accept="image/*"
                required
                disabled={loading}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
              />
              {logo && (
                <div className="mt-2">
                  <img 
                    src={URL.createObjectURL(logo)} 
                    alt="Logo Preview" 
                    className="w-32 h-16 object-contain"
                  />
                </div>
              )}
            </div>

            <div className="mb-4">
              <label htmlFor="poc" className="block text-sm font-medium text-gray-700 pb-4">
                Point of Contact *
              </label>
              <Select
                options={pocOptions}
                value={selectedPoc}
                onChange={(option) => {
                  setSelectedPoc(option);
                  setPoc(option?.value || '');
                }}
                placeholder="Select POC"
                className="w-full"
                isSearchable
                isDisabled={loading}
                noOptionsMessage={() => "No options available"}
              />
            </div>

            <div className="mb-4">
              <label htmlFor="manager" className="block text-sm font-medium text-gray-700 pb-4">
                Manager *
              </label>
              <Select
                options={managerOptions}
                value={selectedManager}
                onChange={(option) => {
                  setSelectedManager(option);
                  setManager(option?.value || '');
                }}
                placeholder="Select Manager"
                className="w-full"
                isSearchable
                isDisabled={loading}
                noOptionsMessage={() => "No managers found"}
              />
            </div>

            <div className="mb-4">
              <label htmlFor="teamLead" className="block text-sm font-medium text-gray-700 pb-4">
                Team Lead *
              </label>
              <Select
                options={teamLeadOptions}
                value={selectedTeamLead}
                onChange={(option) => {
                  setSelectedTeamLead(option);
                  setTeamLead(option?.value || '');
                }}
                placeholder="Select Team Lead"
                className="w-full"
                isSearchable
                isDisabled={loading}
                noOptionsMessage={() => "No team leads found"}
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 pb-4">
                Work Days *
              </label>
              <Select
                isMulti
                options={daysOfWeek}
                value={workdayOptions}
                onChange={handleWorkdayChange}
                placeholder="Select Work Days"
                className="w-full"
                isDisabled={loading}
                noOptionsMessage={() => "No options available"}
              />
              {workday.length > 0 && (
                <p className="text-xs text-gray-500 mt-2">
                  Selected: {workday.join(', ')}
                </p>
              )}
            </div>

            <div className="mb-4">
              <label htmlFor="launch" className="block text-sm font-medium text-gray-700 pb-4">
                Launch Date
              </label>
              <input
                type="date"
                id="launch"
                value={launch}
                onChange={(e) => setLaunch(e.target.value)}
                disabled={loading}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
              />
            </div>

            {error && <p className="text-red-500 text-sm mb-4">{error}</p>}

            <div className="text-left pt-6">
              <button
                type="submit"
                disabled={loading}
                className={`w-56 py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4 ${
                  loading 
                    ? 'bg-gray-400 cursor-not-allowed' 
                    : 'bg-primary hover:bg-secondary text-white'
                }`}
              >
                {loading ? (
                  <>
                    <span className="material-symbols-rounded animate-spin text-white text-xl font-regular">
                      progress_activity
                    </span>
                    Adding...
                  </>
                ) : (
                  <>
                    <span className="material-symbols-rounded text-white text-xl font-regular">add_circle</span>
                    Add Team
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default AddTeam;