import React, { useEffect, useState } from 'react';
import { alertMessage } from '../../common/coreui';
import Select from 'react-select';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditDepartment = ({ isVisible, setVisible, dataItemsId }) => {
    const [departmentName, setDepartmentName] = useState('');
    const [hod, setHod] = useState('');
    const [launchDate, setLaunchDate] = useState('');
    const [users, setUsers] = useState([]);
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(true);

    const [loggedInUser, setLoggedInUser] = useState(null);

    // React Select states
    const [selectedHod, setSelectedHod] = useState(null);

    // Create options for HOD dropdown - filter ONLY for 'HOD' (not 'Manager')
    const hodOptions = users
        .filter(user => {
            const hasValidResponsibility = user.resource_types?.some(rt => rt === 'HOD'); // Fixed: Only 'HOD', not 'Manager'
            const hasValidName = user.fullName && user.fullName.trim() !== '';
            return hasValidResponsibility && hasValidName;
        })
        .map(user => ({
            value: user.fullName,
            label: user.fullName
        }));

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    // Fetch users and department details (merged into one effect, no duplication)
    useEffect(() => {
        const fetchData = async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                setLoading(false);
                return;
            }

            try {
                setLoading(true); // Start loading

                // Fetch users
                const usersResponse = await fetch(`${API_URL}/users`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (usersResponse.ok) {
                    const usersData = await usersResponse.json();
                    console.log('EditDepartment Users data:', usersData); // Debug log
                    setUsers(usersData.map(user => ({
                        id: user.id,
                        fullName: `${(user.fname || '').trim()} ${(user.lname || '').trim()}`.trim(),
                        roles: Array.isArray(user.roles) ? user.roles.map(r => (r.name || '').trim()) : [],
                        resource_types: Array.isArray(user.resource_types) ? user.resource_types.map(rt => (rt.name || '').trim()) : [],
                    })));
                }

                // Fetch department details if editing
                if (dataItemsId) {
                    const deptResponse = await fetch(`${API_URL}/departments/${dataItemsId}`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    });

                    if (deptResponse.ok) {
                        const deptData = await deptResponse.json();
                        console.log('Department data:', deptData); // Added debug log for department

                        // Handle both flat and nested structures (fixes race condition and structure mismatch)
                        const dept = deptData.department || deptData; // Fallback to flat if not nested

                        setDepartmentName(dept.name || '');
                        setHod(dept.hod || '');
                        setLaunchDate(dept.launch_date || '');

                        // Set React Select default value for HOD
                        if (dept.hod) {
                            setSelectedHod({ value: dept.hod, label: dept.hod });
                        }
                    } else {
                        throw new Error('Failed to fetch department: ' + deptResponse.statusText);
                    }
                }
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        if (isVisible && dataItemsId) {
            fetchData();
        }
    }, [isVisible, dataItemsId]); // Single effect, runs when modal opens or ID changes

    const handleSubmit = async (event) => {
        event.preventDefault(); // Prevent default form submission behavior
        const token = localStorage.getItem('token');

        const updatedBy = loggedInUser;

        if (!updatedBy) {
            setError('User is not logged in.');
            return;
        }
        
        if (!token) {
            setError('Authentication token is missing.');
            return; // Exit if token is not available
        }
    
        try {
            const response = await fetch(`${API_URL}/departments/${dataItemsId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: departmentName.trim(), // The updated department name
                    hod: selectedHod?.value || hod, // The updated HOD
                    launch_date: launchDate, // The updated launch date
                    updated_by: updatedBy, // Set updated_by as user ID (from localStorage)
                }),
            });
    
            if (!response.ok) {
                throw new Error('Failed to update department: ' + response.statusText);
            }
    
            const result = await response.json();

            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Department updated successfully.',
            });
    
            // Close the modal after a short delay
            setTimeout(() => {
                setVisible(false);
            }, 1000);
            
        } catch (error) {
            alertMessage('error'); // Display error message if something goes wrong
        }
    };
    

    if (!isVisible) return null; // Don't render the modal if not visible

    return (
        <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
            <div className="bg-white rounded-lg shadow-md w-full max-w-3xl relative overflow-y-auto h-[80vh] mt-10 scrollbar-vertical">
                <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                    <h4 className="text-xl text-left font-medium text-gray-800">Edit Department</h4>
                    <button onClick={() => setVisible(false)}
                        className="text-3xl text-gray-500 hover:text-gray-800"
                    >
                        &times;
                    </button>
                </div>
                <form onSubmit={handleSubmit} className="text-left p-6">
                    <div className=''>
                        <div className="mb-4">
                            <label htmlFor="name" className="block text-sm font-medium text-gray-700 pb-4">
                                Department Name
                            </label>
                            <input
                                type="text"
                                id="name"
                                value={departmentName}
                                onChange={(e) => setDepartmentName(e.target.value)}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            />
                            {error && <p className="text-red-500 text-sm">{error}</p>}
                        </div>

                        <div className="mb-4">
                            <label htmlFor="hod" className="block text-sm font-medium text-gray-700 pb-4">
                                Head of Department (HOD)
                            </label>
                            <Select
                                options={hodOptions}
                                value={selectedHod}
                                onChange={(option) => {
                                    setSelectedHod(option);
                                    setHod(option?.value || '');
                                }}
                                placeholder="Select HOD"
                                className="w-full"
                                isSearchable
                                noOptionsMessage={() => "No HODs found"}
                            />
                        </div>

                        <div className="mb-4">
                            <label htmlFor="launchDate" className="block text-sm font-medium text-gray-700 pb-4">
                                Launch Date
                            </label>
                            <input
                                type="date"
                                id="launchDate"
                                value={launchDate}
                                onChange={(e) => setLaunchDate(e.target.value)}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>
                    </div>

                    <div className='text-left pt-6'>
                        <button
                            type="submit"
                            className="w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4"
                            disabled={loading}
                        >
                            <span className="material-symbols-rounded text-white text-xl font-regular">edit</span>
                            {loading ? 'Updating...' : 'Update Department'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default EditDepartment;