import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditDepartment from './EditDepartment';

const isTokenValid = () => {
    const token = localStorage.getItem('token');

    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const DepartmentList = () => {
    const [departments, setDepartments] = useState([]); // Changed variable name to departments
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedDepartmentId, setSelectedDepartmentId] = useState(null);
    const [error, setError] = useState(null);

    // Update column names for departments
    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Department Name", key: "name" }, // Updated to include fullName
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchDepartments = async () => { // Updated function name for clarity
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}departments`, { // Updated the API endpoint
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                console.log('Departments', data);
                
                setDepartments(data.departments.map(department => ({
                    id: department.id,
                    name: department.name,
                    created_by: department.created_by,
                    updated_by: department.updated_by,
                }))); 
            } catch (error) {
                setError(error.message);
            }
        };

        fetchDepartments();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}departments/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete team: ' + response.statusText);
            }

            // Update the departments list after deletion
            setDepartments(prevDepartments => prevDepartments.filter(department => department.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedDepartmentId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={departments}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible} // Pass modal state functions if needed
                setSelectedServiceId={setSelectedDepartmentId}
            />
            {modalVisible && (
                <EditDepartment
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    departmentId={selectedDepartmentId}
                />
            )}
        </div>
    );
};

export default DepartmentList;
