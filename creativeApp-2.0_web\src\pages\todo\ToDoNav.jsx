import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

// Check if the token is available and valid
const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const TodoNav = ({ onSelectToDoGroup, onShowAllToDo, fetchData, setFetchData }) => {
    const location = useLocation(); // Track current location to highlight active menu
    const [todoTag, setTodoTag] = useState([]); // Store fetched tags
    const [error, setError] = useState(null); // Handle error messages
    const [loading, setLoading] = useState(true); // Loading state
    const [activeMenu, setActiveMenu] = useState('all'); // Highlight active menu item

    // Fetch tag from API when component mounts
    useEffect(() => {
        const fetchTags = async () => {
            // if (!fetchData) return; // Do nothing if we shouldn't fetch
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token'); // Get token from local storage

            try {
                const response = await fetch(`${API_URL}todo-tags`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch tags: ' + response.statusText);
                }

                const data = await response.json();
                console.log(data);
                setTodoTag(data.todoTags); // Set the teams fetched from API
                // setFetchData(false); // Set the fetch data flag to false
            } catch (error) {
                setError(error.message); // Catch and display errors
            } finally {
                setLoading(false); // Set loading to false after fetching data
            }
        };

        fetchTags(); // Call the fetchTags function
    }, [activeMenu, fetchData]);

    // Handle team click: Invoke the onSelectToDoGroup callback passed as a prop
    const handleToDoCategoryClick = (todoGroup) => {
        onSelectToDoGroup(todoGroup);
        setActiveMenu(todoGroup);
    };

    // Function to determine the active link based on current location
    const isActive = (path) => {
        return location.pathname === path ? 'bg-white text-gray-900' : 'text-gray-700';
    };
    const todoNavName = [{ nav: 'Today', icon: 'today', total: 3, }, { nav: 'Tomorrow', icon: 'insert_invitation', total: 5, }, { nav: 'This Week', icon: 'date_range', total: 10, }, { nav: 'This Month', icon: 'calendar_month', total: 35, }, { nav: 'Completed Task', icon: 'today', total: 75, }, { nav: 'Failed Task', icon: 'sms_failed', total: 5, }]

    // Show loading state, or error if there's an issue fetching data
    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }
    return (
        <div className="dark:bg-gray-900 rounded-xl">
            <nav>
                <ul className="flex flex-col text-left">
                    {/* "All todo" button */}
                    <h5 className="px-2 w-full text-left font-bold text-sm text-gray-700 pb-2">
                        Task List
                    </h5>
                    <li className={`${activeMenu == 'all' ? "bg-cyan-50" : 'bg-white'}`}>
                        <button
                            className={`py-2 px-2 hover:text-gray-900 flex justify-between w-full items-center gap-4`}
                            onClick={() => handleToDoCategoryClick(`all`)}
                        >
                            <div className='flex items-center gap-2'>
                                <span className="material-symbols-rounded text-xl">event_note</span>
                                <div className="text-sm">All To-do</div>
                            </div>

                            <span className='text-xs bg-gray-100 px-2 py-1 rounded rounded-md'>9</span>
                        </button>
                    </li>


                    {/* "today todo" button */}
                    {todoNavName.map((navList) => (
                        <li className={`${activeMenu == `${navList.nav}` ? "bg-cyan-50" : 'bg-white'}`} key={navList.nav}>
                            <button
                                className={`py-2 px-2 hover:text-gray-900 flex justify-between w-full items-center gap-4 ${isActive('/contacts/frequent')}`}
                                onClick={() => handleToDoCategoryClick(`${navList.nav}`)} // Pass 'today' as todoGroup
                            >
                                <div className='flex items-center gap-2'>
                                    <span className="material-symbols-rounded text-xl">{navList.icon}</span>
                                    <div className="text-sm">{navList.nav}</div>
                                </div>
                                <span className='text-xs bg-gray-100 px-2 py-1 rounded rounded-md'>{navList.total}</span>
                            </button>
                        </li>
                    ))}

                    {/* Dynamically display tag */}
                    {todoTag.length > 0 && (
                        <>
                            <h5 className="w-full text-left font-bold text-sm text-gray-700 mt-2 pt-4 pb-2 border-t border-gray-300 mb-3">
                                Tags <span className="text-xs bg-gray-100 px-2 py-1 rounded rounded-full" >{todoTag.length}</span>
                            </h5>
                            <div className='flex flex-row flex-wrap gap-4'>
                                {todoTag.map((tag) => (
                                    <li className="text-xs bg-gray-100 px-3 py-2 rounded rounded-full" key={tag.id}>
                                        <button onClick={() => handleToDoCategoryClick(tag.id)} // Pass specific team ID
                                        >
                                            {tag.name}
                                        </button>
                                    </li>
                                ))}
                            </div>
                        </>
                    )}
                </ul>
            </nav>
        </div>
    );
}

export default TodoNav;