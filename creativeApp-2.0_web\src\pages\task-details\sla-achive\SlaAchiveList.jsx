import React, { useEffect, useState } from 'react';
import TableContent from '../../../common/table/TableContent';
import EditSlaAchive from './EditSlaAchive';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const SlaAchieveList = () => {
    const [slaAchieves, setSlaAchieves] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedSlaAchieveId, setSelectedSlaAchieveId] = useState(null);
    const [error, setError] = useState(null);

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Product Type Name", key: "name" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchSlaAchieves = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}sla-achieves`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                console.log('SLA Achieve', data);

                setSlaAchieves(data.slaAchieves.map(slaAchieve => ({
                    id: slaAchieve.id,
                    department: slaAchieve.department,
                    team: slaAchieve.team,
                    name: slaAchieve.name,
                    created_by: slaAchieve.created_by,
                    updated_by: slaAchieve.updated_by,
                })));
            } catch (error) {
                setError(error.message);
            }
        };

        fetchSlaAchieves();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}sla-achieve/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete SLA achieve: ' + response.statusText);
            }

            // Update the SLA achieves list after deletion
            setSlaAchieves(prevSlaAchieves => prevSlaAchieves.filter(slaAchieve => slaAchieve.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedSlaAchieveId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={slaAchieves}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedSlaAchieveId}
            />
            {modalVisible && (
                <EditSlaAchive
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    slaAchieveId={selectedSlaAchieveId}
                />
            )}
        </div>
    );
};

export default SlaAchieveList;
