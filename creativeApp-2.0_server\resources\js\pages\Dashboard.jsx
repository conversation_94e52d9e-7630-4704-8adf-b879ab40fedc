import React from 'react';
import WelcomeCard from '../components/Dashboard/WelcomeCard/WelcomeCard';

/**
 * NOTE:
 * This is a new Dashboard page component.
 * It's intended to be the main view for authenticated users.
 * The `user` prop is currently hardcoded but should be fetched from the backend
 * and passed down from a higher-level component (e.g., App.jsx).
 */

const Dashboard = () => {
    // Placeholder for the authenticated user data.
    // In a real application, this would come from an authentication context or a prop.
    const authenticatedUser = {
        name: 'Rashed',
        profile_photo_url: 'https://via.placeholder.com/150', // Replace with a real image URL if available
    };

    return (
        <div className="p-4 sm:p-6 lg:p-8 bg-gray-100 min-h-screen">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Left Column */}
                <div className="lg:col-span-1">
                    {/* Welcome Card */}
                    <div className="h-full">
                        <WelcomeCard user={authenticatedUser} />
                    </div>
                </div>

                {/* Right Column (for other dashboard components) */}
                <div className="lg:col-span-2">
                    {/* Other dashboard cards will go here */}
                    <div className="bg-white p-6 rounded-xl shadow-lg">
                        <h2 className="text-xl font-bold">Other Content</h2>
                        <p>Additional dashboard widgets can be placed here.</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Dashboard;