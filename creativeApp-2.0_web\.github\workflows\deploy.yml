name: Deploy React App (Development)

on:
  push:
    branches:
      - development
  workflow_dispatch:

jobs:
  deploy-react:
    runs-on: self-hosted

    env:
      DEPLOY_PATH: /var/www/html/creativeapp-v2/creativeApp-2.0_web
      REACT_APP_GOOGLE_API_KEY: AIzaSyAILbHL3UrGMpudDr4scErfiAr3syzSZLI
      REACT_APP_MODE: production
      REACT_APP_BASE_API_URL: https://creative.sebpo.net/api/
      REACT_APP_BASE_STORAGE_URL: https://creative.sebpo.net/backend/storage

    steps:
      # Step 1: Checkout repository
      - name: Checkout repository
        uses: actions/checkout@v4

      # # Step 2: Setup Node.js
      # - name: Setup Node.js
      #   uses: actions/setup-node@v4
      #   with:
      #     node-version: '20'

      # # Step 3: Install dependencies (always fresh)
      # - name: Install dependencies
      #   run: npm install --force

      # Step 4: Debugging info
      - name: Information
        run: |
          echo "Current directory:"
          pwd
          echo "Contents:"
          ls -la

      # Step 5: Build React app with env variables
      # - name: Build React app
      #   run: |
      #     echo "Starting React build..."
      #     npm run build --force
      #     echo "Build completed. Contents of build directory:"
      #   env:
      #     REACT_APP_GOOGLE_API_KEY: ${{ env.REACT_APP_GOOGLE_API_KEY }}
      #     REACT_APP_MODE: ${{ env.REACT_APP_MODE }}
      #     REACT_APP_BASE_API_URL: ${{ env.REACT_APP_BASE_API_URL }}
      #     REACT_APP_BASE_STORAGE_URL: ${{ env.REACT_APP_BASE_STORAGE_URL }}

      # Step 6: Deploy build to server
      - name: Deploy build to server
        run: |
          echo "Deploying to $DEPLOY_PATH"
          if [ -d "$DEPLOY_PATH" ]; then
            echo "Clearing existing files..."
            sudo rm -rf "$DEPLOY_PATH"/*
          else
            echo "Creating deploy directory..."
            sudo mkdir -p "$DEPLOY_PATH"
          fi
          sudo chown -R $USER:$USER "$DEPLOY_PATH"
          cp -r build/* "$DEPLOY_PATH"/
          echo "Deployment complete. Contents:"
          ls -la "$DEPLOY_PATH"
