import React, { useEffect, useState } from 'react';
import Avatar from './../assets/images/avatar.png';
import { Link } from 'react-router-dom';
import ContactNav from '../pages/team-contact/ContactNav';
import Loading from '../common/Loading';
import { API_URL } from './../common/fetchData/apiConfig'; 
import Badge from '../common/Badge';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};


const TeamContacts = () => {
    const [userData, setUserData] = useState([]); // Store all user data
    const [teamData, setTeamData] = useState([]); // Store team data
    const [error, setError] = useState(null); // Handle errors
    const [loading, setLoading] = useState(true); // Loading state
    const [searchQuery, setSearchQuery] = useState(''); // Search query state
    const [selectedTeam, setSelectedTeam] = useState(null); // Store selected team
    const [selectedTeamUsers, setSelectedTeamUsers] = useState([]);
    const [selectedUser, setSelectedUser] = useState(null);
    const [filterOptionLoading, setFilterOptionLoading] = useState(false);

    // Fetch teams data
    useEffect(() => {
        const fetchTeams = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');
            setFilterOptionLoading(true);

            try {
                const response = await fetch(`${API_URL}/teams`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch teams: ' + response.statusText);
                }

                const data = await response.json();
                setTeamData(data.teams); // Update team data
            } catch (error) {
                setError(error.message);
            } finally {
                setFilterOptionLoading(false);
            }
        };

        fetchTeams();
    }, []);

    // Fetch all users data (for "All Contacts" functionality)
    useEffect(() => {
        const fetchUsers = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');
            //setLoading(true); // Start loading users

            try {
                const response = await fetch(`${API_URL}/users`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch users: ' + response.statusText);
                }

                const data = await response.json();
                setUserData(data); // Update user data (all users)
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false); // Stop loading users
            }
        };

        fetchUsers();
    }, []);

    // Handle search query change
    const onSearch = (query) => {
        setSearchQuery(query);
    };

    // Handle team selection (passed from ContactNav)
    const onSelectTeam = (teamId) => {
        const team = teamData.find((team) => team.id === teamId);
        if (team) {
            setSelectedTeam(team); // Update selected team
            // Match users by their IDs
            const usersInTeam = userData.filter((user) => team.users.some((teamUser) => teamUser.id === user.id));
            setSelectedTeamUsers(usersInTeam); // Set the users of selected team
        }
    };

    // Handle "All Contacts" click - reset to show all contacts
    const onShowAllContacts = () => {
        setSelectedTeam(null);
        setSelectedTeamUsers([]);
    };

    // Handle user selection for detailed profile
    const handleUserSelect = (user) => {
        setSelectedUser(user); // Set the selected user to show their details
    };

    // Filter users based on search query
    const filteredUsers = selectedTeam ? 
        selectedTeamUsers.filter(user =>
            `${user.fname} ${user.lname}`.toLowerCase().includes(searchQuery.toLowerCase())
        ) :
        userData.filter(user =>
            `${user.fname} ${user.lname}`.toLowerCase().includes(searchQuery.toLowerCase())
        );

    // Sort users in descending order (latest first)
    const sortedUsers = filteredUsers.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    // Automatically select the last user (if not already selected)
    useEffect(() => {
        if (sortedUsers.length > 0 && !selectedUser) {
            setSelectedUser(sortedUsers[0]); // Select the latest user in the sorted list
        }
    }, [sortedUsers, selectedUser]);

    // If loading users or filter options, show loading component
    if (filterOptionLoading) {
        return <Loading />;
    }

    // If an error occurs, show error message
    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div className='bg-white dark:bg-gray-900 rounded-xl p-4'>
            <div className='border border-gray-200 rounded-xl flex justify-start items-start flex-row'>
                {/* Contacts Navigation */}
                <div className='text-left w-1/4 p-6'>
                    <h4 className='text-xl font-medium pb-8'>Team Member Contacts</h4>
                    <ContactNav 
                        teams={teamData} // Pass the teams to ContactNav
                        onSelectTeam={onSelectTeam} // Handle team selection
                        onShowAllContacts={onShowAllContacts} // Pass handler to show all contacts
                    />
                </div>
                {/* All Contacts */}
                <div className='border-x border-gray-200 p-6 w-1/3 h-[100vh] overflow-y-auto scrollbar-vertical'>
                    {/* Search Bar */}
                    <form className="flex items-center pb-4">
                        <label htmlFor="simple-search" className="sr-only">Search</label>
                        <div className="relative w-full">
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg aria-hidden="true" className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <input
                                type="text"
                                id="simple-search"
                                className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                placeholder="Search"
                                onChange={(e) => onSearch(e.target.value)} // Update search query on change
                            />
                        </div>
                    </form>
                    {/* Contact List */}
                    <div className="rounded-xl">
                        {sortedUsers.length > 0 ? (
                            <div>
                                {sortedUsers.map((user) => {
                                    return (
                                        <div
                                            key={user.id}
                                            onClick={() => handleUserSelect(user)} // OnClick to select the user
                                            className={`flex flex-row items-center justify-start gap-4 text-left mb-4 relative py-1 px-2 rounded-xl cursor-pointer ${
                                                selectedUser?.id === user.id ? "bg-primarySeafoam" : ""
                                            }`}
                                        >
                                            <div className="relative flex justify-center items-center text-center text-sm">
                                                
                                                {user.photo ? (
                                                    <img 
                                                        src={`${process.env.REACT_APP_BASE_STORAGE_URL}/${user.photo}`} 
                                                        alt="Profile" 
                                                        className="m-auto text-sm w-12 rounded-full" 
                                                    />
                                                    ) : (
                                                    <div className="w-12 h-12 bg-primary text-white flex justify-center items-center text-center text-sm rounded-full">
                                                        No <br/>Photo
                                                    </div>
                                                )}
                                                <span className="flex w-4 h-4 bg-green-500 rounded-full absolute top-0 left-9 border-2 border-white"></span>
                                            </div>
                                            <div className="block">
                                                <h4 className="text-left font-bold text-sm whitespace-nowrap">{user.fname} {user.lname}</h4>
                                                <p className="text-left">
                                                    {user?.designations?.length > 0 ? user?.designations[0]?.name : "No Designation Found"}
                                                </p>
                                            </div>
                                            <span className="material-symbols-rounded text-xl absolute right-2 text-gray-400">award_star</span>
                                        </div>
                                    );
                                })}
                            </div>
                        ) : (
                            <div>No users found</div>
                        )}
                    </div>
                </div>
                {/* User Details */}
                <div className='p-6 w-2/4'>
                    {selectedUser ? (
                        <div>
                            <div className="flex flex-row justify-between">
                                <div className="flex flex-row justify-center gap-8 items-center">
                                    <div className='w-36 h-36 overflow-hidden rounded-full bg-gray-100 flex flex-col items-center justify-center'>
                                        
                                        {selectedUser.photo ? (
                                            <img 
                                                src={`${process.env.REACT_APP_BASE_STORAGE_URL}/${selectedUser.photo}`} 
                                                alt="Profile" 
                                                className="w-38" 
                                            />
                                            ) : (
                                            <div className="w-36 h-36 bg-primary text-white flex justify-center items-center text-center text-sm">
                                                No <br/>Photo
                                            </div>
                                        )}
                                    </div>
                                    <div className="text-left">
                                        <h3 className="text-2xl font-medium pb-3">
                                            {selectedUser.fname} {selectedUser.lname}
                                        </h3>
                                        <p className="text-xl pb-4">{selectedUser?.designations?.[0]?.name}</p>
                                        <Badge text={selectedUser?.resource_types?.[0]?.name || "Role not found"} colorClass="bg-primary text-white" />

                                    </div>
                                </div>
                            </div>
                            <div className="flex flex-row justify-between p-4 mt-6">
                                <div className="flex flex-row justify-center gap-8 items-center">
                                    <div className="w-full rounded-lg">
                                        <div className="flex flex-row justify-start items-start flex-wrap gap-6">
                                            <div className="text-left min-w-[45%]">
                                                <p className="text-gray-700">Preferred Nickname</p>
                                                <p className="text-gray-900 font-medium">{selectedUser.nick_name}</p>
                                            </div>
                                            <div className="text-left min-w-[45%]">
                                                <p className="text-gray-700">Employee Id</p>
                                                <p className="text-gray-900 font-medium">{selectedUser.eid}</p>
                                            </div>
                                            <div className="text-left min-w-[45%]">
                                                <p className="text-gray-700">Email</p>
                                                <p className="text-gray-900 font-medium">{selectedUser.email}</p>
                                            </div>
                                            <div className="text-left min-w-[45%]">
                                                <p className="text-gray-700">Desk Id</p>
                                                <p className="text-gray-900 font-medium">{selectedUser.desk_id}</p>
                                            </div>
                                            <div className="text-left min-w-[45%]">
                                                <p className="text-gray-700">Team</p>
                                                <p className="text-gray-900 font-medium">
                                                    {
                                                        selectedUser?.teams?.find(team => team.pivot?.is_default === 1)?.name
                                                        || "Team not found"
                                                    }
                                                                    </p>
                                            </div>
                                            <div className="text-left min-w-[45%]">
                                                <p className="text-gray-700">Team Lead/Report to</p>
                                                <p className="text-gray-900 font-medium">{selectedUser?.teams?.[0]?.team_lead || "Not found"}</p>
                                            </div>
                                            <div className="text-left min-w-[45%]">
                                                <p className="text-gray-700">Primary Phone Number</p>
                                                <p className="text-gray-900 font-medium">{selectedUser.primary_contact}</p>
                                            </div>
                                            <div className="text-left min-w-[45%]">
                                                <p className="text-gray-700">Emergency Contact Number</p>
                                                <p className="text-gray-900 font-medium">{selectedUser.emergency_contact}</p>
                                            </div>
                                            <div className="text-left min-w-[45%]">
                                                <p className="text-gray-700">Emergency Contact Relationship</p>
                                                <p className="text-gray-900 font-medium">{selectedUser.relation_contact}</p>
                                            </div>
                                            <div className="text-left min-w-[45%]">
                                                <p className="text-gray-700">Secondary Contact Number</p>
                                                <p className="text-gray-900 font-medium">{selectedUser.secondary_contact}</p>
                                            </div>
                                            <div className="text-left min-w-[45%]">
                                                <p className="text-gray-700">On-Site Status</p>
                                                <p className="text-gray-900 font-medium">{selectedUser?.onsite_statuses?.[0]?.name || "Not found"}</p>
                                            </div>
                                            <div className="text-left min-w-[100%]">
                                                <p className="text-gray-700">Present Address</p>
                                                <p className="text-gray-900 font-medium">{selectedUser.present_address}</p>
                                            </div>
                                            <div className="text-left min-w-[100%]">
                                                <p className="text-gray-700">Permanent Address</p>
                                                <p className="text-gray-900 font-medium">{selectedUser.permanent_address}</p>
                                            </div>
                                            
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div>Select a contact to view profile</div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default TeamContacts;
