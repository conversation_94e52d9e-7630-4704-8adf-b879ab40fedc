import React from 'react';

const Badge = ({ text, colorClass }) => {
    if (text === 'HOD') {
        colorClass = 'bg-green-100 text-green-600'
    } else if (text == 'Manager') {
        colorClass = 'bg-green-200 text-green-800'
    } else if (text == 'Team Lead') {
        colorClass = 'bg-sky-100 text-cyan-700'
    } else if (text === 'Coordinator') {
        colorClass = 'bg-blue-100 text-blue-800'
    } else if (text === 'Shift Lead') {
        colorClass = 'bg-orange-100 text-orange-800'
    } else if (text === 'Designer') {
        colorClass = 'bg-purple-200 text-purple-700'
    } else if (text === 'Developer') {
        colorClass = 'bg-purple-400 text-purple-800'
    } else if (text === 'Quality Assurance') {
        colorClass = 'bg-pink-100 text-pink-800'
    } else if (text === 'Trainee') {
        colorClass = 'bg-gray-200 text-gray-800'
    } else if (text === 'Guest') {
        colorClass = 'bg-gray-100 text-gray-800'
    }else{
        colorClass = 'bg-gray-200 text-gray-800'
    }

  return (
    <span
      className={`items-center rounded-full ring-inset bg-opacity-50 p-1 text-xl font-medium rounded-full block text-center ${colorClass}`}
    >
      {text? text : 'Missing Designation'}

    </span>
  );
};

export default Badge;