import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import useFetchApiData from '../../../common/fetchData/useFetchApiData';
import { alertMessage } from '../../../common/coreui';

import { API_URL } from '../../../common/fetchData/apiConfig'; 

const EditReviewRelease = ({ isVisible, setVisible, dataItemsId }) => {
    const navigate = useNavigate();
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [selectedDepartment, setSelectedDepartment] = useState('');
    const [selectedTeam, setSelectedTeam] = useState('');
    const [releaseName, setReleaseName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    const token = localStorage.getItem('token');
    const { data: departmentsData } = useFetchApiData(`${API_URL}departments`, token);
    const { data: teamsData } = useFetchApiData(`${API_URL}/teams`, token);

    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) setLoggedInUser(userId);
    }, []);

    useEffect(() => {
        if (departmentsData) setDepartments(departmentsData.departments || []);
        if (teamsData) setTeams(teamsData.teams || []);
    }, [departmentsData, teamsData]);

    useEffect(() => {
        if (!dataItemsId || !departments.length) return;

        const fetchReview = async () => {
            try {
                const response = await fetch(`${API_URL}review/${dataItemsId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
        
                if (!response.ok) throw new Error('Failed to fetch review details');
        
                const data = await response.json();
                const review = data.review || data; // <-- this is the key change
        
                setReleaseName(review.name);
                setSelectedDepartment(review.department_id);
                setSelectedTeam(review.team_id);
        
                const department = departments.find(dep => dep.id === review.department_id);
                if (department?.teams?.length) {
                    setTeams(department.teams);
                    if (!review.team_id) {
                        setSelectedTeam(department.teams[0].id);
                    }
                } else {
                    setTeams([]);
                }
            } catch (err) {
                setError(err.message);
            }
        };
        

        fetchReview();
    }, [dataItemsId, departments]);

    const handleDepartmentChange = (e) => {
        const departmentId = e.target.value;
        setSelectedDepartment(departmentId);
        setSelectedTeam('');

        const department = departments.find(dep => dep.id === departmentId);
        if (department?.teams?.length) {
            setTeams(department.teams);
            setSelectedTeam(department.teams[0].id);
        } else {
            setTeams([]);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!loggedInUser || !selectedDepartment || !selectedTeam || !releaseName) {
            setError('Please fill all fields.');
            return;
        }

        try {
            const response = await fetch(`${API_URL}review/${dataItemsId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    department_id: selectedDepartment,
                    team_id: selectedTeam,
                    name: releaseName,
                    updated_by: loggedInUser,
                }),
            });

            if (!response.ok) throw new Error('Failed to update review release.');

            alertMessage('success');
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage('');
            }, 2000);
        } catch (err) {
            alertMessage('error');
        }
    };

    const handleClose = () => setVisible(false);

    return (
        <>
            {isVisible && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative overflow-y-auto max-h-[90vh] mt-10">
                        <button onClick={handleClose} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                            &times;
                        </button>
                        <h4 className="text-xl font-semibold mb-4 py-4">Edit Review Release</h4>
                        <form onSubmit={handleSubmit}>
                            <div className="mb-4">
                                <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">
                                    Select Department
                                </label>
                                <select
                                    id="department"
                                    value={selectedDepartment}
                                    onChange={handleDepartmentChange}
                                    className="block w-full border-gray-300 rounded-md shadow-sm"
                                    required
                                >
                                    <option value="">Select a Department</option>
                                    {departments.map(dep => (
                                        <option key={dep.id} value={dep.id}>{dep.name}</option>
                                    ))}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="team" className="block text-sm font-medium text-gray-700 pb-4">
                                    Select Team
                                </label>
                                <select
                                    id="team"
                                    value={selectedTeam}
                                    onChange={(e) => setSelectedTeam(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm"
                                    required
                                >
                                    <option value="">Select a Team</option>
                                    {teams.map(team => (
                                        <option key={team.id} value={team.id}>{team.name}</option>
                                    ))}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="releaseName" className="block text-sm font-medium text-gray-700 pb-4">
                                    Review Release Name
                                </label>
                                <input
                                    id="releaseName"
                                    type="text"
                                    value={releaseName}
                                    onChange={(e) => setReleaseName(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm"
                                    required
                                />
                            </div>

                            <div className="py-4">
                                <button
                                    type="submit"
                                    className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                                >
                                    Update Review Release
                                </button>
                            </div>

                            {error && <p className="text-red-500 text-sm">{error}</p>}
                            {successMessage && (
                                <div className='bg-[#DAF8E6] p-6 rounded-lg border-l-4 border-green-500 flex flex-row items-center'>
                                    <span className="material-symbols-rounded bg-green-500 text-gray-700 p-1 rounded-md">check_circle</span>
                                    <p className="text-green-500 text-xl font-medium pl-6">{successMessage}</p>
                                </div>
                            )}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default EditReviewRelease;
