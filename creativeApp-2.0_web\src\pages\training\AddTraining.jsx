import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import useFetchApiData from './../../common/fetchData/useFetchApiData.jsx';
import { API_URL } from './../../common/fetchData/apiConfig';

const AddTraining = () => {
    const navigate = useNavigate();
    const location = useLocation();
    
    // Form data state
    const [department, setDepartment] = useState('');
    const [departments, setDepartments] = useState('');
    const [teams, setTeams] = useState([]);
    const [selectedTeamId, setSelectedTeamId] = useState('');
    const [title, setTitle] = useState('');
    const [trainer, setTrainer] = useState('');
    const [arrangeBy, setArrangeBy] = useState('');
    const [categoryId, setCategoryId] = useState('');
    const [topicId, setTopicId] = useState('');
    const [date, setDate] = useState('');
    const [time, setTime] = useState('');
    const [duration, setDuration] = useState('');
    const [presentationUrl, setPresentationUrl] = useState('');
    const [recordUrl, setRecordUrl] = useState('');
    const [accessPasscode, setAccessPasscode] = useState('');
    const [locationField, setLocationField] = useState('');
    const [tags, setTags] = useState([]);
    const [evaluationForm, setEvaluationForm] = useState('');
    const [response, setResponse] = useState('');
    const [loggedUsers, setLoggedUsers] = useState([]);
    const [loggedInUsersTeamId, setLoggedInUsersTeamId] = useState("");
    const [loggedInUserId, setLoggedInUserId] = useState("");
    const [loggedInUsersDepartment, setLoggedInUsersDepartment] = useState("");
    const [loggedInUsersDepartmentId, setLoggedInUsersDepartmentId] = useState("");
    const [loggedInUsersteamName, setLoggedInUsersTeam] = useState("");
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    
    // State for fetched data
    const [categories, setCategories] = useState([]);
    const [topics, setTopics] = useState([]);
    const [users, setUsers] = useState([]);
    const [trainingLocations, setTrainingLocations] = useState([]);
    
    const token = localStorage.getItem('token');

    // Fetch data
    const { data: categoriesData } = useFetchApiData(`${API_URL}training-categories`, token);
    const { data: topicsData } = useFetchApiData(`${API_URL}training-topics`, token);
    const { data: departmentsData } = useFetchApiData(`${API_URL}departments`, token);
    const { data: teamsData } = useFetchApiData(`${API_URL}/teams`, token);
    const { data: usersData } = useFetchApiData(`${API_URL}/users`, token);
    const { data: loggedUsersData } = useFetchApiData(`${API_URL}logged-users`, token);
    const { data: locationsData } = useFetchApiData(`${API_URL}locations`, token);

    // Populate state with fetched data
    useEffect(() => {
        setCategories(categoriesData?.categories || []);
        setTopics(topicsData?.topics || []);
        setDepartments(departmentsData?.departments || []); // Ensure departments is always an array
        setTeams(teamsData?.teams || []);
        setUsers(usersData?.users || []);  // Store users data
        setTrainingLocations(locationsData?.locations || []);

        if (loggedUsersData) {
            const user = loggedUsersData;
            const departmentId = user.departments && user.departments.length > 0 ? user.departments[0].id : '';
            const departmentName = user.departments && user.departments.length > 0 ? user.departments[0].name : '';
            const loggedInUsersTeamId = user.teams && user.teams.length > 0 ? user.teams[0].id : '';
            const loggedInUsersteamName = user.teams && user.teams.length > 0 ? user.teams[0].name : '';

            const loggedInUserId = user.id;
    
            setLoggedInUserId(loggedInUserId);
            setLoggedInUsersDepartmentId(departmentId);
            setLoggedInUsersDepartment(departmentName);
            setLoggedInUsersTeamId(loggedInUsersTeamId);
            setLoggedInUsersTeam(loggedInUsersteamName);

            setLoggedUsers(loggedUsersData.users || []);
        }

    }, [categoriesData, topicsData, departmentsData, teamsData, usersData, locationsData, loggedUsersData]);

    // Get the logged-in user's full name
    const loggedInUser = users.find((user) => user.token === token);  // Assuming `token` is associated with the logged-in user
    const fullName = loggedInUser ? `${loggedInUser.fname} ${loggedInUser.lname}` : '';

    const filteredTeams = teams.filter(team => {
        const isTeamForLoggedInUser = team.id === loggedInUsersTeamId; // Show only teams that the logged-in user is part of
        return isTeamForLoggedInUser;
    });

    const handleSubmit = async (event) => {
        event.preventDefault();

        const formData = {
            department,
            team: selectedTeamId,
            title,
            trainer,
            arrange_by: arrangeBy,
            category_id: categoryId,
            topic_id: topicId,
            date,
            time,
            duration,
            presentation_url: presentationUrl,
            record_url: recordUrl,
            access_passcode: accessPasscode,
            location: locationField,
            tags: tags.join(','), // Assuming tags are an array of values
            evaluation_form: evaluationForm,
            response,
            created_by: fullName,  // Set created_by as the full name of the logged-in user
            updated_by: fullName,  // Set updated_by as the same (optional)
        };

        try {
            const response = await fetch(`${API_URL}training`, {
                method: 'POST',
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            if (!response.ok) {
                const data = await response.json();
                alert(`Error: ${data.error || 'Failed to create training.'}`);
                return;
            }

            setSuccessMessage(`Quick Access Hub "${response.title}" created successfully!`);

        } catch (error) {
            console.error('Error submitting form:', error);
            alert('Failed to create training session.');
        }
    };

    const isModalOpen = location.pathname === '/add-training';
    const handleClose = () => navigate('/training');

    return (
        isModalOpen && (
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-4xl relative overflow-y-auto h-[80vh] mt-10">
                    <button onClick={handleClose} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                        &times;
                    </button>
                    <h4 className="text-xl font-semibold mb-4 py-4">Add Training</h4>
                    <form onSubmit={handleSubmit}>
                        <div className="flex flex-wrap gap-6 text-left">
                            {/* Assigned Department */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">
                                    Department
                                </label>
                                <select
                                    id="department"
                                    name="department"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                    value={loggedInUsersDepartment} // Make sure loggedInUsersDepartment is set correctly
                                >
                                    <option value="" disabled>Select a department</option>
                                    {/* Show the logged-in user's department name */}
                                    {loggedInUsersDepartment && (
                                        <option value={loggedInUsersDepartment}>
                                            {loggedInUsersDepartment}
                                        </option>
                                    )}
                                </select>
                            </div>

                            {/* Teams */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="team" className="block text-sm font-medium text-gray-700 pb-4">
                                    Team
                                </label>
                                <select
                                    id="team"
                                    name="team"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    onChange={(e) => setSelectedTeamId(e.target.value)}
                                    value={selectedTeamId || ""}
                                    required
                                >
                                    <option value="">Select a team</option>
                                    {filteredTeams.map((team) => (
                                        <option key={team.id} value={team.id}>
                                            {team.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            {/* Title */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="title" className="block text-sm font-medium text-gray-700 pb-4">
                                    Training Title
                                </label>
                                <input
                                    id="title"
                                    type="text"
                                    name="title"
                                    value={title}
                                    onChange={(e) => setTitle(e.target.value)}
                                    required
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>

                            {/* Trainer */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="trainer" className="block text-sm font-medium text-gray-700 pb-4">
                                    Trainer
                                </label>
                                <select
                                    id="trainer"
                                    name="trainer"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    value={trainer}
                                    onChange={(e) => setTrainer(e.target.value)}
                                    required
                                >
                                    <option value="">Select a trainer</option>
                                    {users.map((user) => (
                                        <option key={user.id} value={user.id}>
                                            {user.fname} {user.lname}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            {/* Arrange By */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="arrangeBy" className="block text-sm font-medium text-gray-700 pb-4">
                                    Arrange By
                                </label>
                                <select
                                    id="arrangeBy"
                                    name="arrangeBy"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    value={arrangeBy}
                                    onChange={(e) => setArrangeBy(e.target.value)}
                                    required
                                >
                                    <option value="">Select who arranged the training</option>
                                    {users.map((user) => (
                                        <option key={user.id} value={user.id}>
                                            {user.fname} {user.lname}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            {/* Date */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="date" className="block text-sm font-medium text-gray-700 pb-4">
                                    Date 
                                </label>
                                <input
                                    id="date"
                                    type="date"
                                    name="date"
                                    value={date}  // Bind entryDate state as the value of the input
                                    onChange={(e) => setDate(e.target.value)}  // Update the state when a new date is selected
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>

                            {/* Add other fields for category_id, topic_id, etc. */}
                        </div>

                        <div className="pt-8 pb-6 m-auto text-center">
                            <button
                                type="submit"
                                className="min-w-56 bg-primary hover:bg-secondary text-white rounded-md py-3 px-6"
                            >
                                Add Quick Access
                            </button>
                        </div>

                        {error && <p className="text-red-500 text-sm mt-4">{error}</p>}
                        {successMessage && 
                            <div className='bg-[#DAF8E6] p-6 rounded-lg border-l-4 border-green-500 flex flex-row items-center'>
                                <span className="material-symbols-rounded bg-green-500 text-gray-700 p-1 rounded-md">check_circle</span>
                                {/* Success message display */}
                                <p className="text-green-500 text-xl font-medium pl-6">{successMessage}</p>
                            </div>
                        }

                    </form>
                </div>
            </div>
        )
    );
};

export default AddTraining;
