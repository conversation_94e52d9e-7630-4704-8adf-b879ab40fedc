"use strict";(self.webpackChunkcreativeapp=self.webpackChunkcreativeapp||[]).push([[624],{91624:(e,t,a)=>{a.r(t),a.d(t,{default:()=>w});var o=a(65043),s=a(58786),r=a(13076),l=a(56025),n=a(83003),i=a(47554),c=a(72450),d=a(11238),u=a(32650),m=a(73216),x=a(70579);const g="https://creative.sebpo.net/api/",p=e=>{let{isVisible:t,setVisible:a,dataItemsId:s}=e;const[r,n]=(0,o.useState)(""),[i,c]=(0,o.useState)([]),[d,u]=(0,o.useState)(""),[m,p]=(0,o.useState)(""),[h,f]=(0,o.useState)(null);(0,o.useEffect)((()=>{const e=localStorage.getItem("user_id");e&&f(e)}),[]),(0,o.useEffect)((()=>{(async()=>{if(!s)return;const e=localStorage.getItem("token");if(e)try{const t=await fetch(`${g}locations`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch locations: "+t.statusText);const a=(await t.json()).locations;if(!Array.isArray(a))throw new Error("Expected locations to be an array.");const o=a.find((e=>e.id===s));if(!o)throw new Error("Location not found. Please check the ID.");n(o.locations_name)}catch(d){u(d.message)}else u("No authentication token found.")})()}),[s]);return t?(0,x.jsx)("div",{className:"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50",onClick:()=>a(!1),children:(0,x.jsxs)("div",{className:"relative bg-white rounded-lg shadow-lg max-w-md w-full p-5",onClick:e=>e.stopPropagation(),children:[(0,x.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,x.jsx)("h3",{className:"text-lg font-semibold",children:"Update Location"}),(0,x.jsx)("button",{className:"text-gray-500 hover:text-gray-800",onClick:()=>a(!1),children:"\xd7"})]}),(0,x.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t=r.trim(),o=h;if(o){if(Array.isArray(i)){if(i.some((e=>e.locations_name.toLowerCase().trim()===t.toLowerCase()))){u("Location already exists. Please add a different location.");const e=setTimeout((()=>u("")),3e3);return()=>clearTimeout(e)}}u("");try{const e=localStorage.getItem("token"),s=await fetch(`${g}locations`,{method:"POST",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"},body:JSON.stringify({locations_name:t,updated_by:o})});if(!s.ok)throw new Error("Failed to save location: "+s.statusText);const r=await s.json();r.location.locations_name;(0,l.GW)({icon:"success",title:"Success!",text:(null===r||void 0===r?void 0:r.message)||"Location updated successfully."}),n("");const i=await fetch(`${g}locations`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!i.ok)throw new Error("Failed to fetch locations: "+i.statusText);const d=await i.json();c(d.locations||[]),setTimeout((()=>{a(!1),p("")}),1e3)}catch(d){(0,l.GW)("error")}}else u("User is not logged in.")},children:[(0,x.jsxs)("div",{className:"mb-4",children:[(0,x.jsx)("label",{htmlFor:"name",className:"block mb-2",children:"Location Name"}),(0,x.jsx)("input",{type:"text",id:"name",value:r,onChange:e=>n(e.target.value),className:"border rounded w-full p-2",required:!0})]}),(0,x.jsx)("button",{type:"submit",className:"bg-primary hover:bg-secondary text-white rounded-lg px-4 py-2",children:"Update Location"})]})]})}):null};var h=a(67573),f=a(17974),y=a(58598);const b="Work Location",v=()=>{const[e,t]=(0,o.useState)({}),[a,g]=(0,o.useState)({}),[v,w]=(0,o.useState)(""),[j,_]=(0,o.useState)(""),[k,N]=(0,o.useState)(!1),[S,C]=(0,o.useState)(!1),[A,$]=(0,o.useState)(null),[T,E]=(0,o.useState)(null),[F,L]=(0,o.useState)(null),[O,P]=((0,m.Zp)(),(0,o.useState)(!1)),[I,D]=(0,o.useState)("created_at"),[R,M]=(0,o.useState)("desc"),[z,U]=(0,o.useState)("10"),[V,B]=(0,o.useState)(1),{data:W,isFetching:G,error:q}=(0,u.XTr)({sort_by:I,order:R,page:V,per_page:z,query:j}),[Y,{data:H,error:Q}]=(0,u.ILY)(),[X]=(0,u.L0X)(),Z=e=>{let t=Object.entries(e).reduce(((e,t)=>{let[a,o]=t;if("string"===typeof o)return e+`&${a}=${o}`;if(Array.isArray(o)){return e+`&${a}=${o.map((e=>e.value)).join(",")}`}return e}),"");_(t)},J=e=>{(0,i.$3)(e,["id","team","department","updated_at","updated_by","updater","created_at","creator","created_by","updated_by"]);L(null),N(!0)},K=e=>{L(null),$(e),N(!0)},ee=e=>{(0,l.YU)({onConfirm:()=>{X(e),L(null)}})};let te=1;const{rolePermissions:ae}=(0,f.h)(),[oe,se]=(0,o.useState)((()=>[{id:te++,name:"Action",width:"180px",className:"bg-red-300",cell:e=>(0,x.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>L(e),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>K(e.id),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>J(e),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})},{id:te++,name:"S.No",selector:(e,t)=>(V-1)*z+t+1,width:"80px",omit:!1},{id:te++,name:"Location",db_field:"name",selector:e=>e.locations_name||"",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created by",selector:e=>{var t,a;return`${(null===(t=e.creator)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.creator)||void 0===a?void 0:a.lname)||""}`},db_field:"created_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Date",selector:e=>(0,y.hb)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Time",selector:e=>(0,y.DF)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!1},{id:te++,name:"Updated by",selector:e=>{var t,a;return`${(null===(t=e.updater)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.updater)||void 0===a?void 0:a.lname)||""}`},db_field:"updated_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Date",selector:e=>(0,y.hb)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Time",selector:e=>(0,y.DF)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!1}]));(0,o.useEffect)((()=>{se((e=>[...e.map((e=>"Action"===e.name?{...e,cell:e=>(0,x.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>L(e),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>K(e.id),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>J(e),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})}:e))]))}),[ae]);const re=(0,n.wA)(),le=(0,o.useCallback)((async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"group",o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"select",r=e.db_field||"title";try{w(r),C(!0);var l=[];const n=await Y({type:a.trim(),column:r.trim(),text:o.trim()});if(n.data&&(l=n.data),l.length){if("searchable"===s)return t((e=>({...e,[r]:l}))),l;const a=l.map((t=>{if(e.selector){let a=e.selector(t);return a?(t.total&&t.total>1&&(a+=` (${t.total})`),{label:a,value:t[r]}):null}})).filter(Boolean);return t((t=>({...t,[e.id]:(0,i.eb)(a)}))),a}}catch(T){E(T.message)}finally{C(!1)}}),[]);return(0,x.jsx)("section",{className:"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]",children:(0,x.jsxs)("div",{className:"mx-auto pb-6 ",children:[(0,x.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4",children:[(0,x.jsx)("div",{className:"w-4/12 md:w-10/12 text-start",children:(0,x.jsx)("h2",{className:"text-2xl font-bold ",children:b})}),(0,x.jsxs)("div",{className:"w-8/12 flex items-end justify-end gap-1",children:[(0,x.jsx)(l.DF,{columns:oe,setColumns:se}),!G&&W&&parseInt(W.total)>0&&(0,x.jsx)(x.Fragment,{children:(0,x.jsxs)("button",{className:"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:async()=>{try{const t=await re(u.Y_Z.endpoints.getLocationData.initiate({sort_by:I,order:R,page:V,per_page:(null===W||void 0===W?void 0:W.total)||10,query:j})).unwrap();if(null===t||void 0===t||!t.total||t.total<1)return!1;var e=1;let a=t.data.map((t=>{if(oe.length){let a={};return oe.forEach((o=>{!o.omit&&o.selector&&(a[o.name]="S.No"===o.name?e++:o.selector(t)||"")})),a}}));const o=d.Wp.json_to_sheet(a),s=d.Wp.book_new();d.Wp.book_append_sheet(s,o,"Sheet1");const r=d.M9(s,{bookType:"xlsx",type:"array"}),l=new Blob([r],{type:"application/octet-stream"});(0,c.saveAs)(l,`${b.replace(/ /g,"_")}_${a.length}.xlsx`)}catch(T){console.error("Error exporting to Excel:",T)}},children:[G&&(0,x.jsx)(x.Fragment,{children:(0,x.jsx)("span",{className:"material-symbols-outlined animate-spin text-sm me-2",children:"progress_activity"})}),!G&&(0,x.jsx)("span",{className:"material-symbols-outlined text-sm me-2",children:"file_export"}),"Export to Excel (",W.total,")"]})}),ae.hasManagerRole&&(0,x.jsx)("button",{className:" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:()=>P(!0),children:"Add New"})]})]}),(0,x.jsx)(l.$6,{columns:oe,selectedFilterOptions:a,setSelectedFilterOptions:g,fetchDataOptionsForFilterBy:le,filterOptions:e,filterOptionLoading:S,showFilterOption:v,resetPage:()=>{if(Object.keys(a).length){let e={};Object.keys(a).map((t=>{"string"===typeof a[t]?e[t]="":e[t]=[]})),g({...e}),Z({...e})}B(1)},setCurrentPage:B,buildQueryParams:Z}),q&&(0,x.jsx)("div",{className:"text-red-500",children:T}),G&&(0,x.jsx)(r.A,{}),(0,x.jsx)("div",{className:"border border-gray-200 p-0 pb-1 rounded-lg my-5 ",children:(0,x.jsx)(s.Ay,{columns:oe,data:(null===W||void 0===W?void 0:W.data)||[],className:"p-0 scrollbar-horizontal-10",fixedHeader:!0,highlightOnHover:!0,responsive:!0,pagination:!0,paginationServer:!0,paginationPerPage:z,paginationTotalRows:(null===W||void 0===W?void 0:W.total)||0,onChangePage:e=>{e!==V&&B(e)},onChangeRowsPerPage:e=>{e!==z&&(U(e),B(1))},paginationComponentOptions:{selectAllRowsItem:!0,selectAllRowsItemText:"ALL"},sortServer:!0,onSort:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"desc";Object.keys(e).length&&(D(e.db_field||e.name||"created_at"),M(t||"desc"))}})}),O&&(0,x.jsx)(h.A,{isVisible:O,setVisible:P}),k&&(0,x.jsx)(p,{isVisible:k,setVisible:N,dataItemsId:A}),F&&(0,x.jsx)(l.Qg,{item:F,setViewData:L,columns:oe,handleEdit:K,handleDelete:ee})]})})},w=()=>(0,x.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-xl",children:(0,x.jsx)(v,{})})}}]);
//# sourceMappingURL=624.d456a348.chunk.js.map