"use strict";(self.webpackChunkcreativeapp=self.webpackChunkcreativeapp||[]).push([[510],{98510:(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var n=a(65043),i=a(13826),s=a(7726),o=a(91121),r=a(40350),l=a(73216),c=a(70579);const d="https://creative.sebpo.net/api//",m=e=>{let{isVisible:t,setVisible:a,topicId:i}=e;(0,l.Zp)();const[s,o]=(0,n.useState)([]),[r,m]=(0,n.useState)([]),[u,p]=(0,n.useState)(""),[h,g]=(0,n.useState)(""),[f,b]=(0,n.useState)(""),[x,y]=(0,n.useState)(""),[j,v]=(0,n.useState)("");(0,n.useEffect)((()=>{if(!t)return;(async()=>{const e=localStorage.getItem("token");if(e)try{const t=await fetch(`${d}departments`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch departments");const a=await t.json();o(a.departments)}catch(x){y(x.message)}else y("No authentication token found.")})()}),[t]),(0,n.useEffect)((()=>{if(!i||!s.length)return;(async()=>{const e=localStorage.getItem("token");if(e)try{const t=await fetch(`${d}training-topic/${i}`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch Training Topic details");const a=(await t.json()).trainingTopic;if(a){b(a.name),p(a.department),g(a.team);const e=s.find((e=>e.name===a.department));e&&e.teams?(m(e.teams),!a.team&&e.teams.length>0&&g(e.teams[0].name)):m([])}else y("Training topic not found")}catch(x){y(x.message)}else y("No authentication token found.")})()}),[i,s]);return(0,c.jsx)(c.Fragment,{children:t&&(0,c.jsx)("div",{className:"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden",children:(0,c.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative overflow-y-auto h-[80vh] mt-10",children:[(0,c.jsx)("button",{onClick:()=>{a(!1)},className:"absolute top-2 right-2 text-gray-400 hover:text-gray-900",children:"\xd7"}),(0,c.jsx)("h4",{className:"text-xl font-semibold mb-4 py-4",children:"Edit Training Topic"}),(0,c.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),u&&h&&f){y("");try{const e=localStorage.getItem("token");if(!e)return void y("Authentication token is missing.");const t=await fetch(`${d}training-topic/${i}`,{method:"PUT",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"},body:JSON.stringify({department:u,team:h,name:f})});if(!t.ok)throw new Error("Failed to update Training Topic.");const n=await t.json();console.log("Topics for update",n),n.trainingTopic?v(`Training Topic "${n.trainingTopic.name}" updated successfully!`):v("Training Topic updated successfully!"),setTimeout((()=>{a(!1),v("")}),2e3)}catch(x){y(x.message)}}else y("Please fill all fields.")},children:[(0,c.jsxs)("div",{className:"mb-4",children:[(0,c.jsx)("label",{htmlFor:"department",className:"block text-sm font-medium text-gray-700 pb-4",children:"Select Department"}),(0,c.jsxs)("select",{id:"department",value:u,onChange:e=>{const t=e.target.value;if(p(t),g(""),t){const e=s.find((e=>e.name===t));e&&e.teams&&e.teams.length>0?(m(e.teams),g(e.teams[0].name)):(m([]),g(""))}else m([]),g("")},className:"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50",required:!0,children:[(0,c.jsx)("option",{value:"",children:"Select a Department"}),0===s.length?(0,c.jsx)("option",{disabled:!0,children:"No departments available"}):s.map((e=>(0,c.jsx)("option",{value:e.name,children:e.name},e.id)))]})]}),(0,c.jsxs)("div",{className:"mb-4",children:[(0,c.jsx)("label",{htmlFor:"team",className:"block text-sm font-medium text-gray-700 pb-4",children:"Select Team"}),(0,c.jsxs)("select",{id:"team",value:h,onChange:e=>g(e.target.value),className:"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50",required:!0,children:[(0,c.jsx)("option",{value:"",children:"Select a Team"}),0===r.length?(0,c.jsx)("option",{disabled:!0,children:"No teams available"}):r.map((e=>(0,c.jsx)("option",{value:e.name,children:e.name},e.id)))]})]}),(0,c.jsxs)("div",{className:"mb-4",children:[(0,c.jsx)("label",{htmlFor:"topicName",className:"block text-sm font-medium text-gray-700 pb-4",children:"Topic Name"}),(0,c.jsx)("input",{id:"topicName",type:"text",value:f,onChange:e=>b(e.target.value),className:"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50",required:!0})]}),(0,c.jsx)("div",{className:"py-4",children:(0,c.jsx)("button",{type:"submit",className:"w-full bg-primary hover:bg-secondary text-white rounded-md py-3",children:"Update Training Topic"})}),x&&(0,c.jsx)("p",{className:"text-red-500 text-sm",children:x}),j&&(0,c.jsxs)("div",{className:"bg-[#DAF8E6] p-6 rounded-lg border-l-4 border-green-500 flex flex-row items-center",children:[(0,c.jsx)("span",{className:"material-symbols-rounded bg-green-500 text-gray-700 p-1 rounded-md",children:"check_circle"}),(0,c.jsx)("p",{className:"text-green-500 text-xl font-medium pl-6",children:j})]})]})]})})})},u=()=>null!==localStorage.getItem("token"),p="https://creative.sebpo.net/api/",h=()=>{const[e,t]=(0,n.useState)([]),[a,i]=(0,n.useState)(!1),[s,o]=(0,n.useState)(!0),[l,d]=(0,n.useState)(null),[h,g]=(0,n.useState)(null);(0,n.useEffect)((()=>{(async()=>{if(!u())return g("No authentication token found."),void o(!1);const e=localStorage.getItem("token");try{const a=await fetch(`${p}training-topics`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error("Network response was not ok: "+a.statusText);const n=await a.json();console.log("Topics",n),t(n.trainingTopics.map((e=>({id:e.id,name:e.name,department:e.department,team:e.team,created_by:e.created_by,updated_by:e.updated_by}))))}catch(h){g(h.message)}finally{o(!1)}})()}),[]);return h?(0,c.jsx)("div",{className:"text-red-500",children:h}):s?(0,c.jsx)("div",{className:"text-gray-500",children:"Loading..."}):0===e.length?(0,c.jsx)("div",{className:"text-gray-500",children:"No data available"}):(0,c.jsxs)("div",{children:[(0,c.jsx)(r.A,{tableContent:e,columnNames:[{label:"SL",key:"id"},{label:"Toppic Name",key:"name"},{label:"Department",key:"department"},{label:"Team",key:"team"},{label:"Created By",key:"created_by"},{label:"Updated By",key:"updated_by"}],onDelete:async e=>{if(!u())return void g("No authentication token found.");const a=localStorage.getItem("token");try{const n=await fetch(`${p}training-topic/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"}});if(!n.ok)throw new Error("Failed to delete training topic: "+n.statusText);t((t=>t.filter((t=>t.id!==e))))}catch(h){g(h.message)}},onEdit:e=>{d(e),i(!0)},setModalVisible:i,setSelectedServiceId:d}),a&&(0,c.jsx)(m,{isVisible:a,setVisible:i,topicId:l})]})},g=()=>(0,c.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-xl",children:(0,c.jsxs)(i.A,{children:[(0,c.jsx)(s.A,{routeName:"/add-trainingtopic",buttonName:"Add Training Topic"}),(0,c.jsx)(h,{}),(0,c.jsx)(o.A,{})]})})}}]);
//# sourceMappingURL=510.e66aabad.chunk.js.map