<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Role;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "Assigning manager roles...\n";

// Get the manager role
$managerRole = Role::where('name', 'manager')->first();
if (!$managerRole) {
    echo "Manager role not found!\n";
    exit(1);
}

// Get some users to assign manager role (excluding super-admin)
$users = User::whereDoesntHave('roles', function($q) {
    $q->where('name', 'super-admin');
})->take(3)->get();

foreach ($users as $user) {
    // Check if user already has manager role
    if (!$user->roles->contains('name', 'manager')) {
        $user->roles()->attach($managerRole->id);
        echo "Assigned manager role to: {$user->fname} {$user->lname}\n";
    } else {
        echo "User {$user->fname} {$user->lname} already has manager role\n";
    }
}

echo "Done!\n";
echo "Total users with manager role: " . $managerRole->users()->count() . "\n";
