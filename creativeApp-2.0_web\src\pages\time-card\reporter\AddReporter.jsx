import React, { useEffect, useState } from 'react';
import Modal from '../../../common/modal/Modal';
import useFetchApiData from '../../../common/fetchData/useFetchApiData.jsx';
import { API_URL } from '../../../common/fetchData/apiConfig';
import timeZoneData from '../../../common/data/timeZoneData.js';
import { alertMessage } from '../../../common/coreui/alertMessage.js';
import SearchFilterSelect from '../../../common/utility/SearchFilterSelect.jsx';

// Helper function to convert 24-hour time to 12-hour format with AM/PM
const convertTo12HourFormat = (time24) => {
    let [hours, minutes] = time24.split(':');
    hours = parseInt(hours, 10);
    const suffix = hours >= 12 ? 'PM' : 'AM';
    if (hours > 12) hours -= 12;
    if (hours === 0) hours = 12;
    return `${hours}:${minutes} ${suffix}`;
};

const AddReporter = ({ isVisible, setVisible }) => {
    const [teams, setTeams] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [selectedTeamId, setSelectedTeamId] = useState(null);
    const [selectedTeam, setSelectedTeam] = useState('');
    const [reporterName, setReporterName] = useState('');
    const [reporters, setReporters] = useState([]);
    const [location, setLocation] = useState('');
    const [timezone, setTimezone] = useState('');
    const [email, setEmail] = useState('');
    const [startTime, setStartTime] = useState('');
    const [endTime, setEndTime] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [isOpen, setIsOpen] = useState(false);
    const [loggedUsers, setLoggedUsers] = useState([]);
    const [loggedInUserId, setLoggedInUserId] = useState('');
    const [loggedInUsersDepartment, setLoggedInUsersDepartment] = useState('');
    const [loggedInUsersDepartmentId, setLoggedInUsersDepartmentId] = useState('');
    const [loggedInUsersTeamId, setLoggedInUsersTeamId] = useState('');
    const [loggedInUsersteamName, setLoggedInUsersTeam] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);
    const [loading, setLoading] = useState(false);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    const token = localStorage.getItem('token');

    const { data: teamsData } = useFetchApiData(`${API_URL}/teams`, token);
    const { data: departmentsData } = useFetchApiData(`${API_URL}departments`, token);
    const { data: loggedUsersData } = useFetchApiData(`${API_URL}logged-users`, token);
    const { data: reportersData } = useFetchApiData(`${API_URL}reporters`, token);

    useEffect(() => {
        if (teamsData && teamsData.teams) {
            setTeams(teamsData.teams || []);
        }

        if (reportersData) {
            setReporters(reportersData.reporters || []);
        }

        if (loggedUsersData) {
            console.log('Logged Users Data:', loggedUsersData);
            const user = loggedUsersData;
            const departmentId = user.departments && user.departments.length > 0 ? user.departments[0].id : '';
            const departmentName = user.departments && user.departments.length > 0 ? user.departments[0].name : '';
            const loggedInUsersTeamId = user.teams && user.teams.length > 0 ? user.teams[0].id : '';
            const loggedInUsersteamName = user.teams && user.teams.length > 0 ? user.teams[0].name : '';
            const loggedInUserId = user.id;

            setLoggedInUserId(loggedInUserId);
            setLoggedInUsersDepartmentId(departmentId);
            setLoggedInUsersDepartment(departmentName);
            setLoggedInUsersTeamId(loggedInUsersTeamId);
            setLoggedInUsersTeam(loggedInUsersteamName);
            setLoggedUsers(loggedUsersData.users || []);
        }

        if (departmentsData) {
            setDepartments(departmentsData.departments || []);
        }
        
    }, [teamsData, loggedUsersData, departmentsData, selectedTeamId]);

    // Update filteredTeams when `teams` or `loggedInUsersTeamId` changes
    const filteredTeams = teams.filter(team => team.id === loggedInUsersTeamId);

    // Handle location (city) change to auto-fill the timezone
    const handleLocationChange = (event) => {
        const selectedCity = event.target.value;
        setLocation(selectedCity);
        const selectedTimeZone = timeZoneData.find(city => city.city === selectedCity);
        if (selectedTimeZone) {
            setTimezone(selectedTimeZone.timezone);
        } else {
            setTimezone(''); // Reset timezone if city is not found
        }
    };

    // Check if the email already exists among the reporters
    const isEmailExists = (email) => {
        return reporters.some(reporter => reporter.email === email);
    };

    // Show all teams for the logged-in user
    const userTeams = loggedUsersData && loggedUsersData.teams ? loggedUsersData.teams : [];

    const handleSubmit = async (event) => {
        event.preventDefault();

        setLoading(true);

        // Check if the email already exists
        if (isEmailExists(email)) {
            setError('A reporter with this email already exists.');
            return;
        }

        // Get user_id from localStorage for 'created_by'
        const createdBy = loggedInUser;
        
        if (!createdBy) {
            setError('User is not logged in.');
            return;
        }

        // Log the department ID to make sure it's captured
        console.log("Selected Department ID:", loggedInUsersDepartmentId);

        // Check if all fields are filled
        if (!loggedInUsersDepartmentId || !selectedTeamId || !reporterName) {
            setError('Please fill all fields.');
            console.log('Validation failed with the following values:');
            console.log('Department:', loggedInUsersDepartmentId);
            console.log('Team:', selectedTeamId);
            console.log('Reporter Name:', reporterName);
            console.log('Location:', location);
            console.log('Timezone:', timezone);
            console.log('Email:', email);
            console.log('Start Time:', startTime);
            console.log('End Time:', endTime);
            return;
        }

        setError(''); // Clear any previous error messages

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            // Convert times to 12-hour format (match AddSchedule's logic)
            const startTimeFormatted = convertTo12HourFormat(startTime);
            const endTimeFormatted = convertTo12HourFormat(endTime);

            // Ensure team is passed as an integer
            const teamId = parseInt(selectedTeamId, 10); // Ensure it's an integer

            // Create the request body including the created_by field
            const requestData = {
                department: loggedInUsersDepartmentId,
                team: teamId,  // Ensure it's an integer
                name: reporterName,
                location: location,
                timezone: timezone,
                email: email,
                start_time: startTimeFormatted,
                end_time: endTimeFormatted,
                created_by: createdBy,
            };

            console.log("Request Payload:", requestData);

            // Make the POST request
            const response = await fetch(`${API_URL}reporter`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData),
            });

            if (!response.ok) {
                // Log the response text for debugging purposes
                const responseData = await response.json();
                console.error('Backend error details:', responseData);
                setError(responseData.message || 'Failed to add reporter.');
                return;
            }

            const result = await response.json();

            //setSuccessMessage(`Reporter "${result.reporter.name}" added successfully!`);

            // ✅ Success alert
            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Reporter added successfully.',
            });

            setLoading(false);

            // Clear all fields on successful submission
            // setLoggedInUsersDepartmentId('');
            // setSelectedTeam('');
            setReporterName('');
            setLocation('');
            setTimezone('');
            setEmail('');
            setStartTime('');
            setEndTime('');

        } catch (error) {
            console.error("Error details:", error); // Log the error details
            alertMessage('error');
            setLoading(false);
        }
    };

    if (!isVisible) return null;


    return (
        <>
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                <div className="bg-white rounded-lg shadow-md w-full max-w-4xl relative">
                    <div className="flex justify-between items-center bg-gray-100 px-4 py-2">
                        <h4 className="text-base text-left font-medium text-gray-800">Add Reporter</h4>
                        <button
                            className="text-3xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                    </div>
                    {/* Modal Body */}
                    <form onSubmit={handleSubmit}>
                        <div className="flex flex-wrap p-6 overflow-y-auto max-h-[80vh] scrollbar-vertical text-left">
                            {/* Assigned Department */}
                            <div className="mb-4 w-1/2 px-4 pb-4">
                                <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-2">
                                    Department <span className='text-red-600'>*</span>
                                </label>
                                <select
                                    id="department"
                                    name="department"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                    value={loggedInUsersDepartmentId} // Use the department ID here
                                    onChange={(e) => setLoggedInUsersDepartmentId(e.target.value)} // Update department ID on change
                                >
                                    <option value="" disabled>Select a department</option>
                                    {/* Display the logged-in user's department */}
                                    {loggedInUsersDepartment && (
                                        <option value={loggedInUsersDepartmentId}>
                                            {loggedInUsersDepartment}
                                        </option>
                                    )}
                                </select>
                            </div>
                            {/* Teams */}
                            <div className="mb-4 w-1/2 px-4 pb-4">
                                <label htmlFor="team" className="block text-sm font-medium text-gray-700 pb-2">
                                    Team <span className='text-red-600'>*</span>
                                </label>
                                <select
                                    id="team"
                                    name="team"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    onChange={(e) => setSelectedTeamId(e.target.value)}
                                    value={selectedTeamId || ''}
                                    required
                                >
                                    <option value="">Select a team</option>
                                    {userTeams.map((team) => (
                                        <option key={team.id} value={team.id}>
                                            {team.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="mb-4 w-1/2 px-4 pb-4">
                                <label htmlFor="reporterName" className="block text-sm font-medium text-gray-700 pb-2">
                                    Reporter Name <span className='text-red-600'>*</span>
                                </label>
                                <input
                                    id="reporterName"
                                    type="text"
                                    value={reporterName}
                                    onChange={(e) => setReporterName(e.target.value)}
                                    placeholder="Add Reporter Name"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                />
                            </div>

                            <div className="mb-4 w-1/2 px-4 pb-4">
                                <label htmlFor="email" className="block text-sm font-medium text-gray-700 pb-2">
                                    Email
                                </label>
                                <input
                                    id="email"
                                    type="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    placeholder="Enter Reporter Email"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    
                                />
                            </div>

                            <div className="mb-4 w-1/2 px-4 pb-4">
                                <label htmlFor="location" className="block text-sm font-medium text-gray-700 pb-2">
                                    Location <span className='text-red-600'>*</span>
                                </label>
                                {/* <select
                                    id="location"
                                    value={location}
                                    onChange={handleLocationChange}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                >
                                    <option value="">Select City</option>
                                    {timeZoneData.map((cityData, index) => (
                                        <option key={index} value={cityData.city}>
                                            {cityData.city}
                                        </option>
                                    ))}
                                </select> */}
                                <SearchFilterSelect
                                    id="location"
                                    options={timeZoneData.map(cityData => cityData.city)}
                                    value={location}
                                    onChange={handleLocationChange}
                                    placeholder="Search city..."
                                />
                            </div>

                            <div className="w-1/2 px-4 pb-4">
                                <label htmlFor="timezone" className="block text-sm font-medium text-gray-700 pb-2">
                                    Timezone
                                </label>
                                <input
                                    id="timezone"
                                    type="text"
                                    value={timezone}
                                    onChange={(e) => setTimezone(e.target.value)}
                                    placeholder="Enter Timezone"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    
                                    disabled // Make this field read-only since it's auto-filled
                                />
                            </div>

                            {/* Start Time */}
                            <div className="w-1/2 px-4">
                                <label htmlFor="startTime" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Start Time <span className='text-red-600'>*</span></label>
                                <div className="relative">
                                    <div className="absolute inset-y-0 end-0 top-0 flex items-center pe-3.5 pointer-events-none">
                                        <svg className="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                            <path fillRule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clipRule="evenodd"/>
                                        </svg>
                                    </div>
                                    <input
                                        type="time"
                                        id="startTime"
                                        value={startTime}
                                        onChange={(e) => setStartTime(e.target.value)}
                                        
                                        className="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                    />
                                </div>
                            </div>

                            {/* End Time */}
                            <div className="w-1/2 px-4">
                                <label htmlFor="endTime" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">End Time <span className='text-red-600'>*</span></label>
                                <div className="relative">
                                    <div className="absolute inset-y-0 end-0 top-0 flex items-center pe-3.5 pointer-events-none">
                                        <svg className="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                            <path fillRule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clipRule="evenodd"/>
                                        </svg>
                                    </div>
                                    <input
                                        type="time"
                                        id="endTime"
                                        value={endTime}
                                        onChange={(e) => setEndTime(e.target.value)}
                                        
                                        className="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                    />
                                </div>
                            </div>

                        </div>

                        <div className='text-left p-6'>
                            <button
                                type="submit"
                                className="w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4"
                            >
                                <span class="material-symbols-rounded text-white text-xl font-regular">add_circle</span>
                                {loading ? 'Adding Reporter...' : 'Add Reporter'}
                            </button>
                        </div>

                        {error && <p className="text-red-500 text-sm">{error}</p>}

                        {/* {error && 
                        <div className='bg-[#DAF8E6] p-6 rounded-lg border-l-4 border-red-500 flex flex-row items-center'>
                            <span className="material-symbols-rounded bg-red-500 text-gray-700 p-1 rounded-md">check_circle</span>
                            <p className="text-red-500 text-xl font-medium pl-6">{error}</p>
                        </div>} */}

                        {successMessage && 
                        <div className='bg-[#DAF8E6] p-6 rounded-lg border-l-4 border-green-500 flex flex-row items-center'>
                            <span className="material-symbols-rounded bg-green-500 text-gray-700 p-1 rounded-md">check_circle</span>
                            <p className="text-green-500 text-xl font-medium pl-6">{successMessage}</p>
                        </div>}
                    </form>
                </div>
            </div>
        </>
    );
};

export default AddReporter;
