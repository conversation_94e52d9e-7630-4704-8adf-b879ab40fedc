import React, { useState, useEffect, useMemo } from "react";

import { useNavigate, useLocation } from "react-router-dom";
import { API_URL } from './../../../common/fetchData/apiConfig'; 
import useFetchApiData from './../../../common/fetchData/useFetchApiData';
import { alertMessage } from "../../../common/coreui";

const AddTaskRecord = ({isVisible, setVisible}) => {
    const [ticketNumber, setTicketNumber] = useState("");
    const [receivedDate, setReceivedDate] = useState("");
    const [dueDate, setDueDate] = useState("");
    const [unit, setUnit] = useState("");
    const [accountName, setAccountName] = useState("");
    const [campaignName, setCampaignName] = useState("");
    const [notes, setNotes] = useState("");
    const [selectedDepartment, setSelectedDepartment] = useState("");
    const [selectedTeam, setSelectedTeam] = useState("");
    const [selectedProductType, setSelectedProductType] = useState("");
    const [selectedTaskType, setSelectedTaskType] = useState("");
    const [revisionTaskTypeId, setRevisionTaskTypeId] = useState(null);
    const [selectedRevisionType, setSelectedRevisionType] = useState("");
    const [selectedRegion, setSelectedRegion] = useState("");
    const [selectedPriority, setSelectedPriority] = useState("");
    const [selectedReporter, setSelectedReporter] = useState("");
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [selectedDepartmentName, setSelectedDepartmentName] = useState("");
    const [selectedTeamName, setSelectedTeamName] = useState("");
    const [loggedUsers, setLoggedUsers] = useState([]);
    const [loggedInUserId, setLoggedInUserId] = useState("");
    const [loggedInUsersDepartment, setLoggedInUsersDepartment] = useState("");
    const [loggedInUsersDepartmentId, setLoggedInUsersDepartmentId] = useState("");
    const [loggedInUsersTeamId, setLoggedInUsersTeamId] = useState("");
    const [loggedInUsersteamName, setLoggedInUsersTeam] = useState("");
    const [selectedTeamId, setSelectedTeamId] = useState(null);
    const [loggedInUsersTeams, setLoggedInUsersTeams] = useState([]);


    const [productTypes, setProductTypes] = useState([]);
    const [taskTypes, setTaskTypes] = useState([]);
    const [revisionTypes, setRevisionTypes] = useState([]);
    const [regions, setRegions] = useState([]);
    const [priorities, setPriorities] = useState([]);
    const [reporters, setReporters] = useState([]);
    const [error, setError] = useState(null);
    const [successMessage, setSuccessMessage] = useState("");
    const navigate = useNavigate();
    const location = useLocation();

    // Function to check if token is valid
    const isTokenValid = () => {
        const token = localStorage.getItem("token");
        return token && token !== "null";
    };

    const token = localStorage.getItem('token');

    // Fetching data by useFetchApiData component
    const { data: taskTypeData } = useFetchApiData(`${API_URL}task-types`, token);
    const { data: revisionTypeData } = useFetchApiData(`${API_URL}revision-types`, token);
    const { data: regionData } = useFetchApiData(`${API_URL}regions`, token);
    const { data: priorityData } = useFetchApiData(`${API_URL}priorities`, token);
    const { data: reporterData } = useFetchApiData(`${API_URL}reporters`, token);
    const { data: loggedUsersData } = useFetchApiData(`${API_URL}logged-users`, token);

    useEffect(() => {
        if (taskTypeData && taskTypeData.taskTypes) {
            setTaskTypes(taskTypeData.taskTypes || []);
            
            // Find the task type with the name 'Revision' and set its ID
            const revisionType = taskTypeData.taskTypes.find(task => task.name === 'Revision');
            if (revisionType) {
                setRevisionTaskTypeId(revisionType.id);  // Store the ID of the 'Revision' task type
            }
        }
        if (revisionTypeData) {
            setRevisionTypes(revisionTypeData.revisionTypes || []);
        }
        if (regionData) {
            setRegions(regionData.regions || []);
        }
        if (priorityData) {
            setPriorities(priorityData.priorities || []);
        }

        if (reporterData) {
            console.log('Reporters', reporterData.reporters);
            setReporters(reporterData.reporters || []);
        }

        if (loggedUsersData) {
            const user = loggedUsersData;
            const departmentId = user.departments && user.departments.length > 0 ? user.departments[0].id : '';
            const departmentName = user.departments && user.departments.length > 0 ? user.departments[0].name : '';

            const loggedInUsersTeams = user.teams || [];  // Get all teams assigned to the logged-in user
            const loggedInUserId = user.id;

            setLoggedInUserId(loggedInUserId);
            setLoggedInUsersDepartmentId(departmentId);  // Store department ID of logged-in user
            setLoggedInUsersDepartment(departmentName);  // Store department name of logged-in user

            // Check if there's a default team
            const defaultTeam = loggedInUsersTeams.find(team => team.pivot.is_default === 1);
            
            if (defaultTeam) {
                setSelectedTeam(defaultTeam.id);  // Set the default team ID as the selected one
                setSelectedTeamName(defaultTeam.name);  // Set the default team's name as selected
            }

            // Set the team details (use the first team as fallback)
            const loggedInUsersTeamId = loggedInUsersTeams.length > 0 ? loggedInUsersTeams[0].id : '';
            const loggedInUsersteamName = loggedInUsersTeams.length > 0 ? loggedInUsersTeams[0].name : '';

            setLoggedInUsersTeamId(loggedInUsersTeamId);
            setLoggedInUsersTeam(loggedInUsersteamName);

            setLoggedUsers(loggedUsersData.users || []);
            setLoggedInUsersTeams(loggedInUsersTeams);  // Ensure teams are stored correctly
        }
        

    }, [taskTypeData, revisionTypeData, regionData, priorityData, reporterData, loggedUsersData]);

    // Fetch Departments, Teams, and Product Types
    useEffect(() => {
        const fetchDepartments = async () => {
            if (!isTokenValid()) {
                setError("No authentication token found.");
                return;
            }

            const token = localStorage.getItem("token");

            try {
                const response = await fetch(`${API_URL}departments`, {
                    method: "GET",
                    headers: {
                        Authorization: `Bearer ${token}`,
                        "Content-Type": "application/json",
                    },
                });

                if (!response.ok) {
                    throw new Error("Failed to fetch departments.");
                }

                const data = await response.json();
                setDepartments(data.departments || []);
            } catch (error) {
                setError(error.message);
            }
        };

        const fetchTeams = async () => {
            if (!isTokenValid()) {
                setError("No authentication token found.");
                return;
            }

            const token = localStorage.getItem("token");

            try {
                const response = await fetch(`${API_URL}/teams`, {
                    method: "GET",
                    headers: {
                        Authorization: `Bearer ${token}`,
                        "Content-Type": "application/json",
                    },
                });

                if (!response.ok) {
                    throw new Error("Failed to fetch teams.");
                }

                const data = await response.json();


                setTeams(data.teams || []);
            } catch (error) {
                setError(error.message);
            }
        };

        const fetchProductTypes = async () => {
            if (!isTokenValid()) {
                setError("No authentication token found.");
                return;
            }

            const token = localStorage.getItem("token");

            try {
                const response = await fetch(`${API_URL}product-types`, {
                    method: "GET",
                    headers: {
                        Authorization: `Bearer ${token}`,
                        "Content-Type": "application/json",
                    },
                });

                if (!response.ok) {
                    throw new Error("Failed to fetch product types.");
                }

                const data = await response.json();
                console.log('All Product Type', data);

                setProductTypes(data.productTypes || []);
            } catch (error) {
                setError(error.message);
            }
        };

        fetchDepartments();
        fetchTeams();
        fetchProductTypes();
    }, []);

    // Filter teams based on selected department
    const filterTeamsByDepartment = () => {
        if (!selectedDepartment) return []; // If no department is selected, return an empty array

        // Find the department by ID and return the teams from that department
        const department = departments.find(dept => dept.id === parseInt(selectedDepartment));
        
        return department ? department.teams : []; // Return the teams of the selected department
    };

    // Handle department change
    const handleDepartmentChange = (e) => {
        const departmentId = e.target.value;
        setSelectedDepartment(departmentId); // Set the selected department ID
    
        // Find the department name from the list of departments
        const department = departments.find(dept => dept.id === parseInt(departmentId));
        setSelectedDepartmentName(department ? department.name : ""); // Set the department name based on selected ID
    };
    
    // Handle team change
    const handleTeamChange = (e) => {
        const teamId = e.target.value; // Get the selected team ID
        setSelectedTeam(teamId);  // Set the selected team ID
    
        // Find the team from the logged-in user's teams using the team ID
        const team = loggedInUsersTeams.find(t => t.id === parseInt(teamId));
        setSelectedTeamName(team ? team.name : ""); // Set the team name based on selected team
    };
    
    
    const filteredTeams = teams.filter(team => {
        const isTeamForLoggedInUser = team.id === loggedInUsersTeamId; // Show only teams that the logged-in user is part of
        return isTeamForLoggedInUser;
    });

    // Filter product types based on selected team
    const filterProductTypesByTeam = () => {
        if (!selectedTeam) return []; // If no team is selected, return an empty array
    
        // Filter product types where the team_id matches the selectedTeam
        return productTypes.filter((productType) => productType.team_id === parseInt(selectedTeam));
    };

    const filteredProductTypes = filterProductTypesByTeam();

    // Filter task types based on selected team
    const filterTaskTypesByTeam = () => {
        if (!selectedTeam) return taskTypes;

        return taskTypes.filter((taskTypes) => taskTypes.team_id === parseInt(selectedTeam));
    };

    // Memoize filtered task types
    const filteredTaskTypes = useMemo(() => filterTaskTypesByTeam(), [selectedTeam]);

    
    // Dynamically check if the selected task type is a "Revision"
    const isRevisionTaskType = () => {
        const selectedTaskTypeObj = taskTypes.find((task) => task.id === parseInt(selectedTaskType));
        return selectedTaskTypeObj && selectedTaskTypeObj.name === 'Revision';
    };

    // Filter revision types based on the selected team
    const filterRevisionTypesByTeam = () => {
        if (!selectedTeam) return revisionTypes;
        return revisionTypes.filter((revisionType) => revisionType.team_id === parseInt(selectedTeam));
    };

    // Set revisionTypeDisabled based on the task type name being "Revision"
    const revisionTypeDisabled = !isRevisionTaskType(); // Disable if not a "Revision" task type

    // Filtered revision types based on task type and team selection
    const filteredRevisionTypes = revisionTypeDisabled ? [] : filterRevisionTypesByTeam();


    // Handle task type change
    const handleTaskTypeChange = (e) => {
        const taskTypeId = e.target.value; // Ensure this gets the task type ID
        setSelectedTaskType(taskTypeId);  // Set the selected task type ID
        setSelectedRevisionType('');  // Reset revision type when task type changes
    };

    // Filter regions based on selected team
    const filterRegionsByTeam = () => {
        if (!selectedTeam) return regions;

        return regions.filter((regions) => regions.team_id === parseInt(selectedTeam));
    };

    const filteredRegions = filterRegionsByTeam();

    // Filter Priority based on selected team
    const filterPriorityByTeam = () => {
        if (!selectedTeam) return priorities;

        return priorities.filter((priorities) => priorities.team_id === parseInt(selectedTeam));
    };

    const filteredPriority = filterPriorityByTeam();

    // Filter reporters based on selected team
    const filterReportersByTeam = () => {
        if (!selectedTeam) return reporters;
        const selectedTeamId = parseInt(selectedTeam);

        const filtered = reporters.filter((reporter) => parseInt(reporter.team) === selectedTeamId);
        return filtered;
    };

    const filteredReporters = filterReportersByTeam();
    

    // Log filtered data for debugging
    useEffect(() => {
        // console.log("Selected Department:", selectedDepartment);
        // console.log("Selected Team:", selectedTeam);
        // console.log("Filtered Teams:", filteredTeams);
        // console.log("Filtered Product Types:", filteredProductTypes);
    }, [selectedDepartment, selectedTeam, teams, productTypes]);

    // Handle form submission
    const handleSubmit = async (event) => {
        event.preventDefault();
    
        // Lookup department and team names just-in-time before sending
        const department = departments.find(dept => dept.id === parseInt(selectedDepartment));
        const team = loggedInUsersTeams.find(t => t.id === parseInt(selectedTeam));
    
        const formData = {
            ticket_number: ticketNumber,
            received_date: receivedDate,
            due_date: dueDate,
            unit,
            account_name: accountName,
            campaign_name: campaignName,
            notes,
            product_type_id: selectedProductType,
            task_type_id: selectedTaskType,
            revision_type_id: selectedRevisionType,
            region_id: selectedRegion,
            priority_id: selectedPriority,
            reporter_id: selectedReporter,
            department: department ? department.name : "",
            team: team ? team.name : "",
        };
    
        try {
            const token = localStorage.getItem("token");
            const response = await fetch(`${API_URL}task-detail`, {
                method: "POST",
                headers: {
                    Authorization: `Bearer ${token}`,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(formData),
            });
    
            if (!response.ok) {
                if (response.status === 409) {
                    const data = await response.json();
                    setError(data.error);
                } else {
                    throw new Error("Failed to create task.");
                }
                return;
            }
    
            const data = await response.json();
            alertMessage('success');
    
            // Reset form
            setTicketNumber("");
            setReceivedDate("");
            setDueDate("");
            setUnit("");
            setAccountName("");
            setCampaignName("");
            setNotes("");
            setSelectedProductType("");
            setSelectedTaskType("");
            setSelectedRevisionType("");
            setSelectedRegion("");
            setSelectedPriority("");
            setSelectedReporter("");
            setSelectedDepartment("");
            setSelectedTeam("");
            // Don't need to reset name states if you're computing them on submit
        } catch (error) {
            alertMessage('error');
        }
    };
    

    // Modal visibility based on location.pathname
    if (!isVisible) return null;

    return (
        <>
            
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                <div className="bg-white rounded-lg shadow-md w-full max-w-4xl relative">
                    <div className="flex justify-between items-center mb-4 bg-gray-100 px-4 py-2">
                        <h4 className="text-base text-left font-medium text-gray-800">Add New Task</h4>
                        <button
                            className="text-3xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                    </div>
                    <form onSubmit={handleSubmit} className="overflow-y-auto max-h-[70vh] scrollbar-vertical">
                        <div className="flex flex-wrap gap-6 p-6 text-left">
                            {/* Assigned Department */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label className="block text-sm font-medium text-gray-700 pb-4">
                                    Department <span className='text-red-600'>*</span>
                                </label>
                                <select
                                    id="department"
                                    value={selectedDepartment}
                                    onChange={handleDepartmentChange}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                    required
                                >
                                    <option value="">Select a Department</option>
                                    {departments.map((department) => (
                                        <option key={department.id} value={department.id}>
                                            {department.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            {/* Teams Select */}
                            {selectedDepartment && (
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label className="block text-sm font-medium text-gray-700 pb-4">
                                        Teams <span className='text-red-600'>*</span>
                                    </label>
                                    <select
                                        id="teams"
                                        value={selectedTeam}
                                        onChange={handleTeamChange}
                                        className="w-full p-2 border border-gray-300 rounded-md"
                                        required
                                    >
                                        <option value="">Select a Team</option>
                                        {loggedInUsersTeams.length > 0 ? (
                                            loggedInUsersTeams.map((team) => (
                                                <option key={team.id} value={team.id}>
                                                    {team.name}
                                                </option>
                                            ))
                                        ) : (
                                            <option value="" disabled>No teams available</option>
                                        )}
                                    </select>
                                </div>
                            )}

                                                
                            {/* Task settings */}
                            {selectedTeam && (
                                <div className="w-full flex flex-wrap gap-6">
                                    {/* Product Type Select */}
                                    <div className="mb-4 w-full sm:w-[47%]">
                                        <label className="block text-sm font-medium text-gray-700 pb-4">
                                            Product Type <span className='text-red-600'>*</span>
                                        </label>
                                        <select
                                            id="productType"
                                            value={selectedProductType}
                                            onChange={(e) => setSelectedProductType(e.target.value)}
                                            className="w-full p-2 border border-gray-300 rounded-md"
                                            required
                                        >
                                            <option value="">Select a Product Type</option>
                                            {filterProductTypesByTeam().length > 0 ? (
                                                filterProductTypesByTeam().map((productType) => (
                                                    <option key={productType.id} value={productType.id}>
                                                        {productType.name}
                                                    </option>
                                                ))
                                            ) : (
                                                <option value="" disabled>No product types available</option>
                                            )}
                                        </select>
                                    </div>

                                    {/* Task Type */}
                                    <div className="mb-4 w-full sm:w-[47%]">
                                        <label className="block text-sm font-medium text-gray-700 pb-4">
                                            Task Type <span className='text-red-600'>*</span>
                                        </label>
                                        <select
                                            id="taskType"
                                            value={selectedTaskType}
                                            onChange={handleTaskTypeChange} // Handle task type change
                                            className="w-full p-2 border border-gray-300 rounded-md"
                                            required
                                        >
                                            <option value="">Select a Task Type</option>
                                            {filteredTaskTypes.length > 0 ? (
                                                filteredTaskTypes.map((taskType) => (
                                                    <option key={taskType.id} value={taskType.id}>
                                                        {taskType.name}
                                                    </option>
                                                ))
                                            ) : (
                                                <option value="" disabled>No task types available</option>
                                            )}
                                        </select>
                                    </div>

                                    {/* Revision Type Dropdown */}
                                    <div className="mb-4 w-full sm:w-[47%]">
                                        <label className="block text-sm font-medium text-gray-700 pb-4">
                                            Revision Type
                                        </label>
                                        <select
                                            id="revisionType"
                                            value={selectedRevisionType}
                                            onChange={(e) => setSelectedRevisionType(e.target.value)} // Handle revision type change
                                            className={`w-full p-2 border border-gray-300 rounded-md ${revisionTypeDisabled ? 'bg-gray-100' : ''}`}
                                            disabled={revisionTypeDisabled} // Dynamically disable the dropdown
                                        >
                                            <option value="">Select a Revision Type</option>
                                            {filteredRevisionTypes.length > 0 ? (
                                                filteredRevisionTypes.map((revisionType) => (
                                                    <option key={revisionType.id} value={revisionType.id}>
                                                        {revisionType.name}
                                                    </option>
                                                ))
                                            ) : (
                                                <option value="" disabled>No revision types available</option>
                                            )}
                                        </select>
                                    </div>

                                    {/* Regions */}
                                    <div className="mb-4 w-full sm:w-[47%]">
                                        <label className="block text-sm font-medium text-gray-700 pb-4">
                                            Regions <span className='text-red-600'>*</span>
                                        </label>
                                        <select
                                            id="region"
                                            value={selectedRegion}
                                            onChange={(e) => setSelectedRegion(e.target.value)}
                                            className="w-full p-2 border border-gray-300 rounded-md"
                                            required
                                        >
                                            <option value="">Select region</option>
                                            {filteredRegions.length > 0 ? (
                                                filteredRegions.map((region) => (
                                                    <option key={region.id} value={region.id}>
                                                        {region.name}
                                                    </option>
                                                ))
                                            ) : (
                                                <option value="" disabled>No regions available</option>
                                            )}
                                        </select>
                                    </div>
                                    {/* Priority */}
                                    <div className="mb-4 w-full sm:w-[47%]">
                                        <label className="block text-sm font-medium text-gray-700 pb-4">
                                            Priority <span className='text-red-600'>*</span>
                                        </label>
                                        <select
                                            id="priority"
                                            value={selectedPriority}
                                            onChange={(e) => setSelectedPriority(e.target.value)}
                                            className="w-full p-2 border border-gray-300 rounded-md"
                                            required
                                        >
                                            <option value="">Select priority</option>
                                            {filteredPriority.length > 0 ? (
                                                filteredPriority.map((priority) => (
                                                    <option key={priority.id} value={priority.id}>
                                                        {priority.name}
                                                    </option>
                                                ))
                                            ) : (
                                                <option value="" disabled>No priority available</option>
                                            )}
                                        </select>
                                    </div>

                                    {/* Reporter */}
                                    <div className="mb-4 w-full sm:w-[47%]">
                                        <label className="block text-sm font-medium text-gray-700 pb-4">
                                            Reporter <span className='text-red-600'>*</span>
                                        </label>
                                        <select
                                            id="reporter"
                                            value={selectedReporter}
                                            onChange={(e) => setSelectedReporter(e.target.value)}
                                            className="w-full p-2 border border-gray-300 rounded-md"
                                            required
                                        >
                                            <option value="">Select a Reporter</option>
                                            {filteredReporters.length > 0 ? (
                                                filteredReporters.map((reporter) => (
                                                    <option key={reporter.id} value={reporter.id}>
                                                        {reporter.name}
                                                    </option>
                                                ))
                                            ) : (
                                                <option value="" disabled>No reporters available</option>
                                            )}
                                        </select>
                                    </div>

                                </div>
                            )}
                            {/* Ticket Number */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="ticketNumber" className="block text-sm font-medium text-gray-700 pb-4">
                                    Ticket Number <span className='text-red-600'>*</span>
                                </label>
                                <input
                                    id="ticketNumber"
                                    type="text"
                                    value={ticketNumber}
                                    onChange={(e) => setTicketNumber(e.target.value)}
                                    placeholder="Add the ticket"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                />
                            </div>  

                            {/* Received Date */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="receivedDate" className="block text-sm font-medium text-gray-700 pb-4">
                                    Received Date <span className='text-red-600'>*</span>
                                </label>
                                <input
                                    id="receivedDate"
                                    type="date"
                                    value={receivedDate}
                                    onChange={(e) => setReceivedDate(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                />
                            </div>

                            {/* Due Date */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 pb-4">
                                    Due Date <span className='text-red-600'>*</span>
                                </label>
                                <input
                                    id="dueDate"
                                    type="date"
                                    value={dueDate}
                                    onChange={(e) => setDueDate(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                />
                            </div>

                            {/* Unit */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="unit" className="block text-sm font-medium text-gray-700 pb-4">
                                    Unit <span className='text-red-600'>*</span>
                                </label>
                                <input
                                    id="unit"
                                    type="number"
                                    value={unit}
                                    onChange={(e) => setUnit(e.target.value)}
                                    placeholder="Add the unit number"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                />
                            </div>

                            {/* Account Name */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="accountName" className="block text-sm font-medium text-gray-700 pb-4">
                                    Account Name <span className='text-red-600'>*</span>
                                </label>
                                <input
                                    id="accountName"
                                    type="text"
                                    value={accountName}
                                    onChange={(e) => setAccountName(e.target.value)}
                                    placeholder="Add account name"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                />
                            </div>

                            {/* Campaign Name */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="campaignName" className="block text-sm font-medium text-gray-700 pb-4">
                                    Campaign Name <span className='text-red-600'>*</span>
                                </label>
                                <input
                                    id="campaignName"
                                    type="text"
                                    value={campaignName}
                                    onChange={(e) => setCampaignName(e.target.value)}
                                    placeholder="Add campaign name"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                />
                            </div>

                            {/* Notes */}
                            <div className="mb-4 w-full sm:w-[100%]">
                                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 pb-4">
                                    Notes
                                </label>
                                <textarea
                                    id="notes"
                                    value={notes}
                                    onChange={(e) => setNotes(e.target.value)}
                                    placeholder="Note/Comment"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>  

                        </div>

                        <div className="p-6">
                            <button
                                type="submit"
                                className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                            >
                                Add Task Details
                            </button>
                        </div>

                        {error && <p className="text-red-500 text-sm">{error}</p>}
                        {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                    </form>
                </div>
            </div>
           
        </>
    );
};

export default AddTaskRecord;
