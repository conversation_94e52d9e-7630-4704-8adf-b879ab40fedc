import React, { useEffect, useState } from 'react';
import Select from 'react-select';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditTraining = ({ isVisible, setVisible, trainingId }) => {
    const [trainingDetails, setTrainingDetails] = useState({
        training_title: '',
        training_department: '',
        training_arrange: '',
        training_category: '',
        training_topic: '',
        training_date: '',
        training_time: '',
        training_duration: '',
        training_presentation_url: '',
        training_record_url: '',
        training_access_passcode: '',
        training_tags: [],
        training_evaluation_form: '',
        training_response: '',
        training_location: '',
        training_team: '',
        training_trainer: '',
    });

    const [departments, setDepartments] = useState([]);
    const [locations, setLocations] = useState([]);
    const [categories, setCategories] = useState([]);
    const [topics, setTopics] = useState([]);
    const [teams, setTeams] = useState([]);
    const [tagOptions, setTagOptions] = useState([]);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    const getToken = () => localStorage.getItem('token');

    useEffect(() => {
        const fetchData = async () => {
            try {
                const token = getToken();
                const [trainingRes, deptRes, locRes, catRes, topicRes, teamRes] = await Promise.all([
                    fetch(`${API_URL}training/${trainingId}`, {
                        headers: { Authorization: `Bearer ${token}` },
                    }),
                    fetch(`${API_URL}departments`, { headers: { Authorization: `Bearer ${token}` } }),
                    fetch(`${API_URL}locations`, { headers: { Authorization: `Bearer ${token}` } }),
                    fetch(`${API_URL}training-categories`, { headers: { Authorization: `Bearer ${token}` } }),
                    fetch(`${API_URL}training-topic`, { headers: { Authorization: `Bearer ${token}` } }),
                    fetch(`${API_URL}/teams`, { headers: { Authorization: `Bearer ${token}` } }),
                ]);

                const [trainingData, deptData, locData, catData, topicData, teamData] = await Promise.all([
                    trainingRes.json(),
                    deptRes.json(),
                    locRes.json(),
                    catRes.json(),
                    topicRes.json(),
                    teamRes.json(),
                ]);

                console.log(trainingData)

                if (trainingData.training) {
                    let data = trainingData.training;

                    setTrainingDetails({
                        ...data
                    })

                    console.log(data)
                    console.log(trainingDetails)


                    const tags = trainingData.training.training_tags
                        ? trainingData.training.training_tags.split(',').map((tag) => ({ label: tag, value: tag }))
                        : [];
                    setTrainingDetails({ ...trainingData.training, training_tags: tags });
                }

                setDepartments(deptData.departments || []);
                setLocations(locData.locations || []);
                setCategories(catData.categories || []);
                setTopics(topicData.topics || []);
                setTeams(teamData.teams || []);

                const allTags = trainingData.trainings.reduce((acc, training) => {
                    const tags = training.training_tags ? training.training_tags.split(',') : [];
                    return [...new Set([...acc, ...tags])];
                }, []);
                setTagOptions(allTags.map((tag) => ({ label: tag, value: tag })));
            } catch (error) {
                setError('Failed to fetch data.');
            }
        };

        fetchData();
    }, [trainingId]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setTrainingDetails((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleTagsChange = (selectedOptions) => {
        setTrainingDetails((prev) => ({
            ...prev,
            training_tags: selectedOptions || [],
        }));
    };

    const handleSubmit = async (event) => {
    event.preventDefault();

    // Validate time and duration formats
    if (!/^\d{2}:\d{2}:\d{2}$/.test(trainingDetails.training_time)) {
        setError('Training time must be in the format hh:mm:ss.');
        return;
    }
    if (!/^\d{2}:\d{2}:\d{2}$/.test(trainingDetails.training_duration)) {
        setError('Training duration must be in the format hh:mm:ss.');
        return;
    }

    try {
        const token = localStorage.getItem('token');
        if (!token) {
            setError('Authentication token is missing.');
            return;
        }

        const payload = {
            ...trainingDetails,
            training_tags: trainingDetails.training_tags.map((tag) => tag.value).join(','), // Convert tags to string
        };

        console.log('Submitting payload:', payload);

        const response = await fetch(`${API_URL}training/${trainingId}`, {
            method: 'PUT',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to update training: ${errorText}`);
        }

        const result = await response.json();
        setSuccessMessage(`Training "${result.training_title}" updated successfully!`);
        setTimeout(() => setSuccessMessage(''), 5000);
    } catch (error) {
        console.error('Error during submission:', error);
        setError(error.message);
    }
};


    if (!isVisible) return null;

    return (
        <div
        className="fixed inset-0 bg-gray-800 bg-opacity-50 flex justify-center items-center z-50"
        onClick={() => setVisible(false)}
    >
        <div
            className="bg-white p-6 rounded-lg shadow-lg w-full max-w-4xl relative"
            onClick={(e) => e.stopPropagation()}
        >
            {/* Close Button */}
            <button
                type="button"
                className="absolute top-3 right-3 text-gray-600 hover:text-gray-900 text-lg"
                onClick={() => setVisible(false)}
            >
                &times;
            </button>
            
                <h3 className="text-lg font-semibold mb-4">Edit Training</h3>
                {error && <p className="text-red-500">{error}</p>}
                {successMessage && <p className="text-green-500">{successMessage}</p>}
                <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                        <label>Training Title</label>
                        <input
                            type="text"
                            name="training_title"
                            value={trainingDetails.training_title}
                            onChange={handleChange}
                            required
                            className="w-full border p-2"
                        />
                    </div>
                    <div>
                        <label>Training Department</label>
                        <select
                            name="training_department"
                            value={trainingDetails.training_department}
                            onChange={handleChange}
                            required
                            className="w-full border p-2"
                        >
                            <option value="">Select Department</option>
                            {departments.map((dept) => (
                                <option key={dept.id} value={dept.name}>
                                    {dept.name}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label>Training Arrange By</label>
                        <input
                            type="text"
                            name="training_arrange"
                            value={trainingDetails.training_arrange}
                            onChange={handleChange}
                            className="w-full border p-2"
                        />
                    </div>
                    <div>
                        <label>Training Category</label>
                        <select
                            name="training_category"
                            value={trainingDetails.training_category}
                            onChange={handleChange}
                            className="w-full border p-2"
                        >
                            <option value="">Select Category</option>
                            {categories.map((cat) => (
                                <option key={cat.id} value={cat.name}>
                                    {cat.name}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label>Training Topic</label>
                        <select
                            name="training_topic"
                            value={trainingDetails.training_topic}
                            onChange={handleChange}
                            className="w-full border p-2"
                        >
                            <option value="">Select Topic</option>
                            {topics.map((topic) => (
                                <option key={topic.id} value={topic.name}>
                                    {topic.name}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label>Training Date</label>
                        <input
                            type="date"
                            name="training_date"
                            value={trainingDetails.training_date}
                            onChange={handleChange}
                            required
                            className="w-full border p-2"
                        />
                    </div>
                    <div>
                        <label>Training Time</label>
                        <input
                            type="time"
                            name="training_time"
                            value={trainingDetails.training_time}
                            onChange={handleChange}
                            required
                            className="w-full border p-2"
                        />
                    </div>
                    <div>
                        <label>Training Duration</label>
                        <input
                            type="text"
                            name="training_duration"
                            value={trainingDetails.training_duration}
                            onChange={handleChange}
                            className="w-full border p-2"
                        />
                    </div>
                    <div>
                        <label>Training Presentation URL</label>
                        <input
                            type="url"
                            name="training_presentation_url"
                            value={trainingDetails.training_presentation_url}
                            onChange={handleChange}
                            className="w-full border p-2"
                        />
                    </div>
                    <div>
                        <label>Training Record URL</label>
                        <input
                            type="url"
                            name="training_record_url"
                            value={trainingDetails.training_record_url}
                            onChange={handleChange}
                            className="w-full border p-2"
                        />
                    </div>
                    <div>
                        <label>Training Access Passcode</label>
                        <input
                            type="text"
                            name="training_access_passcode"
                            value={trainingDetails.training_access_passcode}
                            onChange={handleChange}
                            className="w-full border p-2"
                        />
                    </div>
                    <div>
                        <label>Training Location</label>
                        <select
                            name="training_location"
                            value={trainingDetails.training_location}
                            onChange={handleChange}
                            required
                            className="w-full border p-2"
                        >
                            <option value="">Select Location</option>
                            {locations.map((loc) => (
                                <option key={loc.id} value={loc.locations_name}>
                                    {loc.locations_name}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label>Training Team</label>
                        <select
                            name="training_team"
                            value={trainingDetails.training_team}
                            onChange={handleChange}
                            className="w-full border p-2"
                        >
                            <option value="">Select Team</option>
                            {teams.map((team) => (
                                <option key={team.id} value={team.name}>
                                    {team.name}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label>Training Trainer</label>
                        <input
                            type="text"
                            name="training_trainer"
                            value={trainingDetails.training_trainer}
                            onChange={handleChange}
                            className="w-full border p-2"
                        />
                    </div>
                    <div>
                        <label>Training Tags</label>
                        <Select
                            isMulti
                            options={tagOptions}
                            value={trainingDetails.training_tags}
                            onChange={handleTagsChange}
                            className="w-full"
                        />
                    </div>
                    <div>
                        <label>Training Evaluation Form</label>
                        <input
                            type="url"
                            name="training_evaluation_form"
                            value={trainingDetails.training_evaluation_form}
                            onChange={handleChange}
                            className="w-full border p-2"
                        />
                    </div>
                    <div>
                        <label>Training Response</label>
                        <input
                            type="text"
                            name="training_response"
                            value={trainingDetails.training_response}
                            onChange={handleChange}
                            className="w-full border p-2"
                        />
                    </div>
                    <button
                        type="submit"
                        className="col-span-3 bg-blue-700 text-white rounded-lg px-4 py-2"
                    >
                        Update Training
                    </button>
                </form>
            </div>
        </div>
    );
};

export default EditTraining;
