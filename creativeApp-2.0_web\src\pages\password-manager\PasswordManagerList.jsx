import React, { useState } from "react";
import DataTable from "react-data-table-component";
import { confirmationAlert } from "../../common/coreui";
import { FormView, TableView } from "../../common/coreui";
import {
  useGetPasswordManagerDataQuery,
  useCreatePasswordManagerMutation,
  useUpdatePasswordManagerMutation,
  useDeletePasswordManagerMutation,
  useLazyGetDecryptedPasswordQuery,
} from "../../features/api/passwordManagerApi";
import { useGetDepartmentDataQuery } from "../../features/api/departmentApi";
// import { useGetTeamDataQuery } from "../../features/api/teamApi"; // Teams table doesn't exist

const PasswordManagerList = () => {
  // State management
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [sortColumn, setSortColumn] = useState("created_at");
  const [sortDirection, setSortDirection] = useState("desc");
  const [searchQuery, setSearchQuery] = useState("");
  const [modalVisible, setModalVisible] = useState(false);
  const [viewData, setViewData] = useState(null);
  const [formData, setFormData] = useState({});
  const [error, setError] = useState(null);
  const [showPassword, setShowPassword] = useState({});

  // API hooks
  const {
    data: dataItems,
    isLoading,
    refetch,
  } = useGetPasswordManagerDataQuery({
    sort_by: sortColumn,
    order: sortDirection,
    page: currentPage,
    per_page: perPage,
    query: searchQuery ? `search=${searchQuery}` : "",
  });

  const { data: departments } = useGetDepartmentDataQuery({
    page: 1,
    per_page: 100,
  });

  // Note: Teams table doesn't exist in current database, so we'll skip teams for now
  // const { data: teams } = useGetTeamDataQuery({
  //   page: 1,
  //   per_page: 100,
  // });
  const teams = { data: [] }; // Fallback empty teams data

  const [createPasswordManager] = useCreatePasswordManagerMutation();
  const [updatePasswordManager] = useUpdatePasswordManagerMutation();
  const [deletePasswordManager] = useDeletePasswordManagerMutation();
  const [getDecryptedPassword] = useLazyGetDecryptedPasswordQuery();

  // Handle form submission
  const handleSubmit = async (data) => {
    try {
      setError(null);
      if (formData.id) {
        await updatePasswordManager({ id: formData.id, ...data }).unwrap();
      } else {
        await createPasswordManager(data).unwrap();
      }
      setModalVisible(false);
      setFormData({});
      refetch();
    } catch (err) {
      setError(err);
    }
  };

  // Handle edit
  const handleEdit = (item) => {
    setFormData(item);
    setModalVisible(true);
  };

  // Handle delete
  const handleDelete = (id) => {
    confirmationAlert({
      onConfirm: () => {
        deletePasswordManager(id);
        setViewData(null);
      },
    });
  };

  // Handle add new
  const handleAddNew = () => {
    setFormData({});
    setModalVisible(true);
  };

  // Handle show/hide password
  const handleTogglePassword = async (id) => {
    if (showPassword[id]) {
      setShowPassword({ ...showPassword, [id]: null });
    } else {
      try {
        const result = await getDecryptedPassword(id).unwrap();
        setShowPassword({ ...showPassword, [id]: result.password });
      } catch (err) {
        console.error("Error fetching password:", err);
      }
    }
  };

  // Column definitions
  const columns = [
    {
      name: "Action",
      width: "200px",
      cell: (item) => (
        <div className="flex gap-1 mx-2 min-w-[180px] pl-3">
          {/* View Button */}
          <button
            className="flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm"
            onClick={() => setViewData(item)}
            title="View Details"
          >
            <span className="material-symbols-outlined text-lg">visibility</span>
          </button>

          {/* Edit Button */}
          <button
            className="flex items-center justify-center py-1 px-3 text-blue-600 hover:bg-blue-600 hover:text-white rounded-lg text-sm"
            onClick={() => handleEdit(item)}
            title="Edit"
          >
            <span className="material-symbols-outlined text-lg">edit</span>
          </button>

          {/* Show/Hide Password Button */}
          <button
            className="flex items-center justify-center py-1 px-3 text-purple-600 hover:bg-purple-600 hover:text-white rounded-lg text-sm"
            onClick={() => handleTogglePassword(item.id)}
            title={showPassword[item.id] ? "Hide Password" : "Show Password"}
          >
            <span className="material-symbols-outlined text-lg">
              {showPassword[item.id] ? "visibility_off" : "visibility"}
            </span>
          </button>

          {/* Delete Button */}
          <button
            className="flex items-center justify-center py-1 px-3 text-red-600 hover:bg-red-600 hover:text-white rounded-lg text-sm"
            onClick={() => handleDelete(item.id)}
            title="Delete"
          >
            <span className="material-symbols-outlined text-lg">delete</span>
          </button>
        </div>
      ),
    },
    {
      name: "Password Title",
      selector: (row) => row.password_title,
      sortable: true,
      db_field: "password_title",
      form: {
        type: "text",
        required: true,
        props: {
          placeholder: "Enter password title (e.g., Gmail Account)",
        },
      },
    },
    {
      name: "Password",
      cell: (row) => (
        <div className="font-mono">
          {showPassword[row.id] ? (
            <span className="text-green-600">{showPassword[row.id]}</span>
          ) : (
            <span className="text-gray-400">••••••••</span>
          )}
        </div>
      ),
      form: {
        type: "password",
        required: true,
        props: {
          placeholder: "Enter secure password",
        },
      },
      db_field: "password",
    },
    {
      name: "Department",
      selector: (row) => row.department?.name || "N/A",
      form: {
        type: "select",
        options: [
          { label: "Select Department", value: "" },
          ...(departments?.data?.map((dept) => ({
            label: dept.name,
            value: dept.id,
          })) || []),
        ],
      },
      db_field: "department_id",
    },
    {
      name: "Team",
      selector: (row) => row.team?.name || "N/A",
      form: {
        type: "select",
        options: [
          { label: "Select Team", value: "" },
          ...(teams?.data?.map((team) => ({
            label: team.name,
            value: team.id,
          })) || []),
        ],
      },
      db_field: "team_id",
    },
    {
      name: "Strength",
      selector: (row) => row.password_strength,
      cell: (row) => (
        <span
          className={`px-2 py-1 rounded text-xs ${
            row.password_strength === "Strong"
              ? "bg-green-100 text-green-800"
              : row.password_strength === "Moderate"
              ? "bg-yellow-100 text-yellow-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {row.password_strength}
        </span>
      ),
    },
    {
      name: "Created By",
      selector: (row) => row.user?.full_name || "N/A",
      cell: (row) => (
        <div className="flex items-center gap-2">
          {row.user?.photo && (
            <img
              src={row.user.photo}
              alt={row.user.full_name}
              className="w-6 h-6 rounded-full"
            />
          )}
          <span>{row.user?.full_name || "N/A"}</span>
        </div>
      ),
    },
    {
      name: "Created At",
      selector: (row) => new Date(row.created_at).toLocaleDateString(),
      sortable: true,
      db_field: "created_at",
    },
  ];

  return (
    <section className="bg-gray-50 dark:bg-gray-900 py-3 sm:py-5">
      <div className="px-4 mx-auto max-w-screen-2xl lg:px-12">
        <div className="relative overflow-hidden bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
          {/* Header */}
          <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">
            <div className="w-full md:w-1/2">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Password Manager
              </h2>
            </div>
            <div className="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
              {/* Search */}
              <div className="flex items-center">
                <input
                  type="text"
                  placeholder="Search passwords..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              {/* Add New Button */}
              <button
                onClick={handleAddNew}
                className="flex items-center justify-center text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2"
              >
                <span className="material-symbols-outlined mr-2">add</span>
                Add New Password
              </button>
            </div>
          </div>

          {/* Data Table */}
          <div className="border border-gray-200 p-0 pb-1 rounded-lg my-5">
            <DataTable
              columns={columns}
              data={dataItems?.data || []}
              className="p-0 scrollbar-horizontal-10"
              fixedHeader
              highlightOnHover
              responsive
              pagination
              paginationServer
              paginationPerPage={perPage}
              paginationTotalRows={dataItems?.meta?.total || 0}
              onChangePage={(page) => {
                if (page !== currentPage) {
                  setCurrentPage(page);
                }
              }}
              onChangeRowsPerPage={(newPerPage) => {
                if (newPerPage !== perPage) {
                  setPerPage(newPerPage);
                  setCurrentPage(1);
                }
              }}
              sortServer
              onSort={(column, sortDirection = "desc") => {
                if (Object.keys(column).length) {
                  setSortColumn(column.db_field || column.name || "created_at");
                  setSortDirection(sortDirection || "desc");
                }
              }}
              progressPending={isLoading}
            />
          </div>

          {/* Form Modal */}
          {modalVisible && (
            <FormView
              handleSubmit={handleSubmit}
              item={formData}
              error={error}
              setError={setError}
              grid={2}
              setModalVisible={setModalVisible}
              columns={columns}
              title={formData.id ? "Edit Password" : "Add New Password"}
            />
          )}

          {/* View Modal */}
          {viewData && (
            <TableView
              item={viewData}
              setViewData={setViewData}
              columns={columns}
              handleEdit={handleEdit}
              handleDelete={handleDelete}
              title="Password Details"
            />
          )}
        </div>
      </div>
    </section>
  );
};

export default PasswordManagerList;
