import React, { useEffect, useState } from 'react';
import { alertMessage } from '../../common/coreui';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const getDayOfWeek = (date) => {
    const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return daysOfWeek[date.getDay()];
};

const EditHolidayCalendar = ({ isVisible, setVisible, dataItemsId }) => {
    const [holidayDetails, setHolidayDetails] = useState({
        location_id: '',
        holiday_name: '',
        holiday_start_date: '',
        holiday_end_date: '',
        day_of_week: '',
        days: 0,
        department_id: '',
        team_id: ''
    });
    const [locations, setLocations] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loading, setLoading] = useState(false);

    const getToken = () => localStorage.getItem('token');
    const getUserId = () => localStorage.getItem('user_id');  // Get user_id from localStorage

    const isTokenValid = () => {
        const token = getToken();
        return token !== null;
    };

    const fetchTeams = async (departmentId) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        try {
            const response = await fetch(`${API_URL}/teams?department_id=${departmentId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${getToken()}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`Network response was not ok: ${response.statusText}`);
            }

            const data = await response.json();
            if (data.teams && Array.isArray(data.teams)) {
                setTeams(data.teams);
            } else {
                setError('Failed to load teams.');
            }
        } catch (error) {
            setError(error.message);
        }
    };

    useEffect(() => {
        const fetchHoliday = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}holidaycalenders/${dataItemsId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${getToken()}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`Network response was not ok: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Edited Holiday', data);

                if (data) {
                    setHolidayDetails({
                        location_id: data.location_id || '',
                        holiday_name: data.holiday_name || '',
                        holiday_start_date: data.holiday_start_date || '',
                        holiday_end_date: data.holiday_end_date || '',
                        day_of_week: data.day_of_week || '',
                        days: data.days || 0,
                        department_id: data.department_id || '',
                        team_id: data.team_id || ''
                    });
                } else {
                    setError('No holiday data found.');
                }
            } catch (error) {
                setError(error.message);
            }
        };

        const fetchLocations = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}locations`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${getToken()}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`Network response was not ok: ${response.statusText}`);
                }

                const data = await response.json();
                if (data.locations && Array.isArray(data.locations)) {
                    setLocations(data.locations);
                } else {
                    setError('Failed to load locations.');
                }
            } catch (error) {
                setError(error.message);
            }
        };

        const fetchDepartments = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${getToken()}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`Network response was not ok: ${response.statusText}`);
                }

                const data = await response.json();
                if (data.departments && Array.isArray(data.departments)) {
                    setDepartments(data.departments);
                } else {
                    setError('Failed to load departments.');
                }
            } catch (error) {
                setError(error.message);
            }
        };

        fetchHoliday();
        fetchLocations();
        fetchDepartments();
    }, [dataItemsId]);  // This effect will run when dataItemsId changes

    useEffect(() => {
        if (holidayDetails.department_id) {
            fetchTeams(holidayDetails.department_id);  // Fetch teams based on the selected department_id
        } else {
            setTeams([]); // Clear teams if no department is selected
        }
    }, [holidayDetails.department_id]);  // Trigger fetchTeams when department_id changes

    useEffect(() => {
        if (holidayDetails.holiday_start_date && holidayDetails.holiday_end_date) {
            const start = new Date(holidayDetails.holiday_start_date);
            const end = new Date(holidayDetails.holiday_end_date);
            if (end >= start) {
                const daysList = [];
                let currentDate = new Date(start);

                while (currentDate <= end) {
                    const dayOfWeek = getDayOfWeek(currentDate);
                    daysList.push(dayOfWeek);
                    currentDate.setDate(currentDate.getDate() + 1);
                }

                setHolidayDetails((prevDetails) => ({
                    ...prevDetails,
                    days: daysList.length,
                    day_of_week: daysList.join(', '),
                }));
            } else {
                setHolidayDetails((prevDetails) => ({
                    ...prevDetails,
                    days: 0,
                    day_of_week: '',
                }));
            }
        }
    }, [holidayDetails.holiday_start_date, holidayDetails.holiday_end_date]);

    const handleSubmit = async (event) => {
        event.preventDefault();

        setLoading(true);

        const updatedHolidayDetails = {
            ...holidayDetails,
            updated_by: getUserId(),  // Add the user_id from localStorage to the request
        };

        try {
            const response = await fetch(`${API_URL}holidaycalenders/${dataItemsId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${getToken()}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatedHolidayDetails),
            });

            if (!response.ok) {
                throw new Error('Failed to update holiday: ' + response.statusText);
            }

            const result = await response.json();
            alertMessage('success');

            setLoading(false);

            setTimeout(() => {
                setVisible(false);
                setSuccessMessage('Holiday calendar updated successfully!');
            }, 1000);
        } catch (error) {
            setLoading(false);
            alertMessage('error');
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setHolidayDetails((prevDetails) => ({
            ...prevDetails,
            [name]: value,
        }));
    };

    if (!isVisible) return null;

    return (
        <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
            <div className="bg-white rounded-lg shadow-md w-full max-w-4xl relative">
                <div className="flex justify-between items-center bg-gray-100 px-4 py-2">
                    <h4 className="text-base text-left font-medium text-gray-800">Update Holiday</h4>
                    <button
                        className="text-3xl text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>
                {error && <div className="text-red-500">{error}</div>}
                {successMessage && <div className="text-green-500">{successMessage}</div>}
                <form onSubmit={handleSubmit}>
                    <div className='flex flex-wrap gap-[4%] p-6 overflow-y-auto max-h-[90vh] scrollbar-vertical'>
                        <div className="mb-6 w-full md:max-w-[48%] text-left">
                            <label htmlFor="department_id" className="block mb-2">Holiday Department</label>
                            <select
                                id="department_id"
                                name="department_id"
                                value={holidayDetails.department_id}
                                onChange={handleChange}
                                className="py-3 px-4 block w-full border-gray-300 rounded-sm shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            >
                                <option value="">Select Department</option>
                                {departments.map((department) => (
                                    <option key={department.id} value={department.id}>
                                        {department.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div className="mb-6 w-full md:max-w-[48%] text-left">
                            <label htmlFor="team_id" className="block mb-2">Team</label>
                            <select
                                id="team_id"
                                name="team_id"
                                value={holidayDetails.team_id}
                                onChange={handleChange}
                                className="py-3 px-4 block w-full border-gray-300 rounded-sm shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            >
                                <option value="">Select Team</option>
                                {teams.map((team) => (
                                    <option key={team.id} value={team.id}>
                                        {team.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div className="mb-6 w-full md:max-w-[48%] text-left">
                            <label htmlFor="location_id" className="block mb-2">Office Location</label>
                            <select
                                id="location_id"
                                name="location_id"
                                value={holidayDetails.location_id}
                                onChange={handleChange}
                                className="py-3 px-4 block w-full border-gray-300 rounded-sm shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            >
                                <option value="">Select Office Location</option>
                                {locations.map((location) => (
                                    <option key={location.id} value={location.id}>
                                        {location.locations_name}
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div className="mb-6 w-full md:max-w-[48%] text-left">
                            <label htmlFor="holiday_name" className="block mb-2">Holiday Name</label>
                            <input
                                type="text"
                                id="holiday_name"
                                name="holiday_name"
                                value={holidayDetails.holiday_name}
                                onChange={handleChange}
                                className="py-3 px-4 block w-full border-gray-300 rounded-sm shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            />
                        </div>
                        <div className="mb-6 w-full md:max-w-[48%] text-left">
                            <label htmlFor="holiday_start_date" className="block mb-2">Holiday Start Date</label>
                            <input
                                type="date"
                                id="holiday_start_date"
                                name="holiday_start_date"
                                value={holidayDetails.holiday_start_date}
                                onChange={handleChange}
                                className="py-3 px-4 block w-full border-gray-300 rounded-sm shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            />
                        </div>
                        <div className="mb-6 w-full md:max-w-[48%] text-left">
                            <label htmlFor="holiday_end_date" className="block mb-2">Holiday End Date</label>
                            <input
                                type="date"
                                id="holiday_end_date"
                                name="holiday_end_date"
                                value={holidayDetails.holiday_end_date}
                                onChange={handleChange}
                                className="py-3 px-4 block w-full border-gray-300 rounded-sm shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            />
                        </div>
                        <div className="mb-6 w-full md:max-w-[48%] text-left">
                            <label htmlFor="day_of_week" className="block mb-2">Day of Week</label>
                            <input
                                type="text"
                                id="day_of_week"
                                name="day_of_week"
                                value={holidayDetails.day_of_week}
                                className="py-3 px-4 block w-full border-gray-300 rounded-sm shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                readOnly
                            />
                        </div>
                        <div className="mb-4 w-full md:max-w-[48%] text-left">
                            <label htmlFor="days" className="block mb-2">Days</label>
                            <input
                                type="text"
                                id="days"
                                name="days"
                                value={holidayDetails.days}
                                className="py-3 px-4 block w-full border-gray-300 rounded-sm shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                readOnly
                            />
                        </div>
                    </div>

                    <div className='text-left p-6'>
                        <button
                            type="submit"
                            className="w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4"
                        >
                            <span class="material-symbols-rounded text-white text-xl font-regular">add_circle</span>
                            {loading ? 'Updating...' : 'Update Holiday'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default EditHolidayCalendar;
