import React, { useEffect, useMemo, useState } from "react";
import { getWorldTimeStrings } from "../utils/worldTimeUtils";


const WelcomeCard = ({ userData, dateTimeStrings }) => {

  const [bgImageUrl, setBgImageUrl] = useState("");
  const [bgLoaded, setBgLoaded] = useState(false);
  const [localTimes, setLocalTimes] = useState({ english: "", bengali: "", hijri: "" });

  useEffect(() => {
    const url = `https://source.unsplash.com/1920x1080/?nature,dark,forest&sig=${Math.random()}`;
    setBgImageUrl(url);
  }, []);

  useEffect(() => {
    if (!dateTimeStrings) {
      getWorldTimeStrings()
        .then((times) => setLocalTimes(times))
        .catch(() => {});
    }
  }, [dateTimeStrings]);

  const timesToShow = dateTimeStrings || localTimes;

  const fullName =
    userData && (userData.fname || userData.lname)
      ? `${userData.fname || ""} ${userData.lname || ""}`.trim()
      : "Team Member";

  const profileSrc = userData?.photo
    ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${userData.photo}`
    : null;

  const initials = useMemo(() => {
    const parts = (fullName || "").split(" ").filter(Boolean);
    return parts.slice(0, 2).map((p) => p[0]?.toUpperCase()).join("") || "TM";
  }, [fullName]);

  // Single status chip from developer profile
  const rawStatus =
    userData?.member_statuses?.[0]?.name ||
    userData?.member_status ||
    userData?.status ||
    "";

  const status = (rawStatus || "").toLowerCase();

  const statusChip = useMemo(() => {
    let wrap = "bg-white/10 ring-white/20 text-white";
    let icon = "ℹ️";
    let label = rawStatus || "Status";

    if (status.includes("live")) {
      wrap = "bg-emerald-400/10 ring-emerald-300/30 text-emerald-200";
      icon = "🟢";
      label = "Live";
    } else if (status.includes("bench")) {
      wrap = "bg-amber-400/10 ring-amber-300/30 text-amber-200";
      icon = "🟡";
      label = "Bench";
    } else if (status.includes("trainee")) {
      wrap = "bg-sky-400/10 ring-sky-300/30 text-sky-200";
      icon = "🔵";
      label = "Trainee";
    }

    return (
      <div className={`inline-flex items-center gap-2 rounded-lg px-3 py-1.5 ring-1 ${wrap}`}>
        <span className="text-base leading-none">{icon}</span>
        <span className="font-medium">{label}</span>
      </div>
    );
  }, [rawStatus, status]);

  return (
    <div className="relative overflow-hidden rounded-2xl text-white p-6 md:p-8 shadow-lg flex flex-col justify-between min-h-[320px]">
      {/* Background */}
      <div className={`absolute inset-0 transition-opacity duration-700 ${bgLoaded ? "opacity-100" : "opacity-0"}`}>
        {bgImageUrl ? (
          <img
            src={bgImageUrl}
            alt=""
            onLoad={() => setBgLoaded(true)}
            onError={() => setBgLoaded(true)}
            className="absolute inset-0 h-full w-full object-cover"
          />
        ) : (
          <div className="absolute inset-0 bg-slate-900" />
        )}
      </div>
      <div className="absolute inset-0 bg-gradient-to-r from-black/85 via-black/70 to-black/20" />
      <div className="pointer-events-none absolute inset-0 ring-1 ring-white/10 rounded-2xl" />

      {/* Content */}
      <div className="relative z-10 flex flex-col lg:flex-row items-start gap-6">
        {/* Left */}
        <div className="flex-1">
          <p className="text-base md:text-lg font-semibold">Welcome Back 👋</p>
          <h2 className="text-[28px] md:text-4xl font-extrabold tracking-tight mt-1">{fullName}</h2>

          <p className="mt-3 max-w-2xl text-white/80 text-sm md:text-base leading-relaxed">
            Welcome to the team! Great people make a great team, and we’re so glad you’re here.
            This is a place where your skills, ideas, and passion will truly shine!
          </p>



          <div className="mt-5 text-xs md:text-sm font-mono space-y-1 text-white/90">
            <p>{timesToShow.english || "Loading..."}</p>
            <p>{timesToShow.bengali || "লোড হচ্ছে..."}</p>
            <p>{timesToShow.hijri || "..."}</p>
          </div>
        </div>

        {/* Right: Avatar + single status chip */}
        <div className="shrink-0 w-full md:w-auto flex flex-col items-start md:items-end gap-3 md:gap-4">
          <div className="relative">
            <div className="w-24 h-24 md:w-28 md:h-28 rounded-full overflow-hidden border-4 border-white/80 shadow-xl ring-4 ring-white/20">
              {profileSrc ? (
                <img
                  src={profileSrc}
                  alt={`${fullName} profile`}
                  className="h-full w-full object-cover"
                  onError={(e) => (e.currentTarget.style.display = "none")}
                />
              ) : (
                <div className="h-full w-full flex items-center justify-center bg-white/20 text-white/90 text-2xl font-bold">
                  {initials}
                </div>
              )}
            </div>
            <div className="absolute inset-0 -z-10 blur-2xl rounded-full bg-white/20 opacity-30" />
          </div>
          {statusChip}
        </div>
      </div>
    </div>
  );
};

export default WelcomeCard;