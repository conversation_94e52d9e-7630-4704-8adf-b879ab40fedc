import React from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import MainLayout from './dashboard/MainLayout';

import Login from './common/Login';
import Dashboard from './dashboard/Dashboard';
import UserList from './pages/team-member/TeamMemberList';
import Teams from './dashboard/settings/Teams';
import AddLocation from './pages/location/AddLocation';
import AddBranch from './pages/branch/AddBranch';
import Department from './dashboard/settings/Department';
import AddTeam from './pages/team/AddTeam';
import AddMember from './pages/team-member/AddMember';
import Settings from './dashboard/Settings';
import AddRole from './pages/role/AddRole';
import AddDepartment from './pages/department/AddDepartment';
import AddBillingStatus from './pages/billing-status/AddBillingStatus';
import AddResourceStatus from './pages/resource-status/AddResourceStatus';
import AddResourceType from './pages/resource-type/AddResourceType';
import AddDesignation from './pages/designation/AddDesignation';
import MemberIndex from './dashboard/MemberIndex';
import AddBlood from './pages/blood/AddBlood';
import AddAvailableStatus from './pages/available-status/AddAvailableStatus';
import AddContactType from './pages/contact-type/AddContactType';
import AddMemberStatus from './pages/member-status/AddMemberStatus';
import AddOnsiteStatus from './pages/onsite-status/AddOnsiteStatus';
import MemberOnboard from './dashboard/MemberOnboard';
import AddSchedule from './pages/schedule/AddSchedule';
import ProtectedRoute from './route/ProtectedRoute';
import TeamContacts from './dashboard/TeamContacts';

// Abdur Rahman
import Holiday from './dashboard/Holiday';
import QuickAccess from './dashboard/QuickAccessHubs';
import Training from './dashboard/Training'
import AddHolidayCalender from './pages/holiday-calender/AddHolidayCalender';
import AddTrainingCategory from './pages/training/training-category/AddTrainingCategory';
import AddTrainingTopic from './pages/training/training-topic/AddTrainingTopic';
import AddQuickAccessHub from './pages/quickaccesshub/AddQuickAccessHub';
import AddTraining from './pages/training/AddTraining';
import TaskDetails from './dashboard/task-details/TaskDetails';
import Formation from './dashboard/task-details/Formation';
import AddTaskRecord from './pages/task-details/task-record/AddTaskRecord';
import AddTimeCard from './pages/time-card/AddTimeCard';
import TimeCard from './dashboard/time-card/TimeCard';
import AddTeamShiftPlan from './pages/team-shift-plan/AddTeamShiftPlan';
import TeamShiftPlan from './dashboard/TeamShiftPlan';
import Profile from './dashboard/Profile';
import WorldTime from './pages/world-time/WorldTime';
import TimeZoneConvert from './pages/world-time/TimeZoneConvert';
import AttendanceFormation from './pages/attendance/AttendanceFormation/AttendanceFormationList';
import Attendance from './pages/attendance/Attendance/Attendance';
import SchedulePlaners from './pages/schedule-planers/SchedulePlaners';

// Imran Ahmed
import Todo from './dashboard/Todo';
import AddTodo from './pages/todo/commonTodo/AddTodo';
import Abouttheapp from './dashboard/Abouttheapp';
import AddAboutTheApp from './pages/about-the-app/AddAboutTheApp';
import AddChangeLog from './pages/change-log/AddChangeLog';
import Changelog from './dashboard/Changelog';
import AddReporter from './pages/time-card/reporter/AddReporter';
import Reporter from './dashboard/time-card/Reporter';
import Appsupport from './dashboard/Appsupport';
import AddAppsupport from './pages/app-support/AddAppsupport';
import Givefeedback from './dashboard/Givefeedback';
import Reportproblem from './dashboard/Reportproblem';
import AddGiveFeedback from './pages/give-feedback/AddGiveFeedback';
import AddReportProblem from './pages/report-problem/AddReportProblem';
import Teamsnapshot from './dashboard/Teamsnapshot';
import TeamSnapshotList from './pages/team-snapshot/TeamSnapshotList';
import Notice from './dashboard/NoticeBoard';
import NoticeBoard from './dashboard/NoticeBoard';
import AddNoticeBoardCategory from './pages/settings/noticeboardcategory/AddNoticeBoardCategory';
import AddNotice from './pages/notice/AddNotice';
import HolidayCalendarGoogleList from './pages/holiday-calender/HolidayCalenderGoogleList';
//import OfficeSeatPlan from './pages/holiday-calender/OfficeSeatPlan';
import OfficeSeatPlan from './pages/seat-plan/OfficeSeatPlan';
import NotFound from './common/utility/NotFound';
import Unauthorized from './common/utility/UnAuthorized';
import ResetPassword from './common/login/ResetPassword';
import UpdatePassword from './common/login/UpdatePassword';
import Welcome from './dashboard/PasswordManage';
import Creativetools from './dashboard/Creativetools';
import PasswordManagerList from './pages/password-manager/PasswordManagerList';
import PasswordManage from './dashboard/PasswordManage';


// Define your routes
const CreativeRoutes = [
  {
    path: '/login',
    element: <Login />,
  },
  // {
  //   path: '*',
  //   element: <NotFound />,
  // },
  {
    path: 'reset-password',
    element: <ResetPassword />,
  },
  {
    path: '/password/reset/:token',
    element: <UpdatePassword />,
  },
  {
    path: 'world-time-share',
    element: <WorldTime />,
  },
  {
    path: 'time-zone-convert-share',
    element: <TimeZoneConvert />,
  },
  {
    path: '/',
    element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><MainLayout /></ProtectedRoute>,
    children: [
      {
        path: '/',
        element: <Dashboard />, // Default to Dashboard
      },
      {
        path: 'unauthorized',
        element: <Unauthorized />,
      },
      {
        path: 'member-onboard',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><MemberOnboard /></ProtectedRoute>,
        
      },
      
      {
        path: 'world-time',
        element: <WorldTime />,
      },

      {
        path: 'time-zone-convert',
        element: <TimeZoneConvert />,
      },
      {
        path: 'time-zone-convert',
        element: <TimeZoneConvert />,
      },
      {
        path: 'attendance',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><Attendance/></ProtectedRoute>,
      },
      {
        path: 'attendance-formation',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AttendanceFormation/></ProtectedRoute>,
      },

      {
        path: 'team-members',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><TeamContacts/></ProtectedRoute>,
      },
      {
        path: 'member-index',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><MemberIndex /></ProtectedRoute>,
      },
      {
        path: 'team-snapshot',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><TeamSnapshotList /></ProtectedRoute>,
      },
      {
        path: 'profile',
        element: <Profile />,
      },
      {
        path: 'add-member',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><AddMember/></ProtectedRoute>,
      },
      {
        path: 'teams',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><Teams/></ProtectedRoute>,
      },
      {
        path: 'add-team',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddTeam/></ProtectedRoute>,
      },
      {
        path: 'add-role',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><AddRole/></ProtectedRoute>,
      },
      {
        path: 'departments',
        element: <Department />,
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><Department/></ProtectedRoute>,
      },
      {
        path: 'add-department',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddDepartment/></ProtectedRoute>,
      },
      {
        path: 'add-billing-status',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddBillingStatus/></ProtectedRoute>,
      },
      {
        path: 'add-resource-status',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddResourceStatus/></ProtectedRoute>,
      },
      {
        path: 'add-resource-type',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddResourceType/></ProtectedRoute>,
      },
      {
        path: 'add-designation',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddDesignation/></ProtectedRoute>,
      },
      {
        path: 'add-location',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddLocation/></ProtectedRoute>,
      },
      {
        path: 'add-branch',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddBranch/></ProtectedRoute>,
      },
      {
        path: 'add-schedule',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddSchedule/></ProtectedRoute>,
      },
      {
        path: 'add-available-status',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddAvailableStatus/></ProtectedRoute>,
      },
      {
        path: 'add-contact-type',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddContactType/></ProtectedRoute>,
      },
      {
        path: 'add-blood',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddBlood/></ProtectedRoute>,
      },
      {
        path: 'add-member-status',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddMemberStatus/></ProtectedRoute>,
      },
      {
        path: 'add-onsite-status',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddOnsiteStatus/></ProtectedRoute>,
      },
      {
        path: 'settings',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><Settings /></ProtectedRoute>,
      },

      // Task Details
      {
        path: 'add-task',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><AddTaskRecord /></ProtectedRoute>,
      },
      {
        path: 'task-records',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><TaskDetails /></ProtectedRoute>,
      },
      {
        path: 'formation',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><Formation /></ProtectedRoute>,
      },

      {
        path: 'add-reporter',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><AddReporter /></ProtectedRoute>,
      },

      {
        path: 'reporters',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><Reporter /></ProtectedRoute>,
      },

      // Routes for All Users
      {
        path: 'add-time',
        element: <AddTimeCard />,
      },

      {
        path: 'time-cards',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><TimeCard /></ProtectedRoute>,
      },

      // Abdur Rahman
      {
        path: 'holidaycalenders',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><Holiday /></ProtectedRoute>,
      },
      {
        path: 'add-holiday',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddHolidayCalender /></ProtectedRoute>,
      },
      {
        path: 'quickaccesshub',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><QuickAccess/></ProtectedRoute>,
      },
      {
        path: 'add-hub',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddQuickAccessHub /></ProtectedRoute>,
      },
      {
        path: 'training',
        element: <Training />,
      },
      {
        path: 'add-training',
        element: <AddTraining/>,
      },
      {
        path: 'add-training-category',
        element: <AddTrainingCategory />,
      },

      {
        path: 'add-trainingtopic',
        element: <AddTrainingTopic />,
      },

      {
        path: 'add-team-shift-plan',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddTeamShiftPlan /></ProtectedRoute>,
      },
      {
        path: 'shift-plan',
        element: <TeamShiftPlan/>,
      },
      
      {
        path: 'schedule-planners',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'shift-lead']}><SchedulePlaners /></ProtectedRoute>,
      },

      {
        path: 'achieve',
        element: <Todo />
      },
      {
        path: 'achieve/add-todo',
        element: <AddTodo />
      },
      {
        path: 'about-the-app',
        element: <Abouttheapp/>
      },
      {
        path: 'add-about-the-app',
        element: <AddAboutTheApp/>
      },
      {
        path: 'change-log',
        element: <Changelog/>
      },
      {
        path: 'add-change-log',
        element: <AddChangeLog/>
      },
      {
        path: 'app-support',
        element: <Appsupport/>
      },
      {
        path: 'add-app-support',
        element: <AddAppsupport/>
      },
      {
        path: 'give-feedback',
        element: <Givefeedback/>
      },
      {
        path: 'add-feedback',
        element: <AddGiveFeedback/>
      },
      
      {
        path: 'report-problem',
        element: <Reportproblem />
      },
      {
        path: 'add-report-problem',
        element: <AddReportProblem />
      },
      {
        path: 'team-snapshot',
        element: <Teamsnapshot />
      },
      {
        path: 'noticeboard',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><NoticeBoard/></ProtectedRoute>,
      },
      {
        path: 'add-notice-category',
        element: <AddNoticeBoardCategory/>
      },
      {
        path: 'add-notice',
        element: <AddNotice/>
      },
     
      {
        path: 'events',
        element: <HolidayCalendarGoogleList/>
      },
      {
        path: 'seat-plan',
        element: <OfficeSeatPlan/>
      },
      {
        path: 'password-manager',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><PasswordManage /></ProtectedRoute>
      },
      {
        path: 'password-manager',
        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><PasswordManagerList /></ProtectedRoute>
      },
      {
        path: 'creative-tools',
        element: <Creativetools/>
      }
     
      

      
    ],
  },
  {
    path: 'users',
    element: <UserList />,
  },
  {
    path: '*',
    element: <Navigate to="/login" />,
  }, 
];

// Create the router
const router = createBrowserRouter(CreativeRoutes);

export default router;