import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';

// Helper function to convert 24-hour time to 12-hour format with AM/PM
const convertTo12HourFormat = (time24) => {
    let [hours, minutes] = time24.split(':');
    hours = parseInt(hours, 10);
    const suffix = hours >= 12 ? 'PM' : 'AM';
    if (hours > 12) hours -= 12;
    if (hours === 0) hours = 12;
    return `${hours}:${minutes} ${suffix}`;
};

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;  // Token validation check
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const AddSchedule = ({isVisible, setVisible}) => {
    const location = useLocation();
    const navigate = useNavigate();
    
    const [teams, setTeams] = useState([]);
    const [shiftName, setShiftName] = useState('');
    const [shiftStart, setShiftStart] = useState('');
    const [shiftEnd, setShiftEnd] = useState('');
    const [selectedTeam, setSelectedTeam] = useState(''); // Store selected team
    const [selectedDepartment, setSelectedDepartment] = useState(''); // Store selected team

    
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    useEffect(() => {
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
            try {
                const parsedUser = JSON.parse(storedUser);
                setLoggedInUser(parsedUser);
            } catch (error) {
                console.error('Failed to parse user data from localStorage:', error);
            }
        }

        if (
            loggedInUser?.departments?.length > 0 &&
            !selectedDepartment // only set if nothing is already selected
        ) {
            setSelectedDepartment(loggedInUser.departments[0].id.toString());
        }

    }, [loggedInUser, selectedDepartment]);    

    useEffect(() => {
        const fetchTeams = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');
            try {
                const teamsResponse = await fetch(`${API_URL}/teams`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!teamsResponse.ok) {
                    throw new Error('Failed to fetch teams');
                }

                const teamsData = await teamsResponse.json();
                setTeams(teamsData.teams);
            } catch (error) {
                setError(error.message);
            }
        };

        fetchTeams();
    }, []);
    
    const handleSubmit = async (event) => {
        event.preventDefault();
        setError('');
        setSuccessMessage('');

        const createdBy = loggedInUser;

        if (!createdBy) {
            setError('User is not logged in.');
            return;
        }
    
        // Validate inputs (optional)
        if (!shiftName || !shiftStart || !shiftEnd || !selectedTeam || !selectedDepartment) {
            setError('Please fill all fields.');
            return;
        }
    
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }
    
            // Convert the 24-hour times to 12-hour times
            const shiftStartFormatted = convertTo12HourFormat(shiftStart);
            const shiftEndFormatted = convertTo12HourFormat(shiftEnd);
    
            // Send the formatted times to the backend
            const response = await fetch(`${API_URL}schedules`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    shift_name: shiftName,
                    shift_start: shiftStartFormatted,  // Use the 12-hour format
                    shift_end: shiftEndFormatted,      // Use the 12-hour format
                    department_id: selectedDepartment,
                    team_id: selectedTeam, 
                    created_by: createdBy,
                }),
            });
    
            const result = await response.json();
    
            if (result.status === false) {
                setError(`Error: ${result.message}`);
                if (result.errors) {
                    setError(JSON.stringify(result.errors));
                }
                return;
            }
    
            //setSuccessMessage(`Schedule for "${shiftName}" added successfully!`);

            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Office schedule added successfully.',
            });
    
            // Reset form after submission
            setShiftName('');
            setShiftStart('');
            setShiftEnd('');
            setSelectedDepartment('');
            setSelectedTeam('');
        } catch (error) {
            alertMessage('error');
        }
    };
    

    if (!isVisible) return null;

    return (
        <>
            
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                <div className="bg-white rounded-lg shadow-md w-full max-w-3xl relative max-h-[90vh]">
                    <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                        <h3 className="text-base text-left font-medium text-gray-800">Add Schedule</h3>
                        <button
                            className="text-2xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                    </div>
                    <form onSubmit={handleSubmit} className='text-left p-6 bg-white overflow-y-auto max-h-[90vh] scrollbar-vertical'>
                        {/* Department Selection */}
                        <div className="mb-4">
                            <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">Select Department</label>
                            <select
                                id="department"
                                value={selectedDepartment}
                                onChange={(e) => setSelectedDepartment(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            >
                                <option value="">Select a Department</option>
                                {loggedInUser?.departments?.map((dept) => (
                                    <option key={dept.id} value={dept.id}>{dept.name}</option>
                                ))}
                            </select>
                        </div>

                        {/* Team Selection */}
                        <div className="mb-4">
                            <label htmlFor="team" className="block text-sm font-medium text-gray-700 pb-4">Select Team</label>
                            <select
                                id="team"
                                value={selectedTeam}
                                onChange={(e) => setSelectedTeam(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            >
                                <option value="">Select a Team</option>
                                {teams.map((team) => (
                                    <option key={team.id} value={team.id}>{team.name}</option>
                                ))}
                            </select>
                        </div>
                        {/* Shift Name */}
                        <div className="mb-4">
                            <label htmlFor="shiftName" className="block text-sm font-medium text-gray-700 pb-4">
                                Shift Name
                            </label>
                            <input
                                type="text"
                                id="shiftName"
                                value={shiftName}
                                onChange={(e) => setShiftName(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>

                        {/* Time Selection */}
                        <div className="flex flex-row justify-center gap-4">
                            {/* Shift Start */}
                            <div className="mb-4 w-full sm:w-1/2">
                                <label htmlFor="start-time" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Start Time</label>
                                <div className="relative">
                                    <div className="absolute inset-y-0 end-0 top-0 flex items-center pe-3.5 pointer-events-none">
                                        <svg className="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                            <path fillRule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clipRule="evenodd"/>
                                        </svg>
                                    </div>
                                    <input
                                        type="time"
                                        id="shiftStart"
                                        value={shiftStart} // Keep the 12-hour format (hh:mm AM/PM)
                                        onChange={(e) => setShiftStart(e.target.value)}
                                        required
                                        className="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                    />
                                </div>
                            </div>

                            {/* Shift End */}
                            <div className="mb-4 w-full sm:w-1/2">
                                <label htmlFor="end-time" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">End Time</label>
                                <div className="relative">
                                    <div className="absolute inset-y-0 end-0 top-0 flex items-center pe-3.5 pointer-events-none">
                                        <svg className="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                            <path fillRule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clipRule="evenodd"/>
                                        </svg>
                                    </div>
                                    <input
                                        type="time"
                                        id="shiftEnd"
                                        value={shiftEnd} // Keep the 12-hour format (hh:mm AM/PM)
                                        onChange={(e) => setShiftEnd(e.target.value)}
                                        required
                                        className="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Error and Success Messages */}
                        <div className="pb-4 text-center">
                            {error && <p className="text-red-500">{error}</p>}
                            {successMessage && <p className="text-green-500">{successMessage}</p>}
                        </div>

                        {/* Submit Button */}
                        <button
                            type="submit"
                            className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600"
                        >
                            Add Schedule
                        </button>
                    </form>
                </div>
            </div>
           
        </>
    );
};

export default AddSchedule;
