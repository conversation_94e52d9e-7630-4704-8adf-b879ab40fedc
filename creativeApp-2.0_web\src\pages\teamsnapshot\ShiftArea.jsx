import React, { useState, useEffect } from "react";
import useFetchApiData from "../../common/fetchData/useFetchApiData.jsx";
import { API_URL } from "../../common/fetchData/apiConfig.js";

const ShiftArea = () => {
  const [users, setUsers] = useState([]);
  const [shiftData, setShiftData] = useState({});
  const [resourceTypes, setResourceTypes] = useState(new Set());

  const token = localStorage.getItem("token");
  const { data: usersData, error } = useFetchApiData(`${API_URL}/users`, token);

  useEffect(() => {
    if (error) {
      console.error("API Error:", error);
    }
  }, [error]);

  useEffect(() => {
    if (Array.isArray(usersData) && usersData.length > 0) {
      console.log("Fetched Users:", usersData);
      setUsers(usersData);
    } else {
      console.warn("No valid users found in API response");
    }
  }, [usersData]);

  useEffect(() => {
    if (!Array.isArray(users) || users.length === 0) return;

    const shiftCounts = {};
    const uniqueResourceTypes = new Set(); // Store unique resource types dynamically

    users.forEach((user) => {
      if (!user.schedules || !Array.isArray(user.schedules)) return;

      user.schedules.forEach((schedule) => {
        const shiftName = schedule?.shift_name?.toLowerCase() || "custom";

        if (!shiftCounts[shiftName]) {
          shiftCounts[shiftName] = {}; // Dynamically initialize
        }

        if (!user.resource_types || !Array.isArray(user.resource_types)) return;

        user.resource_types.forEach((resource) => {
          const role = resource?.name?.toLowerCase() || "unknown";
          uniqueResourceTypes.add(role); // Store unique roles dynamically

          if (!shiftCounts[shiftName][role]) {
            shiftCounts[shiftName][role] = 0; // Initialize role count
          }
          shiftCounts[shiftName][role]++;
        });
      });
    });

    setShiftData(shiftCounts);
    setResourceTypes(uniqueResourceTypes); // Update state with unique roles
  }, [users]);

  return (
    <div className="p-6 overflow-x-auto">
      <div className="flex gap-6 mb-8 text-start w-max">
        {Object.entries(shiftData).map(([shiftName, data], index) => (
          <ShiftCard
            key={index}
            title={`${shiftName.charAt(0).toUpperCase()}${shiftName.slice(1)}`}
            data={data}
            resourceTypes={Array.from(resourceTypes)} // Pass dynamic resource types
            bgColor={getShiftColor(shiftName)}
          />
        ))}
      </div>
    </div>
  );
};

// Function to assign colors dynamically based on shift name
const getShiftColor = (shiftName) => {
  const colors = {
    morning: "bg-blue-500",
    evening: "bg-orange-500",
    night: "bg-blue-900",
    custom: "bg-gray-200 text-black",
  };

  if (colors[shiftName]) {
    return colors[shiftName]; // Return predefined color if exists
  }

  // Generate a dynamic color for new shift names
  const colorPalette = [
    "bg-red-500", "bg-yellow-500", "bg-purple-500", "bg-pink-500",
    "bg-indigo-500", "bg-teal-500", "bg-cyan-500", "bg-lime-500"
  ];

  // Simple hash function to assign colors based on shift name
  const hash = shiftName.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return colorPalette[hash % colorPalette.length] || "bg-gray-500"; // Default color
};


// Shift Card Component
const ShiftCard = ({ title, data, resourceTypes, bgColor }) => (
  <div className={`${bgColor} text-white p-6 rounded-2xl shadow-lg w-[420px] overflow-y-auto flex flex-col`}>
    <h3 className="text-xl font-bold mb-4">{title}</h3>
    <div className="grid grid-cols-2 gap-4">
      {resourceTypes.map((type, idx) => (
        <Card key={idx} title={`Total ${type.charAt(0).toUpperCase() + type.slice(1)}`} count={data[type] || 0} />
      ))}
    </div>
  </div>
);

// Card Component
const Card = ({ title, count }) => (
  <div className="bg-white text-black p-4 rounded-lg shadow-md flex flex-col items-center">
    <span className="text-sm font-bold">{title}</span>
    <p className="text-2xl font-bold mt-2">{count}</p>
  </div>
);

export default ShiftArea;
