"use strict";(self.webpackChunkcreativeapp=self.webpackChunkcreativeapp||[]).push([[237],{88237:(e,t,a)=>{a.r(t),a.d(t,{default:()=>p});var n=a(65043),r=a(13826),s=a(7726),o=a(91121),i=a(73216),l=a(70579);const c="https://creative.sebpo.net/api//",d=e=>{let{isVisible:t,setVisible:a,trainingCategoryId:r}=e;(0,i.Zp)();const[s,o]=(0,n.useState)([]),[d,m]=(0,n.useState)([]),[u,g]=(0,n.useState)(""),[h,p]=(0,n.useState)(""),[y,b]=(0,n.useState)(""),[f,x]=(0,n.useState)(""),[j,v]=(0,n.useState)("");(0,n.useEffect)((()=>{if(!t)return;(async()=>{const e=localStorage.getItem("token");if(e)try{const t=await fetch(`${c}departments`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch departments");const a=await t.json();o(a.departments)}catch(f){x(f.message)}else x("No authentication token found.")})()}),[t]),(0,n.useEffect)((()=>{if(!r||!s.length)return;(async()=>{const e=localStorage.getItem("token");if(e)try{const t=await fetch(`${c}training-category/${r}`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch training category details");const a=(await t.json()).trainingCategory;b(a.name),g(a.department),p(a.team);const n=s.find((e=>e.name===a.department));n&&n.teams?(m(n.teams),!a.team&&n.teams.length>0&&p(n.teams[0].name)):m([])}catch(f){x(f.message)}else x("No authentication token found.")})()}),[r,s]);return(0,l.jsx)(l.Fragment,{children:t&&(0,l.jsx)("div",{className:"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden",children:(0,l.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative overflow-y-auto h-[80vh] mt-10",children:[(0,l.jsx)("button",{onClick:()=>{a(!1)},className:"absolute top-2 right-2 text-gray-400 hover:text-gray-900",children:"\xd7"}),(0,l.jsx)("h4",{className:"text-xl font-semibold mb-4 py-4",children:"Edit Training Category"}),(0,l.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),u&&h&&y){x("");try{const e=localStorage.getItem("token");if(!e)return void x("Authentication token is missing.");const t=await fetch(`${c}training-category/${r}`,{method:"PUT",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"},body:JSON.stringify({department:u,team:h,name:y})});if(!t.ok)throw new Error("Failed to update training category.");const n=await t.json();v(`Training Category "${n.training_category.name}" updated successfully!`),setTimeout((()=>{a(!1),v("")}),2e3)}catch(f){x(f.message)}}else x("Please fill all fields.")},children:[(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsx)("label",{htmlFor:"department",className:"block text-sm font-medium text-gray-700 pb-4",children:"Select Department"}),(0,l.jsxs)("select",{id:"department",value:u,onChange:e=>{const t=e.target.value;if(g(t),p(""),t){const e=s.find((e=>e.name===t));e&&e.teams&&e.teams.length>0?(m(e.teams),p(e.teams[0].name)):(m([]),p(""))}else m([]),p("")},className:"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50",required:!0,children:[(0,l.jsx)("option",{value:"",children:"Select a Department"}),0===s.length?(0,l.jsx)("option",{disabled:!0,children:"No departments available"}):s.map((e=>(0,l.jsx)("option",{value:e.name,children:e.name},e.id)))]})]}),(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsx)("label",{htmlFor:"team",className:"block text-sm font-medium text-gray-700 pb-4",children:"Select Team"}),(0,l.jsxs)("select",{id:"team",value:h,onChange:e=>p(e.target.value),className:"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50",required:!0,children:[(0,l.jsx)("option",{value:"",children:"Select a Team"}),0===d.length?(0,l.jsx)("option",{disabled:!0,children:"No teams available"}):d.map((e=>(0,l.jsx)("option",{value:e.name,children:e.name},e.id)))]})]}),(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsx)("label",{htmlFor:"categoryName",className:"block text-sm font-medium text-gray-700 pb-4",children:"Category Name"}),(0,l.jsx)("input",{id:"categoryName",type:"text",value:y,onChange:e=>b(e.target.value),className:"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50",required:!0})]}),(0,l.jsx)("div",{className:"py-4",children:(0,l.jsx)("button",{type:"submit",className:"w-full bg-primary hover:bg-secondary text-white rounded-md py-3",children:"Update Training Category"})}),f&&(0,l.jsx)("p",{className:"text-red-500 text-sm",children:f}),j&&(0,l.jsxs)("div",{className:"bg-[#DAF8E6] p-6 rounded-lg border-l-4 border-green-500 flex flex-row items-center",children:[(0,l.jsx)("span",{className:"material-symbols-rounded bg-green-500 text-gray-700 p-1 rounded-md",children:"check_circle"}),(0,l.jsx)("p",{className:"text-green-500 text-xl font-medium pl-6",children:j})]})]})]})})})};var m=a(40350);const u=()=>null!==localStorage.getItem("token"),g="https://creative.sebpo.net/api/",h=()=>{const[e,t]=(0,n.useState)([]),[a,r]=(0,n.useState)(null),[s,o]=(0,n.useState)(!1),[i,c]=(0,n.useState)(null);(0,n.useEffect)((()=>{(async()=>{if(!u())return void r("No authentication token found.");const e=localStorage.getItem("token");try{const a=await fetch(`${g}training-categories`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error("Network response was not ok: "+a.statusText);const n=await a.json();console.log("Training Category",n),t(n.trainingCategories.map((e=>({id:e.id,name:e.name,department:e.department,team:e.team,created_by:e.created_by,updated_by:e.updated_by}))))}catch(a){r(a.message)}})()}),[]);return a?(0,l.jsx)("div",{className:"text-red-500",children:a}):(0,l.jsxs)("div",{children:[(0,l.jsx)(m.A,{tableContent:e,columnNames:[{label:"SL",key:"id"},{label:"Product Type Name",key:"name"},{label:"Department",key:"department"},{label:"Team",key:"team"},{label:"Created By",key:"created_by"},{label:"Updated By",key:"updated_by"}],onDelete:async e=>{if(!u())return void r("No authentication token found.");const n=localStorage.getItem("token");try{const a=await fetch(`${g}training-category/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error("Failed to delete training category: "+a.statusText);t((t=>t.filter((t=>t.id!==e))))}catch(a){r(a.message)}},onEdit:e=>{c(e),o(!0)},setModalVisible:o,setSelectedServiceId:c}),s&&(0,l.jsx)(d,{isVisible:s,setVisible:o,trainingCategoryId:i})]})},p=()=>(0,l.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-xl",children:(0,l.jsxs)(r.A,{children:[(0,l.jsx)(s.A,{routeName:"/add-training-category",buttonName:"Add Training Category"}),(0,l.jsx)(h,{}),(0,l.jsx)(o.A,{})]})})}}]);
//# sourceMappingURL=237.d864732c.chunk.js.map