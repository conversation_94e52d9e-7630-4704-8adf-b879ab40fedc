import React, { useEffect, useState } from 'react';
import useFetchApiData from '../../../common/fetchData/useFetchApiData.jsx';
import { API_URL } from '../../../common/fetchData/apiConfig';
import timeZoneData from '../../../common/data/timeZoneData.js';
import { alertMessage } from '../../../common/coreui/alertMessage.js';
import SearchFilterSelect from '../../../common/utility/SearchFilterSelect.jsx';

const isTokenValid = () => {
    const token = localStorage.getItem('token');

    return token !== null; // Additional validation logic can be added here
};

const convertTo12HourFormat = (time24) => {
    let [hours, minutes] = time24.split(':');
    hours = parseInt(hours, 10);
    const suffix = hours >= 12 ? 'PM' : 'AM';
    if (hours > 12) hours -= 12;
    if (hours === 0) hours = 12;
    return `${hours}:${minutes} ${suffix}`;
};

const EditReporter = ({ isVisible, setVisible, dataItemsId }) => {
    const [teams, setTeams] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [selectedTeamId, setSelectedTeamId] = useState(null);  
    const [selectedDepartment, setSelectedDepartment] = useState(null);
    const [reporterName, setReporterName] = useState('');
    const [location, setLocation] = useState('');
    const [email, setEmail] = useState('');
    const [timezone, setTimezone] = useState('');
    const [startTime, setStartTime] = useState('');
    const [endTime, setEndTime] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedUsers, setLoggedUsers] = useState([]);
    const [loggedInUserId, setLoggedInUserId] = useState('');
    const [loggedInUsersDepartment, setLoggedInUsersDepartment] = useState('');
    const [loggedInUsersDepartmentId, setLoggedInUsersDepartmentId] = useState('');
    const [existingEmail, setExistingEmail] = useState('');
    const [loading, setLoading] = useState(false);
    const [loggedInUser, setLoggedInUser] = useState(null);

    const token = localStorage.getItem('token');

    const { data: teamsData } = useFetchApiData(`${API_URL}/teams`, token);
    const { data: departmentsData } = useFetchApiData(`${API_URL}departments`, token);
    const { data: loggedUsersData } = useFetchApiData(`${API_URL}/logged-users`, token);
    const { data: reportersData } = useFetchApiData(`${API_URL}reporters`, token);


    const filteredTeams = (departments && selectedDepartment) 
    ? departments
        .filter(department => department.id === selectedDepartment)
        .map(department => department.teams)
        .flat() // Flatten to make sure we have a flat array of teams
    : []; // Default to an empty array if no departments or no selectedDepartment


    useEffect(() => {
        if (!isVisible) return; // Ensure it runs only when visible
    
        // We need to check if both teams and departments are ready before fetching the reporter data
        if (!teamsData) {
            console.warn('Teams data is not loaded yet');
            return;
        }
    
        const fetchDepartments = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }
    
            const token = localStorage.getItem('token');
    
            try {
                // Fetch departments
                const response = await fetch(`${API_URL}departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    throw new Error('Failed to fetch departments');
                }
    
                const data = await response.json();
                console.log('Departments data', data);
    
                // Set departments in state
                setDepartments(data.departments || []);
            } catch (error) {
                setError(error.message || 'An error occurred while fetching departments');
            }
        };
    
        // Call the departments fetch
        fetchDepartments();
    
        // Update teams state with the fetched teams
        setTeams(teamsData.teams || []);
    
        // Update logged users data
        if (loggedUsersData) {
            const user = loggedUsersData;
            const departmentId = user.departments && user.departments.length > 0 ? user.departments[0].id : '';
            const departmentName = user.departments && user.departments.length > 0 ? user.departments[0].name : '';
            const loggedInUserId = user.id;
    
            setLoggedInUserId(loggedInUserId);
            setLoggedInUsersDepartmentId(departmentId);
            setLoggedInUsersDepartment(departmentName);
            setLoggedUsers(loggedUsersData.users || []);
        }
    
        // Fetch reporter data
        const fetchReporter = async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }
    
            try {
                const response = await fetch(`${API_URL}reporter/${dataItemsId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    const errorData = await response.json();
                    setError(errorData.message || 'Failed to fetch reporter details');
                    return;
                }
    
                const data = await response.json();
                const reporter = data.reporter;
    
                // Set reporter details in the state
                setReporterName(reporter.name);
                setLocation(reporter.location);
                setEmail(reporter.email);
                setTimezone(reporter.timezone);
                setStartTime(reporter.start_time);
                setEndTime(reporter.end_time);
                setSelectedDepartment(reporter.department);
                setSelectedTeamId(reporter.team); // Set the team id based on the reporter's team
    
                setExistingEmail(reporter.email); // Save the email from fetched data
    
                // Match the reporter's department and get the corresponding teams
                if (Array.isArray(departments) && departments.length > 0) {
                    const department = departments.find(dep => dep.id === reporter.department);
                    if (department) {
                        if (Array.isArray(department.teams) && department.teams.length > 0) {
                            setTeams(department.teams || []); // Ensure teams are available
                            const team = department.teams.find(team => team.id === reporter.team);
                            if (team) {
                                setSelectedTeamId(team.id); // Set the default selected team
                            } else {
                                console.warn(`Team with id ${reporter.team} not found in department ${department.name}`);
                            }
                        } else {
                            console.warn(`Department ${department.name} has no teams available`);
                        }
                    } else {
                        console.warn(`Department with id ${reporter.department} not found`);
                    }
                } else {
                    console.warn('Departments data is empty or not loaded');
                }
            } catch (error) {
                setError(error.message || 'An unexpected error occurred');
            }
        };
    
        fetchReporter(); // Fetch reporter after departments and teams are ready
    }, [dataItemsId, isVisible, departments, teamsData, loggedUsersData]);
    
    
    useEffect(() => {
        if (!selectedDepartment) return;

        const department = departments.find(dep => dep.id === selectedDepartment);
        if (department && department.teams) {
            setTeams(department.teams);
        } else {
            setTeams([]);
        }
    }, [selectedDepartment, departments]);

    const handleLocationChange = (event) => {
        const selectedCity = event.target.value;
        setLocation(selectedCity);
        const selectedTimeZone = timeZoneData.find(city => city.city === selectedCity);
        if (selectedTimeZone) {
            setTimezone(selectedTimeZone.timezone);
        } else {
            setTimezone(''); // Reset timezone if city is not found
        }
    };

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault();

        setLoading(true);
    
        // Get user_id from localStorage for 'created_by'
        const updatedBy = loggedInUser;
        
        if (!updatedBy) {
            setError('User is not logged in.');
            return;
        }
    
        if (!loggedInUsersDepartmentId || !selectedTeamId || !reporterName) {
            setError('Please fill all fields.');
            return;
        }
    
        // Check if the email has changed and if it already exists
        if (email !== existingEmail) {
            const emailExists = reportersData.reporters.some(reporter => reporter.email === email);
            if (emailExists) {
                setError('Email already exists. Please use a different email.');
                return;
            }
        }
    
        setError('');
    
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }
    
            const startTimeFormatted = convertTo12HourFormat(startTime);
            const endTimeFormatted = convertTo12HourFormat(endTime);
    
            const requestData = {
                department: loggedInUsersDepartmentId,
                team: selectedTeamId,
                name: reporterName,
                location: location,
                email: email,
                timezone: timezone,
                start_time: startTimeFormatted,
                end_time: endTimeFormatted,
                updated_by: updatedBy,
            };
    
            const response = await fetch(`${API_URL}reporter/${dataItemsId}`, {
                method: 'PUT', 
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData),
            });
    
            if (!response.ok) {
                const responseData = await response.json();
                console.error('Backend error details:', responseData);
                setError(responseData.message || 'Failed to update reporter.');
                return;
            }
    
            const result = await response.json();
            console.log("Edited Reporters data", result);

            //setSuccessMessage(`Reporter "${result.reporter.name}" updated successfully!`);

            // ✅ Success alert
            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Reporter updated successfully.',
            });

            setLoading(false);

            // Reset form
            setLoggedInUsersDepartmentId('');
            setSelectedTeamId('');
            setReporterName('');
            setLocation('');
            setEmail('');
            setTimezone('');
            setStartTime('');
            setEndTime('');

            setTimeout(() => {
                setVisible(false);
                setSuccessMessage(''); 
            }, 2000);

        } catch (error) {
            alertMessage('error');
            setLoading(false);
        }
    };

    const handleClose = () => {
        setVisible(false);
    };

    // Modal rendering
    return isVisible ? (
        <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
            <div className="bg-white rounded-lg shadow-md w-full max-w-4xl relative">
                <div className="flex justify-between items-center mb-4 bg-gray-100 px-4 py-2">
                        <h4 className="text-base text-left font-medium text-gray-800">Edit Reporter</h4>
                        <button
                            className="text-3xl text-gray-500 hover:text-gray-800"
                            onClick={handleClose}
                        >
                            &times;
                        </button>
                    </div>
                <form onSubmit={handleSubmit}>
                    <div className="flex flex-wrap p-6 overflow-y-auto max-h-[80vh] scrollbar-vertical text-left">
                        {/* Assigned Department */}
                        <div className="mb-4 w-full sm:w-1/2 px-4">
                            <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">
                                Department <span className='text-red-600'>*</span>
                            </label>
                            <select
                                id="department"
                                name="department"
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                                value={loggedInUsersDepartmentId} // Use the department ID here
                                onChange={(e) => setLoggedInUsersDepartmentId(e.target.value)} // Update department ID on change
                            >
                                <option value="" disabled>Select a department</option>
                                {/* Display only the logged-in user's department */}
                                {loggedInUsersDepartment && (
                                    <option value={loggedInUsersDepartmentId}>
                                        {loggedInUsersDepartment}
                                    </option>
                                )}
                            </select>
                        </div>

                        {/* Teams */}
                        <div className="mb-4 w-1/2 px-4">
                            <label htmlFor="team" className="block text-sm font-medium text-gray-700 pb-4">
                                Team <span className='text-red-600'>*</span>
                            </label>
                            <select
                                id="team"
                                name="team"
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                onChange={(e) => setSelectedTeamId(e.target.value)} // Handle team change
                                value={selectedTeamId || ""} // Set default value to selectedTeamId
                                required
                            >
                                <option value="">Select a team</option>
                                {teams && teams.length > 0 ? (
                                    teams.map((team) => (
                                        <option key={team.id} value={team.id}>
                                            {team.name}
                                        </option>
                                    ))
                                ) : (
                                    <option value="" disabled>No teams available</option>
                                )}
                            </select>
                        </div>

                        {/* Reporter Name */}
                        <div className="mb-4 w-full sm:w-1/2 px-4">
                            <label htmlFor="reporterName" className="block text-sm font-medium text-gray-700 pb-4">
                                Reporter Name <span className='text-red-600'>*</span>
                            </label>
                            <input
                                id="reporterName"
                                type="text"
                                value={reporterName}
                                onChange={(e) => setReporterName(e.target.value)}
                                placeholder="Add Reporter Name"
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            />
                        </div>

                        {/* Email */}
                        <div className="mb-4 w-full sm:w-1/2 px-4">
                            <label htmlFor="email" className="block text-sm font-medium text-gray-700 pb-4">
                                Email
                            </label>
                            <input
                                id="email"
                                type="email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                placeholder="Enter Reporter Email"
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                
                            />
                        </div>

                        {/* Location */}
                        <div className="mb-4 w-full sm:w-1/2 px-4">
                            <label htmlFor="location" className="block text-sm font-medium text-gray-700 pb-4">
                                Location <span className='text-red-600'>*</span>
                            </label>
                            {/* <select
                                id="location"
                                value={location}
                                onChange={handleLocationChange}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            >
                                <option value="">Select City</option>
                                {timeZoneData.map((cityData, index) => (
                                    <option key={index} value={cityData.city}>
                                        {cityData.city}
                                    </option>
                                ))}
                            </select> */}
                                <SearchFilterSelect
                                    id="location"
                                    options={timeZoneData.map(cityData => cityData.city)}
                                    value={location}
                                    onChange={handleLocationChange}
                                    placeholder="Search city..."
                                />
                        </div>

                        {/* Timezone */}
                        <div className="mb-4 w-full sm:w-1/2 px-4">
                            <label htmlFor="timezone" className="block text-sm font-medium text-gray-700 pb-4">
                                Timezone
                            </label>
                            <input
                                id="timezone"
                                type="text"
                                value={timezone}
                                onChange={(e) => setTimezone(e.target.value)}
                                placeholder="Enter Timezone"
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                
                                disabled

                            />
                        </div>

                        {/* Start Time */}
                        <div className="mb-4 w-full sm:w-1/2 px-4">
                            <label htmlFor="startTime" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                                Start Time <span className='text-red-600'>*</span>
                            </label>
                            <div className="relative">
                                <div className="absolute inset-y-0 end-0 top-0 flex items-center pe-3.5 pointer-events-none">
                                    <svg className="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                        <path fillRule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clipRule="evenodd"/>
                                    </svg>
                                </div>
                                <input
                                    type="time"
                                    id="startTime"
                                    value={startTime}
                                    onChange={(e) => setStartTime(e.target.value)}
                                    
                                    className="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                />
                            </div>
                        </div>

                        {/* End Time */}
                        <div className="mb-4 w-full sm:w-1/2 px-4">
                            <label htmlFor="endTime" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                                End Time <span className='text-red-600'>*</span>
                            </label>
                            <div className="relative">
                                <div className="absolute inset-y-0 end-0 top-0 flex items-center pe-3.5 pointer-events-none">
                                    <svg className="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                        <path fillRule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clipRule="evenodd"/>
                                    </svg>
                                </div>
                                <input
                                    type="time"
                                    id="endTime"
                                    value={endTime}
                                    onChange={(e) => setEndTime(e.target.value)}
                                    
                                    className="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                />
                            </div>
                        </div>
                    </div>

                    <div className='text-left p-6'>
                        <button
                            type="submit"
                            className="w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4"
                        >
                            <span class="material-symbols-rounded text-white text-xl font-regular">add_circle</span>
                            {loading ? 'Updating...' : 'Update Reporter'}
                        </button>
                    </div>
                </form>

            </div>
        </div>
    ) : null;
};

export default EditReporter;
