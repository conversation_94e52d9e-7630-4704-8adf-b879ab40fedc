import React, { useEffect, useState } from 'react';
import { alertMessage } from '../../common/coreui';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditResourceType = ({ isVisible, setVisible, dataItemsId }) => {
    const [resourceTypeName, setResourceTypeName] = useState('');
    const [resourceTypes, setResourceTypes] = useState([]); // Store resource types fetched from API
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    useEffect(() => {
        const fetchResourceTypeName = async () => {
            if (!dataItemsId) return;

            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}resource_types`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch resource types: ' + response.statusText);
                }

                const data = await response.json();
                const typeArray = data['Resource Types']; // Adjusted to access resource types correctly
                if (!Array.isArray(typeArray)) {
                    throw new Error('Expected resource types to be an array.');
                }

                const typeData = typeArray.find(type => type.id === dataItemsId);
                if (typeData) {
                    setResourceTypeName(typeData.name);  // Set the name from the matching resource type
                } else {
                    throw new Error('Resource type not found. Please check the ID.');
                }
            } catch (error) {
                setError(error.message);
            }
        };

        fetchResourceTypeName();
    }, [dataItemsId]);

    // Update the Resource Type
    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedTypeName = resourceTypeName.trim();

        
        const updatedBy = loggedInUser;

        if (!updatedBy) {
            setError('User is not logged in.');
            return;
        }

        if (Array.isArray(resourceTypes)) {
            const typeExists = resourceTypes.some(type => {
                const typeNameLower = type.name.toLowerCase().trim();
                return typeNameLower === trimmedTypeName.toLowerCase();
            });

            if (typeExists) {
                setError('Resource type already exists. Please add a different type.');
                const timeoutId = setTimeout(() => setError(''), 3000);
                return () => clearTimeout(timeoutId);
            }
        }

        setError(''); // Clear any previous error

        try {
            const token = localStorage.getItem('token');

            const response = await fetch(`${API_URL}resource_types/${dataItemsId}`, {
                method: 'PUT',  // Use PUT for updating
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedTypeName,
                    updated_by: updatedBy,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to update resource type: ' + response.statusText);
            }

            const result = await response.json();
            //setSuccessMessage(result.message || 'Resource type updated successfully!'); // Use message from API response

            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Resource type updated successfully.',
            });

            setResourceTypeName(''); // Clear the input after success

            // Optionally, refetch resource types after the update
            const newResourceTypesResponse = await fetch(`${API_URL}resource_types`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!newResourceTypesResponse.ok) {
                throw new Error('Failed to fetch resource types: ' + newResourceTypesResponse.statusText);
            }

            const newResourceTypesData = await newResourceTypesResponse.json();
            setResourceTypes(newResourceTypesData.resource_types || []);

            // Close the modal after a short delay
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage(''); // Clear the success message
            }, 1000);

        } catch (error) {
            alertMessage('error');
        }
    };

    if (!isVisible) return null;

    return (
        <div
            className="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50"
            onClick={() => setVisible(false)}
        >
            <div
                className="relative bg-white rounded-lg shadow-lg max-w-md w-full p-5"
                onClick={(e) => e.stopPropagation()} // Prevent click from closing the modal
            >
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Update Resource Type</h3>
                    <button
                        className="text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>
                {error && <div className="text-red-500">{error}</div>}
                {successMessage && <div className="text-green-500">{successMessage}</div>}
                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label htmlFor="name" className="block mb-2">Resource Type</label>
                        <input
                            type="text"
                            id="name"
                            value={resourceTypeName}
                            onChange={(e) => setResourceTypeName(e.target.value)}
                            className="border rounded w-full p-2"
                            required
                        />
                    </div>
                    <button
                        type="submit"
                        className="bg-primary hover:bg-secondary text-white rounded-md px-4 py-2"
                    >
                        Update Resource Type
                    </button>
                </form>
            </div>
        </div>
    );
};

export default EditResourceType;
