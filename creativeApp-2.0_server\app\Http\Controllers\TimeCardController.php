<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TimeCard;
use App\Models\TaskType;
use App\Models\ProductType;
use App\Models\RevisionType;
use App\Models\Region;
use App\Models\RecordType;
use App\Models\Priority;
use App\Models\Reporter;
use App\Models\Team;
use App\Models\Schedule;
use App\Models\ReviewRelease;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Validator;
use Carbon\Carbon;

use Illuminate\Support\Facades\Log;

class TimeCardController extends Controller
{
    /**
     * Show all time card with relevant relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $timeCards = TimeCard::with([
            'product_types',
            'task_types',
            'record_types',
            'revision_types',
            'regions',
            'priorities',
            'reporters',
        ])->get();

        // Log the time card retrieved
        Log::info('All time cards Retrieved:', ['timeCards_count' => $timeCards->count()]);

        return response()->json(['timeCards' => $timeCards], 200);
    }

    /**
     * Display the specified time card.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the time card by ID
        $timeCard = TimeCard::find($id);

        if (!$timeCard) {
            return response()->json(['error' => 'time card not found.'], 404);
        }

        // Log the time card retrieved
        Log::info('time card Retrieved:', ['timeCard' => $timeCard]);

        return response()->json(['timeCard' => $timeCard], 200);
    }


    public function formatHoursToHHMM($hours)
    {
        $hoursInt = floor($hours);
        $minutes = round(($hours - $hoursInt) * 60);

        // Pad with leading zeros
        return str_pad($hoursInt, 2, '0', STR_PAD_LEFT) . ':' . str_pad($minutes, 2, '0', STR_PAD_LEFT);
    }

    public function formatMillisecondsToHHMM($milliseconds)
    {
        if ($milliseconds <= 0) {
            return '00:00';
        }

        $totalMinutes = floor($milliseconds / 60000);
        $hours = floor($totalMinutes / 60);
        $minutes = $totalMinutes % 60;

        return str_pad($hours, 2, '0', STR_PAD_LEFT) . ':' . str_pad($minutes, 2, '0', STR_PAD_LEFT);
    }


    //Query and Filtered data for Data table
    public function timeCardsData(Request $request)
    {
        // Define the query with all necessary relationships
        $query = TimeCard::with(['creator', 'updater', 'team', 'departments', 'schedule', 'product_type', 'task_type', 'revision_type', 'record_type', 'region', 'priority', 'reporter', 'review']);

        // Decode input parameters for each field
        $decodedTeam = $request->filled('team_id') ? urldecode($request->input('team_id')) : null;
        $decodedDepartment = $request->filled('department_id') ? urldecode($request->input('department_id')) : null;
        $decodedDate = $request->filled('date') ? urldecode($request->input('date')) : null;
        $decodedDateRange = $request->filled('range') ? urldecode($request->input('range')) : null;
        $decodedMonth = $request->filled('month') ? urldecode($request->input('month')) : null;
        $decodedWeek = $request->filled('week') ? urldecode($request->input('week')) : null;
        $decodedYear = $request->filled('year') ? urldecode($request->input('year')) : null;
        $decodedQuarter = $request->filled('quarter') ? urldecode($request->input('quarter')) : null;
        $decodedShift = $request->filled('shift_id') ? urldecode($request->input('shift_id')) : null;
        $decodedUser = $request->filled('user_id') ? urldecode($request->input('user_id')) : null;
        $decodedTicketNumber = $request->filled('ticket') ? urldecode($request->input('ticket')) : null;
        $decodedProductType = $request->filled('product_type_id') ? urldecode($request->input('product_type_id')) : null;
        $decodedTaskType = $request->filled('task_type_id') ? urldecode($request->input('task_type_id')) : null;
        $decodedRecordType = $request->filled('record_type_id') ? urldecode($request->input('record_type_id')) : null;
        $decodedRevisionType = $request->filled('revision_type_id') ? urldecode($request->input('revision_type_id')) : null;
        $decodedPriority = $request->filled('priority_id') ? urldecode($request->input('priority_id')) : null;
        $decodedReview = $request->filled('review_id') ? urldecode($request->input('review_id')) : null;
        $decodedUnit = $request->filled('unit') ? urldecode($request->input('unit')) : null;
        $decodedHour = $request->filled('hour') ? urldecode($request->input('hour')) : null;
        $decodedReporter = $request->filled('reporter_id') ? urldecode($request->input('reporter_id')) : null;
        $decodedRegion = $request->filled('region_id') ? urldecode($request->input('region_id')) : null;
        $decodedAccount = $request->filled('account') ? urldecode($request->input('account')) : null;
        $decodedCampaign = $request->filled('campaign') ? urldecode($request->input('campaign')) : null;
        $decodedSla = $request->filled('sla') ? urldecode($request->input('sla')) : null;
        $decodedClientError = $request->filled('client_error') ? urldecode($request->input('client_error')) : null;
        $decodedNotes = $request->filled('notes') ? urldecode($request->input('notes')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;


        // Filtering: Add the logic for each new field

        $authUser = $request->user();

        // \Log::info('Authenticated User:', ['user' => $authUser]);
        // \Log::info('Roles:', ['roles' => $authUser->roles]);



        if (
            !$authUser->roles()->whereIn('name', [
                "super-admin",
                "admin",
                // "hod",
                "manager",
                "coordinator",
                "shift-lead"
            ])->exists()
        ) {
            $query->where('user_id', '=', trim($authUser->id));
        }


        if ($decodedTeam) {
            $teams = explode(',', $decodedTeam);
            $query->where(function ($q) use ($teams) {
                foreach ($teams as $team) {
                    $q->orWhere('team_id', '=', trim($team));
                }
            });
        }


        if ($decodedDepartment) {
            $departments = explode(',', $decodedDepartment);
            $query->where(function ($q) use ($departments) {
                foreach ($departments as $department) {
                    $q->orWhere('department_id', '=', trim($department));
                }
            });
        }

        // Filter by date
        if ($decodedDate) {
            $dates = explode(',', $decodedDate);
            $query->where(function ($q) use ($dates) {
                foreach ($dates as $date) {
                    $q->orWhere('date', 'like', '%' . trim($date) . '%');
                }
            });
        }

        if ($decodedDateRange) {
            // Expects a comma-separated string like "YYYY-MM-DD,YYYY-MM-DD"
            $dateRange = explode(',', $decodedDateRange);

            // Check that we have exactly two dates to form a range
            if (count($dateRange) === 2) {
                $startDate = trim($dateRange[0]);
                $endDate = trim($dateRange[1]);

                // Use whereBetween for proper and efficient date range filtering.
                // This assumes your 'date' column is a DATE, DATETIME, or TIMESTAMP type.
                $query->whereBetween('date', [$startDate, $endDate]);
            }
        }


        // Filter by date
        if ($decodedMonth) {
            $months = explode(',', $decodedMonth);
            $query->where(function ($q) use ($months) {
                foreach ($months as $month) {
                    $q->orWhere('month', 'like', '%' . trim($month) . '%');
                }
            });
        }

        if ($decodedWeek) {
            $weeks = explode(',', $decodedWeek);
            $query->where(function ($q) use ($weeks) {
                foreach ($weeks as $week) {
                    $q->orWhere('week', 'like', '%' . trim($week) . '%');
                }
            });
        }

        if ($decodedYear) {
            $years = explode(',', $decodedYear);
            $query->where(function ($q) use ($years) {
                foreach ($years as $year) {
                    $q->orWhere('year', 'like', '%' . trim(string: $year) . '%');
                }
            });
        }

        if ($decodedQuarter) {
            $quarters = explode(',', $decodedQuarter);
            $query->where(function ($q) use ($quarters) {
                foreach ($quarters as $quarter) {
                    $q->orWhere('quarter', 'like', '%' . trim(string: $quarter) . '%');
                }
            });
        }



        // Filter by shift
        if ($decodedShift) {
            $shifts = explode(',', $decodedShift);
            $query->where(function ($q) use ($shifts) {
                foreach ($shifts as $shift) {
                    $q->orWhere('shift_id', '=', trim($shift));
                }
            });
        }

        // Filter by user
        if ($decodedUser) {
            $users = explode(',', $decodedUser);
            $query->where(function ($q) use ($users) {
                foreach ($users as $user) {
                    $q->orWhere('user_id', '=', trim($user));
                }
            });
        }

        // Filter by ticket number
        if ($decodedTicketNumber) {
            $ticketNumbers = explode(',', $decodedTicketNumber);
            $query->where(function ($q) use ($ticketNumbers) {
                foreach ($ticketNumbers as $ticketNumber) {
                    $q->orWhere('ticket_number', 'like', '%' . trim($ticketNumber) . '%');
                }
            });
        }

        // Filter by product type
        if ($decodedProductType) {
            $productTypes = explode(',', $decodedProductType);
            $query->where(function ($q) use ($productTypes) {
                foreach ($productTypes as $productType) {
                    $q->orWhere('product_type_id', '=', trim($productType));
                }
            });
        }

        // Filter by task type
        if ($decodedTaskType) {
            $taskTypes = explode(',', $decodedTaskType);
            $query->where(function ($q) use ($taskTypes) {
                foreach ($taskTypes as $taskType) {
                    $q->orWhere('task_type_id', '=', trim($taskType));
                }
            });
        }

        // Filter by record type
        if ($decodedRecordType) {
            $recordTypes = explode(',', $decodedRecordType);
            $query->where(function ($q) use ($recordTypes) {
                foreach ($recordTypes as $recordType) {
                    $q->orWhere('record_type_id', '=', trim($recordType));
                }
            });
        }

        // Filter by revision type
        if ($decodedRevisionType) {
            $revisionTypes = explode(',', $decodedRevisionType);
            $query->where(function ($q) use ($revisionTypes) {
                foreach ($revisionTypes as $revisionType) {
                    $q->orWhere('revision_type_id', '=', trim($revisionType));
                }
            });
        }

        // Filter by priority
        if ($decodedPriority) {
            $priorities = explode(',', $decodedPriority);
            $query->where(function ($q) use ($priorities) {
                foreach ($priorities as $priority) {
                    $q->orWhere('priority_id', '=', trim($priority));
                }
            });
        }

        // Filter by review and release
        if ($decodedReview) {
            $reviews = explode(',', $decodedReview);
            $query->where(function ($q) use ($reviews) {
                foreach ($reviews as $review) {
                    $q->orWhere('review_id', '=', trim($review));
                }
            });
        }

        // Filter by unit
        if ($decodedUnit) {
            $units = explode(',', $decodedUnit);
            $query->where(function ($q) use ($units) {
                foreach ($units as $unit) {
                    $q->orWhere('unit', '=', trim($unit));
                }
            });
        }

        // Filter by hour
        if ($decodedHour) {
            $hours = explode(',', $decodedHour);
            $query->where(function ($q) use ($hours) {
                foreach ($hours as $hour) {
                    $q->orWhere('hour', '=', trim($hour));
                }
            });
        }

        // Filter by reporter
        if ($decodedReporter) {
            $reporters = explode(',', $decodedReporter);
            $query->where(function ($q) use ($reporters) {
                foreach ($reporters as $reporter) {
                    $q->orWhere('reporter_id', '=', trim($reporter));
                }
            });
        }

        // Filter by region
        if ($decodedRegion) {
            $regions = explode(',', $decodedRegion);
            $query->where(function ($q) use ($regions) {
                foreach ($regions as $region) {
                    $q->orWhere('region_id', '=', trim($region));
                }
            });
        }

        // Filter by account
        if ($decodedAccount) {
            $accounts = explode(',', $decodedAccount);
            $query->where(function ($q) use ($accounts) {
                foreach ($accounts as $account) {
                    $q->orWhere('account', 'like', '%' . trim($account) . '%');
                }
            });
        }

        // Filter by campaign
        if ($decodedCampaign) {
            $campaigns = explode(',', $decodedCampaign);
            $query->where(function ($q) use ($campaigns) {
                foreach ($campaigns as $campaign) {
                    $q->orWhere('campaign', 'like', '%' . trim($campaign) . '%');
                }
            });
        }

        // Filter by SLA
        if ($decodedSla) {
            $query->where('sla', '=', $decodedSla);
        }

        // Filter by client error
        if ($decodedClientError) {
            $query->where('client_error', '=', $decodedClientError);
        }

        // Filter by notes
        if ($decodedNotes) {
            $query->where('notes', 'like', '%' . trim($decodedNotes) . '%');
        }

        // Filter by created_by
        if ($decodedCreatedBy) {
            $query->where('created_by', '=', trim($decodedCreatedBy));
        }

        // Filter by updated_by
        if ($decodedUpdatedBy) {
            $query->where('updated_by', '=', trim($decodedUpdatedBy));
        }

        $summaryQuery = (clone $query);

        // Sorting logic
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';
        $query->orderBy($sortBy, $order);





        // Pagination
        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $taskrecords = $query->paginate($perPage, ['*'], 'page', $page);



        // --- SUMMARY METRICS --- created_at
        $uniqueDaysCount = (clone $summaryQuery)
            ->distinct('date')
            ->count('date');

        $totalDevelopers = (clone $summaryQuery)
            ->distinct('user_id')
            ->count('user_id');


        // $uniqueDaysCount = $summaryQuery->groupBy('date')->get()->count();

        // $totalTasks = $summaryQuery->count();
        $totalTasks = (clone $summaryQuery)
            ->distinct('ticket')
            ->count('ticket');
        $totalUnits = $summaryQuery->sum('unit');
        $totalWorkHours = $summaryQuery->where('hour', '!=', "00:00:00")
            ->pluck('hour')
            ->map(function ($hours) {
                $parts = explode(':', $hours);
                $hour = (int) $parts[0];
                $hour = $hour >= 12 ? $hour - 12 : $hour;
                $minute = (int) $parts[1];
                $second = (int) $parts[2];

                return ($hour * 3600) + ($minute * 60) + $second; // return as seconds
            })
            ->sum();
        // $totalWorkHours = $summaryQuery->selectRaw('SEC_TO_TIME(SUM(TIME_TO_SEC(hour))) as total_hours')->value('total_hours');

        // $totalHours = $uniqueDaysCount * 8 * 60 * 60 * 1000 * $totalDevelopers;

        // $idleHours = $totalHours - $totalWorkHours;
        // // Ensure idle hours never negative
        // $idleHours = max(0, $idleHours);

        // $idleHours = $summaryQuery->Where('task_type_id', '=', 10)->sum('hour');
        $idleHours = $summaryQuery
            ->where('task_type_id', '=', 10)
            ->where('hour', '!=', "00:00:00")
            ->pluck('hour')
            ->map(function ($hours) {
                $parts = explode(':', $hours);
                $hour = (int) $parts[0];
                $hour = $hour >= 12 ? $hour - 12 : $hour;
                $minute = (int) $parts[1];
                $second = (int) $parts[2];

                return ($hour * 3600) + ($minute * 60) + $second; // return as seconds
            })
            ->sum();


        $clientErrors = $summaryQuery->sum('client_error');
        $internalErrors = $summaryQuery->sum('internal_error');

        $activeTime = $totalWorkHours - $idleHours;
        $activeTimePercent = $totalWorkHours > 0 ? round(($activeTime / $totalWorkHours) * 100, 2) : 0;

        $clientErrorRate = $totalTasks > 0 ? round(($clientErrors / $totalTasks) * 100, 2) : 0;
        $internalErrorRate = $totalTasks > 0 ? round(($internalErrors / $totalTasks) * 100, 2) : 0;

        // $testResult = $summaryQuery->selectRaw('SUM(hour) as total_hours')->value('total_hours');
        $testResult = $summaryQuery->where('hour', '!=', "00:00:00")
            ->pluck('hour')
            ->map(function ($hours) {
                $parts = explode(':', $hours);
                $hour = (int) $parts[0];
                $hour = $hour >= 12 ? $hour - 12 : $hour;
                $minute = (int) $parts[1];
                $second = (int) $parts[2];

                return ($hour * 3600) + ($minute * 60) + $second; // return as seconds
            })
            ->sum();

        return response()->json([
            'authUserRoles' => $authUser->id,
            'authUser' => $authUser,
            'testResult' => $testResult,
            'data' => $taskrecords,
            'summary' => [
                'total_developers' => $totalDevelopers,
                'total_tasks' => $totalTasks,
                'total_units' => $totalUnits,
                'unique_days' => $uniqueDaysCount,

                // 'total_work_hours' => $this->formatMillisecondsToHHMM($totalWorkHours),
                // 'idle_hours' => $this->formatMillisecondsToHHMM($idleHours),
                // 'expected_work_hours' => $this->formatMillisecondsToHHMM($totalHours),

                'total_work_hours' => $totalWorkHours,
                'idle_hours' => $idleHours,
                // 'expected_work_hours' => $totalHours,

                'client_errors' => $clientErrors,
                'internal_errors' => $internalErrors,
                'active_time_percent' => $activeTimePercent,
                'client_error_rate' => $clientErrorRate,
                'internal_error_rate' => $internalErrorRate,
            ]
        ], 200);




        // return response()->json($taskrecords, 200);
    }



    public function group(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }


        // Build the query: Select the group column and the count of records in each group.
        $results = TimeCard::with([
            'creator',
            'updater',
            'team',
            'departments',
            'schedule',
            'product_type',
            'task_type',
            'revision_type',
            'record_type',
            'region',
            'priority',
            'reporter',
            'review'
        ]);


        $results->select($column, $column . ' as ticket', \DB::raw("COUNT(*) as total"));

        $results->groupBy($column)->orderBy($column);


        return response()->json($results->get(), 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'title' parameter from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }

        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the 'title' column using a partial match
        $results = TaskDetails::with([
            'creator',
            'updater',
            'team',
            'departments',
            'schedule',
            'product_type',
            'task_type',
            'revision_type',
            'record_type',
            'region',
            'priority',
            'reporter',
            'review'
        ]);


        if (strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);

            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        } else {
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }



    /**
     * Create a new time card by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id,
            'fname' => $authUser->fname,
            'lname' => $authUser->lname
        ]);

        // Ensure the user is authenticated
        if (!auth()->check()) {
            Log::warning('Unauthorized access attempt: No authenticated user.');
            return response()->json(['error' => 'Unauthorized'], 401); // Unauthorized if user is not logged in
        }

        // Log the authenticated user's details
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);

        // Validate the request data
        try {
            $validatedData = $request->validate([
                'team_id' => 'required|integer',
                'department_id' => 'required|integer',
                'date' => 'required|date',
                'shift_id' => 'required|integer',
                'user_id' => 'required|integer',
                'ticket' => 'required|string|max:255',
                'product_type_id' => 'nullable|integer',
                'task_type_id' => 'required|integer',
                'record_type_id' => 'nullable|integer',
                'revision_type_id' => 'nullable|integer',
                'priority_id' => 'nullable|integer',
                'unit' => 'nullable|integer',
                'hour' => 'required|regex:/^\d+:[0-5]\d$/',
                // 'hour' => 'required|date_format:H:i',
                'reporter_id' => 'nullable|integer',
                'region_id' => 'nullable|integer',
                'review_id' => 'nullable|integer',
                // 'workflow_id' => 'nullable|integer',
                'account' => 'nullable|string|max:255',
                'campaign' => 'nullable|string|max:255',
                'sla' => 'nullable|string|max:255',
                'client_error' => 'nullable|integer',
                'internal_error' => 'nullable|integer',
                'notes' => 'nullable|string',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Log validation errors
            Log::error('Validation failed:', ['errors' => $e->errors()]);
            return response()->json(['error' => 'Validation failed', 'timeCards' => $e->errors()], 422);
        }

        // Log the request data after validation
        Log::info('Create time card Request:', ['request' => $validatedData]);

        // Check if the user has the appropriate role
        if (!$authUser->roles()->whereIn('name', ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'])->exists()) {
            Log::warning('Unauthorized time card Creation Attempt:', ['user_id' => $authUser->id]);
            return response()->json(['error' => 'You do not have permission to create time card.'], 403);
        }

        // Log role-based access check success
        Log::info('User has required role:', ['user_id' => $authUser->id, 'role' => 'super-admin/admin']);



        // Attempt to create a new time card record
        try {
            $date = Carbon::parse($validatedData['date']);
            $timeCard = TimeCard::create([
                'team_id' => $validatedData['team_id'],
                'department_id' => $validatedData['department_id'],
                'date' => $validatedData['date'],
                'month' => $date->format('F'),            // "May", "June", etc.
                'week' => $date->weekOfYear,              // 1-53
                'year' => $date->year,                    // 2025
                'quarter' => $date->quarter,              // 1-4
                'shift_id' => $validatedData['shift_id'],
                'user_id' => $validatedData['user_id'],
                'ticket' => $validatedData['ticket'],
                'product_type_id' => $validatedData['product_type_id'],
                'task_type_id' => $validatedData['task_type_id'],
                'record_type_id' => $validatedData['record_type_id'],
                'revision_type_id' => $validatedData['revision_type_id'],
                'priority_id' => $validatedData['priority_id'],
                'unit' => $validatedData['unit'],
                'hour' => $validatedData['hour'],
                'reporter_id' => $validatedData['reporter_id'],
                'region_id' => $validatedData['region_id'],
                'review_id' => $validatedData['review_id'],
                // 'workflow_id' => $validatedData['workflow_id'],
                'account' => $validatedData['account'],
                'campaign' => $validatedData['campaign'],
                'sla' => $validatedData['sla'],
                'client_error' => $validatedData['client_error'],
                'internal_error' => $validatedData['internal_error'],
                'notes' => $validatedData['notes'],
                'created_by' => $authUser->id,
            ]);

            $timeCard->updated_at = null;
            $timeCard->saveQuietly();


            // Log the successful creation of the time card
            Log::info('Time card created successfully:', ['timeCard' => $timeCard]);

            // Return success response
            return response()->json([
                'message' => 'Time card created successfully.',
                'timeCard' => $timeCard
            ], 201);
        } catch (\Exception $e) {
            // Log error during time card creation
            Log::error('Error creating time card:', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to create time card. Please try again later.'], 500);
        }
    }


    /**
     * Update an existing time card by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id,
            'fname' => $authUser->fname,
            'lname' => $authUser->lname
        ]);

        // Ensure the user is authenticated
        if (!auth()->check()) {
            Log::warning('Unauthorized access attempt: No authenticated user.');
            return response()->json(['error' => 'Unauthorized'], 401); // Unauthorized if user is not logged in
        }

        // Log the authenticated user's details
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);

        // Validate the request data
        try {
            $validatedData = $request->validate([
                'team_id' => 'required|integer',
                'department_id' => 'required|integer',
                'date' => 'required|date',
                'shift_id' => 'required|integer',
                'user_id' => 'required|integer',
                'ticket' => 'required|string|max:255',
                'product_type_id' => 'nullable|integer',
                'task_type_id' => 'required|integer',
                'record_type_id' => 'nullable|integer',
                'revision_type_id' => 'nullable|integer',
                'priority_id' => 'nullable|integer',
                'unit' => 'nullable|integer',
                'hour' => 'required|regex:/^\d+:[0-5]\d$/',
                // 'hour' => 'required|date_format:H:i',
                'reporter_id' => 'nullable|integer',
                'region_id' => 'nullable|integer',
                'review_id' => 'nullable|integer',
                // 'workflow_id' => 'nullable|integer',
                'account' => 'nullable|string|max:255',
                'campaign' => 'nullable|string|max:255',
                'sla' => 'nullable|string|max:255',
                'client_error' => 'nullable|integer',
                'internal_error' => 'nullable|integer',
                'notes' => 'nullable|string',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Log validation errors
            Log::error('Validation failed:', ['errors' => $e->errors()]);
            return response()->json(['error' => 'Validation failed', 'timeCards' => $e->errors()], 422);
        }

        // Log the request data after validation
        Log::info('Update time card Request:', ['request' => $validatedData]);

        // Check if the user has the appropriate role
        if (!$authUser->roles()->whereIn('name', ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'])->exists()) {
            Log::warning('Unauthorized time card Update Attempt:', ['user_id' => $authUser->id]);
            return response()->json(['error' => 'You do not have permission to update time card.'], 403);
        }

        // Log role-based access check success
        Log::info('User has required role:', ['user_id' => $authUser->id, 'role' => 'super-admin/admin']);

        // Find the time card by ID
        $timeCard = TimeCard::find($id);

        if (!$timeCard) {
            // If time card not found, log error and return response
            Log::error('Time card not found:', ['timeCard_id' => $id]);
            return response()->json(['error' => 'Time card not found.'], 404);
        }

        // Attempt to update the time card record
        try {

            $date = Carbon::parse($validatedData['date']);
            $timeCard->update([
                'team_id' => $validatedData['team_id'],
                'department_id' => $validatedData['department_id'],
                'date' => $validatedData['date'],
                'month' => $date->format('F'),            // "May", "June", etc.
                'week' => $date->weekOfYear,              // 1-53
                'year' => $date->year,                    // 2025
                'quarter' => $date->quarter,              // 1-4
                'shift_id' => $validatedData['shift_id'],
                'user_id' => $validatedData['user_id'],
                'ticket' => $validatedData['ticket'],
                'product_type_id' => $validatedData['product_type_id'],
                'task_type_id' => $validatedData['task_type_id'],
                'record_type_id' => $validatedData['record_type_id'],
                'revision_type_id' => $validatedData['revision_type_id'],
                'priority_id' => $validatedData['priority_id'],
                'unit' => $validatedData['unit'],
                'hour' => $validatedData['hour'],
                'reporter_id' => $validatedData['reporter_id'],
                'region_id' => $validatedData['region_id'],
                'review_id' => $validatedData['review_id'],
                // 'workflow_id' => $validatedData['workflow_id'],
                'account' => $validatedData['account'],
                'campaign' => $validatedData['campaign'],
                'sla' => $validatedData['sla'],
                'client_error' => $validatedData['client_error'],
                'internal_error' => $validatedData['internal_error'],
                'notes' => $validatedData['notes'],
                'updated_by' => $authUser->id,
            ]);

            // Log the successful update of the time card
            Log::info('Time card updated successfully:', ['timeCard' => $timeCard]);

            // Return success response
            return response()->json([
                'message' => 'Time card updated successfully.',
                'timeCard' => $timeCard
            ], 200);
        } catch (\Exception $e) {
            // Log error during time card update
            Log::error('Error updating time card:', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to update time card. Please try again later.'], 500);
        }
    }


    /**
     * Delete a time card by Super Admin or Admin.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        $timeCard = TimeCard::findOrFail($id);

        // Check if the user has the appropriate role
        if ($timeCard->user_id === $authUser->id || $authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the time card

            // Delete the time card
            $timeCard->delete();

            return response()->json(['message' => 'time card deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete this time card.'], 403);
    }
}
