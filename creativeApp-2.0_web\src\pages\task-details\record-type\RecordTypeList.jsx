import React, { useEffect, useState } from 'react';
import TableContent from '../../../common/table/TableContent';
import EditRecordType from './EditRecordType';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null; 
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const RecordTypeList = () => {
    const [recordTypes, setRecordTypes] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedRecordTypeId, setSelectedRecordTypeId] = useState(null);
    const [error, setError] = useState(null);

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Product Type Name", key: "name" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchRecordTypes = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}record-types`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                console.log('Task Details:', data);
                
                setRecordTypes(data.recordTypes.map(recordType => ({
                    id: recordType.id,
                    department: recordType.department,
                    team: recordType.team,
                    name: recordType.name,
                    created_by: recordType.created_by,
                    updated_by: recordType.updated_by,
                })));
            } catch (error) {
                setError(error.message);
            }
        };

        fetchRecordTypes();
    }, []);

    // Function to format date to dd/mm/yy
    const formatDate = (dateString) => {
        if (!dateString) return ''; // Return an empty string if there's no date
        const date = new Date(dateString);
        const options = { day: '2-digit', month: '2-digit', year: '2-digit' };
        return date.toLocaleDateString('en-GB', options); // 'en-GB' for dd/mm/yy format
    };

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}record-type/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete record type: ' + response.statusText);
            }

            // Update the record types list after deletion
            setRecordTypes(prevRecordTypes => prevRecordTypes.filter(recordType => recordType.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedRecordTypeId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    // Format the data for display (handling date formatting here)
    const formattedRecordTypes = recordTypes.map(recordType => ({
        ...recordType,
        created_at: formatDate(recordType.created_at),
        updated_at: formatDate(recordType.updated_at),
    }));

    return (
        <div>
            <TableContent
                tableContent={formattedRecordTypes} // Use formatted data here
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedRecordTypeId}
            />
            {modalVisible && (
                <EditRecordType
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    recordTypeId={selectedRecordTypeId}
                />
            )}
        </div>
    );
};

export default RecordTypeList;
