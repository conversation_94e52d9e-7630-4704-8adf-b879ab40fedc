import React, { useEffect, useState, useRef } from 'react';
import TableContent from '../../../common/table/TableContent';
import EditReporter from './EditReporter';
import useFetchApiData from './../../../common/fetchData/useFetchApiData';
import TablePagination from '../../../common/table/TablePagination';
import { API_URL } from './../../../common/fetchData/apiConfig';
import AddReporter from './AddReporter';
import CommonClock from './../../../common/clock/CommonClock'; // Import CommonClock
import CurrentTimeByIp from './../../../common/clock/CurrentTimeByIp'; // Import CurrentTimeByIp component
import moment from 'moment-timezone'; // Make sure you have moment-timezone

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

// Convert 24-hour time to 12-hour format with AM/PM
const convertTo12HourFormat = (time24) => {
    if (typeof time24 !== 'string' || !time24.includes(':')) return time24;  // Handle invalid time format

    let [hours, minutes] = time24.split(':');
    hours = parseInt(hours, 10);
    const suffix = hours >= 12 ? 'PM' : 'AM';
    if (hours > 12) hours -= 12;
    if (hours === 0) hours = 12;
    return `${hours}:${minutes} ${suffix}`;
};

const ReporterList = () => {
    const [reporters, setReporters] = useState([]);
    const [error, setError] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedReporterId, setSelectedReporterId] = useState(null);
    const [teams, setTeams] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [filteredReporters, setFilteredReporters] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;
    const [addModalVisible, setAddModalVisible] = useState(false);
    const timeDataRef = useRef(null);

    const [currentTime, setCurrentTime] = useState('');


    const token = localStorage.getItem('token');

    // Ref to store previous current time to avoid infinite loop
    const previousTimeRef = useRef({});

    // Fetch Data
    const { data: teamsData } = useFetchApiData(`${API_URL}/teams`, token);
    const { data: departmentsData } = useFetchApiData(`${API_URL}departments`, token);
    const { data: reportersData } = useFetchApiData(`${API_URL}reporters`, token);

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Reporter Name", key: "name" },
        { label: "Email", key: "email" },
        { label: "Reporter Location", key: "location" },
        { label: "Reporter Time Zone", key: "timezone" },
        { label: "Reporter Current Date", key: "current_time" },
        { label: "Current Time", key: "current_time_only" },
        { label: "Local Time", key: "local_time" },
        { label: "Online Status", key: "online_status" },
        { label: "Office Schedule", key: "office_schedule" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    // Reporters Current Time based one Converted Timezone
    const convertDateTime = (currentDateTime,  toTimezone="America/Los_Angeles", fromTimezone="Asia/Dhaka", ) => {
        if (!currentDateTime || !toTimezone) return null;

        const fromTime = moment.tz(currentDateTime, fromTimezone);
        var convertedTime = fromTime.clone().tz(toTimezone);
        return convertedTime.isValid() ? convertedTime.format("LLL") : null;
    };

    // Extract only date (without time) from current datetime
    const getCurrentDateOnlyInTimezone = (timezone) => {
        return moment().tz(timezone).format('dddd, Do MMMM YYYY');
    };

    // Extract only time (hh:mm AM/PM) from current time
    const getCurrentTimeOnlyInTimezone = (timezone) => {
        return moment().tz(timezone).format('hh:mm A');
    };

    useEffect(() => {
        if (teamsData) setTeams(teamsData.teams || []);
        if (departmentsData) setDepartments(departmentsData.departments || []);
    
        if (reportersData) {
            const teamsMap = teams.reduce((map, team) => {
                map[team.id] = team.name;
                return map;
            }, {});
    
            const departmentsMap = departments.reduce((map, department) => {
                map[department.id] = department.name;
                return map;
            }, {});
    
            const mappedReporters = reportersData.reporters.map((reporter) => {
                const startTimeFormatted = convertTo12HourFormat(reporter.start_time);  // Ensure 12-hour format
                const endTimeFormatted = convertTo12HourFormat(reporter.end_time);      // Ensure 12-hour format
    
                // Convert the current_time to the 12-hour format for comparison
                const currentTimeFormatted = currentTime ? convertTo12HourFormat(currentTime) : null;
    
                // Log to verify time format
                console.log(`Raw Times - currentTime: ${currentTimeFormatted}, start_time: ${startTimeFormatted}, end_time: ${endTimeFormatted}`);
    
                // Convert both current time and office times to 24-hour format
                const currentTime24 = currentTimeFormatted ? convertTo24HourFormat(currentTimeFormatted) : null;
                const startTime24 = convertTo24HourFormat(startTimeFormatted);
                const endTime24 = convertTo24HourFormat(endTimeFormatted);
    
                // Log the converted times
                console.log(`Converted Times - currentTime24: ${currentTime24}, startTime24: ${startTime24}, endTime24: ${endTime24}`);
    
                // Direct string comparison for checking if current time is within office hours
                const isOnline = currentTime24 && startTime24 && endTime24
                    ? currentTime24 >= startTime24 && currentTime24 <= endTime24
                    : false;
    
                // Return mapped reporter data
                return {
                    id: reporter.id,
                    department: departmentsMap[reporter.team] || '',
                    team: teamsMap[reporter.team] || '',
                    name: reporter.name,
                    email: reporter.email,
                    location: reporter.location,
                    timezone: reporter.timezone,
                    current_time: getCurrentDateOnlyInTimezone(reporter.timezone),
                    current_time_only: getCurrentTimeOnlyInTimezone(reporter.timezone),
                    local_time: timeDataRef.current ? convertTo12HourFormat(timeDataRef.current.formattedTime) : "",
                    online_status: isOnline ? 'Online' : 'Offline',
                    office_schedule: `${startTimeFormatted} to ${endTimeFormatted}`,
                    created_by: reporter.created_by,
                    updated_by: reporter.updated_by,
                };
            });
    
            setReporters(mappedReporters);
            setFilteredReporters(mappedReporters);
        }
    }, [reportersData, teamsData, departmentsData, currentPage, itemsPerPage, currentTime]);
    
    // Helper function to convert 12-hour time format to 24-hour format
    const convertTo24HourFormat = (time12) => {
        if (typeof time12 !== 'string' || !time12.includes(':')) return time12;  // Handle invalid time format
    
        const [time, suffix] = time12.split(' ');
        let [hours, minutes] = time.split(':');
        hours = parseInt(hours, 10);
    
        if (suffix === 'PM' && hours < 12) hours += 12;  // Convert PM to 24-hour
        if (suffix === 'AM' && hours === 12) hours = 0;  // Convert 12 AM to 00:00
    
        const formattedTime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
        return formattedTime;
    };
    
       
    // Handle Local Time from CommonClock
    const handleLocalTimeData = (data) => {
        if (!data.formattedTime) {
            console.error("Invalid time format:", data.formattedTime);
            return;
        }

        if (!timeDataRef.current || timeDataRef.current.formattedTime !== data.formattedTime) {
            timeDataRef.current = data;

            setReporters((prevReporters) => {
                return prevReporters.map((reporter) => {
                    const reporterTime = moment.tz(data.formattedTime, reporter.timezone);
                    const localTime = reporterTime.isValid()
                        ? convertTo12HourFormat(reporterTime.format('HH:mm'))
                        : "Invalid Time";

                    return {
                        ...reporter,
                        current_time: getCurrentDateOnlyInTimezone(reporter.timezone),  // Update to show only date
                        local_time: localTime,  // Correctly setting local time
                        current_time_only: getCurrentTimeOnlyInTimezone(reporter.timezone),  // Update time-only field
                    };
                });
            });
        }
    };

    // Handle Current Time from CurrentTimeByIp based on Timezone
    const handleCurrentTimeData = (data) => {
        // Debug the incoming data to ensure proper format
        console.log("Received current time data:", data);

        if (!data.formattedTime) {
            console.error("Invalid time format:", data.formattedTime);
            return;
        }

        // Ensure the formatted time is a valid date string
        const formattedTime = moment(data.formattedTime, 'YYYY-MM-DD HH:mm:ss'); // Parse the time as a moment object

        // Check if the formatted time is valid
        if (!formattedTime.isValid()) {
            console.error("Invalid time format detected:", data.formattedTime);
            return;
        }

        // Log before updating current time
        console.log("Before updating current_time:", reporters);

        // Update the `currentTime` state
        setCurrentTime(formattedTime.format('YYYY-MM-DD HH:mm:ss'));

        // Update reporters' current time based on their timezones
        setReporters((prevReporters) => {
            const updatedReporters = prevReporters.map((reporter) => {
                // Ensure the reporter's timezone is valid and apply conversion
                const reporterTime = formattedTime.tz(reporter.timezone);
                
                // Format the current time for the reporter in 12-hour AM/PM format
                const current_time = reporterTime.isValid()
                    ? convertTo12HourFormat(reporterTime.format('HH:mm')) // Convert to AM/PM format
                    : "Invalid Time";

                console.log(`Updated Time for ${reporter.name}: ${current_time}`);


                return {
                    ...reporter,
                    current_time: getCurrentDateOnlyInTimezone(reporter.timezone),  // Update to show only date
                    current_time_only: getCurrentTimeOnlyInTimezone(reporter.timezone),  // Update time-only field
                };
            });

            // Log after updating current time
            console.log("After updating current_time:", updatedReporters);

            return updatedReporters;
        });
    };


    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}reporter/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete reporter: ' + response.statusText);
            }

            // Update the reporters list after deletion
            setReporters(prevReporters => prevReporters.filter(reporter => reporter.id !== id));

        } catch (error) {
            setError(error.message);
        }
    };

    // Pagination logic
    const startIndex = (currentPage - 1) * itemsPerPage;
    const currentPageReporters = filteredReporters.slice(startIndex, startIndex + itemsPerPage);

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedReporterId(id);
        setModalVisible(true);
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <CurrentTimeByIp currentTimeData={handleCurrentTimeData} />
            <CommonClock onTimeData={handleLocalTimeData} />
            <TableContent
                tableContent={currentPageReporters}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedReporterId}
            />
            <TablePagination
                currentPage={currentPage}
                totalItems={filteredReporters.length}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
            />

            {addModalVisible && (
                <AddReporter
                    isVisible={addModalVisible}
                    setVisible={setAddModalVisible}
                />
            )}

            {modalVisible && (
                <EditReporter
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    reporterId={selectedReporterId}
                />
            )}
        </div>
    );
};

export default ReporterList;
