<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Role;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "Assigning team-lead roles...\n";

// Get the team-lead role
$teamLeadRole = Role::where('name', 'team-lead')->first();
if (!$teamLeadRole) {
    echo "Team-lead role not found!\n";
    exit(1);
}

// Get some users to assign team-lead role (excluding super-admin and managers)
$users = User::whereDoesntHave('roles', function($q) {
    $q->whereIn('name', ['super-admin', 'manager']);
})->take(2)->get();

foreach ($users as $user) {
    // Check if user already has team-lead role
    if (!$user->roles->contains('name', 'team-lead')) {
        $user->roles()->attach($teamLeadRole->id);
        echo "Assigned team-lead role to: {$user->fname} {$user->lname}\n";
    } else {
        echo "User {$user->fname} {$user->lname} already has team-lead role\n";
    }
}

echo "Done!\n";
echo "Total users with team-lead role: " . $teamLeadRole->users()->count() . "\n";
