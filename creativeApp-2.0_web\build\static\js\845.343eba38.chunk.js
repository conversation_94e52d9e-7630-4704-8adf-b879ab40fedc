"use strict";(self.webpackChunkcreativeapp=self.webpackChunkcreativeapp||[]).push([[845],{68756:(e,t,a)=>{a.d(t,{A:()=>n});var s=a(65043),r=a(56025),l=a(70579);const o="https://creative.sebpo.net/api/",n=e=>{let{isVisible:t,setVisible:a,dataItemsId:n}=e;const[i,d]=(0,s.useState)(""),[c,u]=(0,s.useState)(""),[m,x]=(0,s.useState)(""),[g,p]=(0,s.useState)(null);(0,s.useEffect)((()=>{const e=localStorage.getItem("user_id");e&&p(e)}),[]),(0,s.useEffect)((()=>{(async()=>{if(n){const e=localStorage.getItem("token");try{const t=await fetch(`${o}bloods/${n}`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch blood group: "+t.statusText);const a=await t.json();d(a.blood.name)}catch(c){u(c.message)}}})()}),[n]);return t?(0,l.jsx)("div",{className:"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50",onClick:()=>a(!1),children:(0,l.jsxs)("div",{className:"relative bg-white rounded-lg shadow-lg max-w-md w-full p-5",onClick:e=>e.stopPropagation(),children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Update Blood Group"}),(0,l.jsx)("button",{className:"text-gray-500 hover:text-gray-800",onClick:()=>a(!1),children:"\xd7"})]}),c&&(0,l.jsx)("div",{className:"text-red-500",children:c}),m&&(0,l.jsx)("div",{className:"text-green-500",children:m}),(0,l.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t=localStorage.getItem("token"),s=g;if(s)if(t)try{const e=await fetch(`${o}bloods/${n}`,{method:"PUT",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({name:i.trim(),updated_by:s})});if(!e.ok)throw new Error("Failed to update blood group: "+e.statusText);const l=await e.json();(0,r.GW)({icon:"success",title:"Success!",text:(null===l||void 0===l?void 0:l.message)||"Blood group updated successfully."}),setTimeout((()=>{a(!1),x("")}),1e3)}catch(c){(0,r.GW)("error")}else u("Authentication token is missing.");else u("User is not logged in.")},children:[(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsx)("label",{htmlFor:"name",className:"block mb-2",children:"Blood Group Name"}),(0,l.jsx)("input",{type:"text",id:"name",value:i,onChange:e=>d(e.target.value),className:"border rounded w-full p-2",required:!0})]}),(0,l.jsx)("button",{type:"submit",className:"bg-primary hover:bg-secondary text-white rounded-md px-4 py-2",children:"Update Blood Group"})]})]})}):null}},95845:(e,t,a)=>{a.r(t),a.d(t,{default:()=>v});var s=a(65043),r=a(58786),l=a(13076),o=a(56025),n=a(83003),i=a(47554),d=a(72450),c=a(11238),u=a(32650),m=a(73216),x=a(68756),g=a(60301),p=a(17974),h=a(58598),f=a(70579);const b="Blood Group",y=()=>{const[e,t]=(0,s.useState)({}),[a,y]=(0,s.useState)({}),[v,j]=(0,s.useState)(""),[w,N]=(0,s.useState)(""),[k,_]=(0,s.useState)(!1),[S,C]=(0,s.useState)(!1),[A,$]=(0,s.useState)(null),[F,O]=(0,s.useState)(null),[T,E]=(0,s.useState)(null),[R,B]=((0,m.Zp)(),(0,s.useState)(!1)),[P,D]=(0,s.useState)("created_at"),[I,G]=(0,s.useState)("desc"),[M,U]=(0,s.useState)("10"),[V,z]=(0,s.useState)(1),{data:W,isFetching:q,error:H}=(0,u.FQ9)({sort_by:P,order:I,page:V,per_page:M,query:w}),[L,{data:Q,error:J}]=(0,u.krn)(),[Y]=(0,u.pTt)(),Z=e=>{let t=Object.entries(e).reduce(((e,t)=>{let[a,s]=t;if("string"===typeof s)return e+`&${a}=${s}`;if(Array.isArray(s)){return e+`&${a}=${s.map((e=>e.value)).join(",")}`}return e}),"");N(t)},K=e=>{(0,i.$3)(e,["id","team","department","updated_at","updated_by","updater","created_at","creator","created_by","updated_by"]);E(null),_(!0)},X=e=>{E(null),$(e),_(!0)},ee=e=>{(0,o.YU)({onConfirm:()=>{Y(e),E(null)}})};let te=1;const{rolePermissions:ae}=(0,p.h)(),[se,re]=(0,s.useState)((()=>[{id:te++,name:"Action",width:"180px",className:"bg-red-300",cell:e=>(0,f.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,f.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>E(e),children:(0,f.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,f.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>X(e.id),children:(0,f.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,f.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>K(e),children:(0,f.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,f.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,f.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})},{id:te++,name:"S.No",selector:(e,t)=>(V-1)*M+t+1,width:"80px",omit:!1},{id:te++,name:"Resource Status Name",db_field:"name",selector:e=>e.name||"",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created by",selector:e=>{var t,a;return`${(null===(t=e.creator)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.creator)||void 0===a?void 0:a.lname)||""}`},db_field:"created_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Date",selector:e=>(0,h.hb)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Time",selector:e=>(0,h.DF)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!1},{id:te++,name:"Updated by",selector:e=>{var t,a;return`${(null===(t=e.updater)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.updater)||void 0===a?void 0:a.lname)||""}`},db_field:"updated_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Date",selector:e=>(0,h.hb)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Time",selector:e=>(0,h.DF)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!1}]));(0,s.useEffect)((()=>{re((e=>[...e.map((e=>"Action"===e.name?{...e,cell:e=>(0,f.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,f.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>E(e),children:(0,f.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,f.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>X(e.id),children:(0,f.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,f.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>K(e),children:(0,f.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,f.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,f.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})}:e))]))}),[ae]);const le=(0,n.wA)(),oe=(0,s.useCallback)((async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"group",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"select",l=e.db_field||"title";try{j(l),C(!0);var o=[];const n=await L({type:a.trim(),column:l.trim(),text:s.trim()});if(n.data&&(o=n.data),o.length){if("searchable"===r)return t((e=>({...e,[l]:o}))),o;const a=o.map((t=>{if(e.selector){let a=e.selector(t);return a?(t.total&&t.total>1&&(a+=` (${t.total})`),{label:a,value:t[l]}):null}})).filter(Boolean);return t((t=>({...t,[e.id]:(0,i.eb)(a)}))),a}}catch(F){O(F.message)}finally{C(!1)}}),[]);return(0,f.jsx)("section",{className:"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]",children:(0,f.jsxs)("div",{className:"mx-auto pb-6 ",children:[(0,f.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4",children:[(0,f.jsx)("div",{className:"w-4/12 md:w-10/12 text-start",children:(0,f.jsx)("h2",{className:"text-2xl font-bold ",children:b})}),(0,f.jsxs)("div",{className:"w-8/12 flex items-end justify-end gap-1",children:[(0,f.jsx)(o.DF,{columns:se,setColumns:re}),!q&&W&&parseInt(W.total)>0&&(0,f.jsx)(f.Fragment,{children:(0,f.jsxs)("button",{className:"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:async()=>{try{const t=await le(u.cAH.endpoints.getBloodGroupData.initiate({sort_by:P,order:I,page:V,per_page:(null===W||void 0===W?void 0:W.total)||10,query:w})).unwrap();if(null===t||void 0===t||!t.total||t.total<1)return!1;var e=1;let a=t.data.map((t=>{if(se.length){let a={};return se.forEach((s=>{!s.omit&&s.selector&&(a[s.name]="S.No"===s.name?e++:s.selector(t)||"")})),a}}));const s=c.Wp.json_to_sheet(a),r=c.Wp.book_new();c.Wp.book_append_sheet(r,s,"Sheet1");const l=c.M9(r,{bookType:"xlsx",type:"array"}),o=new Blob([l],{type:"application/octet-stream"});(0,d.saveAs)(o,`${b.replace(/ /g,"_")}_${a.length}.xlsx`)}catch(F){console.error("Error exporting to Excel:",F)}},children:[q&&(0,f.jsx)(f.Fragment,{children:(0,f.jsx)("span",{className:"material-symbols-outlined animate-spin text-sm me-2",children:"progress_activity"})}),!q&&(0,f.jsx)("span",{className:"material-symbols-outlined text-sm me-2",children:"file_export"}),"Export to Excel (",W.total,")"]})}),ae.hasManagerRole&&(0,f.jsx)("button",{className:" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:()=>B(!0),children:"Add New"})]})]}),(0,f.jsx)(o.$6,{columns:se,selectedFilterOptions:a,setSelectedFilterOptions:y,fetchDataOptionsForFilterBy:oe,filterOptions:e,filterOptionLoading:S,showFilterOption:v,resetPage:()=>{if(Object.keys(a).length){let e={};Object.keys(a).map((t=>{"string"===typeof a[t]?e[t]="":e[t]=[]})),y({...e}),Z({...e})}z(1)},setCurrentPage:z,buildQueryParams:Z}),H&&(0,f.jsx)("div",{className:"text-red-500",children:F}),q&&(0,f.jsx)(l.A,{}),(0,f.jsx)("div",{className:"border border-gray-200 p-0 pb-1 rounded-lg my-5 ",children:(0,f.jsx)(r.Ay,{columns:se,data:(null===W||void 0===W?void 0:W.data)||[],className:"p-0 scrollbar-horizontal-10",fixedHeader:!0,highlightOnHover:!0,responsive:!0,pagination:!0,paginationServer:!0,paginationPerPage:M,paginationTotalRows:(null===W||void 0===W?void 0:W.total)||0,onChangePage:e=>{e!==V&&z(e)},onChangeRowsPerPage:e=>{e!==M&&(U(e),z(1))},paginationComponentOptions:{selectAllRowsItem:!0,selectAllRowsItemText:"ALL"},sortServer:!0,onSort:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"desc";Object.keys(e).length&&(D(e.db_field||e.name||"created_at"),G(t||"desc"))}})}),R&&(0,f.jsx)(g.A,{isVisible:R,setVisible:B}),k&&(0,f.jsx)(x.A,{isVisible:k,setVisible:_,dataItemsId:A}),T&&(0,f.jsx)(o.Qg,{item:T,setViewData:E,columns:se,handleEdit:X,handleDelete:ee})]})})},v=()=>(0,f.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-xl",children:(0,f.jsx)(y,{})})}}]);
//# sourceMappingURL=845.343eba38.chunk.js.map