import React, { useState, useEffect } from "react";
import { API_URL } from './../../common/fetchData/apiConfig';
import useFetchApiData from './../../common/fetchData/useFetchApiData';
import { alertMessage } from "../../common/coreui";
import DatePicker from 'react-datepicker';
import moment from 'moment-timezone';
import LoadingIcon from "./../../common/LoadingIcon";
import { ViewSubmitFormData } from './../../common/coreui';
import { timeCardsApi,  useLazyFetchDataOptionsForTimeCardsQuery, useGetTimeCardByIdQuery, useUpdateTimeCardMutation } from "../../features/api";


const EditTimeCard = ({ isVisible, setVisible, dataItemsId }) => {
    const [formData, setFormData] = useState({});
    const [ticketNumber, setTicketNumber] = useState("");
    const [teams, setTeams] = useState([]);
    const [timeCardTeam, setTimeCardTeam] = useState("");
    const [departments, setDepartments] = useState([]);
    const [schedules, setSchedules] = useState([]);
    const [shiftId, setShiftId] = useState(null);
    const [taskDetails, setTaskDetails] = useState([]);
    const [productTypes, setProductTypes] = useState([]);
    const [taskTypes, setTaskTypes] = useState([]);
    const [revisionTypes, setRevisionTypes] = useState([]);
    const [regions, setRegions] = useState([]);
    const [priorities, setPriorities] = useState([]);
    const [reporters, setReporters] = useState([]);
    const [accountName, setAccountName] = useState("");
    const [campaignName, setCampaignName] = useState("");
    const [unit, setUnit] = useState("");
    const [notes, setNotes] = useState("");
    const [hour, setHour] = useState("");
    const [slaAchieve, setSlaAchieve] = useState("");
    const [highPriority, setHighPriority] = useState("");
    const [entryDate, setEntryDate] = useState("");
    const [clientError, setClientError] = useState("");
    const [internalError, setInternalError] = useState("");
    const [error, setError] = useState(null);
    const [successMessage, setSuccessMessage] = useState("");
    const [loggedUsers, setLoggedUsers] = useState([]);
    const [loggedInUserId, setLoggedInUserId] = useState("");
    const [loggedInUsersDepartment, setLoggedInUsersDepartment] = useState("");
    const [loggedInUsersDepartmentId, setLoggedInUsersDepartmentId] = useState("");
    const [loggedInUsersTeamIds, setLoggedInUsersTeamIds] = useState([]);
    const [loggedInUsersTeamId, setLoggedInUsersTeamId] = useState("");
    const [loggedInUsersteamName, setLoggedInUsersTeam] = useState("");
    const [ticketNumbers, setTicketNumbers] = useState([]);
    const [selectedTeamId, setSelectedTeamId] = useState(null);
    const [productTypeId, setProductTypeId] = useState(null);
    const [taskTypeId, setTaskTypeId] = useState(null);
    const [revisionTypeId, setRevisionTypeId] = useState(null);
    const [regionId, setRegionId] = useState(null);
    const [priorityId, setPriorityId] = useState(null);
    const [loading, setLoading] = useState(true);
    const [confirmation, setConfirmation] = useState(false);
    const [viewData, setViewData] = useState(false);

    const [selectedProductType, setSelectedProductType] = useState("");
    const [selectedTaskType, setSelectedTaskType] = useState("");
    const [selectedRevisionType, setSelectedRevisionType] = useState("");
    const [selectedRegion, setSelectedRegion] = useState("");
    const [selectedPriority, setSelectedPriority] = useState("");
    const [selectedReporter, setSelectedReporter] = useState("");
    const [recordTypes, setRecordTypes] = useState([]);
    const [selectedRecordType, setSelectedRecordType] = useState(null);
    const [releaseReview, setReleaseReview] = useState([]);
    const [selectedReviewRelease, setSelectedReviewRelease] = useState(null);
    //const [startDate, setStartDate] = useState(new Date());
    const [startDate, setStartDate] = useState(null);

    
    const [reporterId, setReporterId] = useState(null);


    

    const token = localStorage.getItem('token');

    // Fetching data using the custom hook
    const { data: teamsData } = useFetchApiData(`${API_URL}/teams`, token);
    const { data: reportersData } = useFetchApiData(`${API_URL}/users`, token);
    const { data: schedulesData } = useFetchApiData(`${API_URL}/schedules`, token);
    const { data: loggedUsersData } = useFetchApiData(`${API_URL}/logged-users`, token);
    const { data: taskDetailsData } = useFetchApiData(`${API_URL}/task-details`, token);
    const { data: departmentsData } = useFetchApiData(`${API_URL}/departments`, token);
    const { data: productTypeData } = useFetchApiData(`${API_URL}/product-types`, token);
    const { data: taskTypeData } = useFetchApiData(`${API_URL}/task-types`, token);
    const { data: revisionTypeData } = useFetchApiData(`${API_URL}/revision-types`, token);
    const { data: regionData } = useFetchApiData(`${API_URL}/regions`, token);
    const { data: priorityData } = useFetchApiData(`${API_URL}/priorities`, token);
    const { data: reporterData } = useFetchApiData(`${API_URL}/reporters`, token);
    const { data: recordTypeData } = useFetchApiData(`${API_URL}/record-types`, token);
    const { data: reviewReleaseData } = useFetchApiData(`${API_URL}/reviews`, token);

    useEffect(() => {
        if (teamsData && teamsData.teams) {
            setTeams(teamsData.teams || []);
        }

        if (reportersData) {
            setReporters(reportersData.filter(reporter => reporter.fname || reporter.lname) || []); // Only reporters with fname or lname
        }

        if (schedulesData) {
            setSchedules(schedulesData.schedules || []);
        }

        if (loggedUsersData) {
            const user = loggedUsersData;
            const departmentId = user.departments && user.departments.length > 0 ? user.departments[0].id : '';
            const departmentName = user.departments && user.departments.length > 0 ? user.departments[0].name : '';
            const loggedInUserTeamIds = user.teams && user.teams.length > 0 ? user.teams.map(team => team.id) : []; // Allow multiple teams
            const loggedInUsersteamNames = user.teams && user.teams.length > 0 ? user.teams.map(team => team.name) : ''; // Allow multiple teams

            const loggedInUserId = user.id;

            setLoggedInUserId(loggedInUserId);
            setLoggedInUsersDepartmentId(departmentId);
            setLoggedInUsersDepartment(departmentName);
            setLoggedInUsersTeamIds(loggedInUserTeamIds); // Store multiple team IDs
            setLoggedInUsersTeam(loggedInUsersteamNames);

            setLoggedUsers(loggedUsersData.users || []);
        }

        if (taskDetailsData) {
            setTaskDetails(taskDetailsData.taskDetails || []);
            if (taskDetailsData.taskDetails.length > 0) {
                setAccountName(taskDetailsData.taskDetails[0].account_name || "");
                setCampaignName(taskDetailsData.taskDetails[0].campaign_name || "");
                setProductTypeId(taskDetailsData.taskDetails[0].product_type_id || "");
                setTaskTypeId(taskDetailsData.taskDetails[0].task_type_id || "");
                setRevisionTypeId(taskDetailsData.taskDetails[0].revision_type_id || "");
                setPriorityId(taskDetailsData.taskDetails[0].priority_id || "");
                setRegionId(taskDetailsData.taskDetails[0].region_id || "");
                setReporterId(taskDetailsData.taskDetails[0].reporter_id || "");
                
            }
        }

        if (selectedTeamId && Array.isArray(teamsData?.teams) && Array.isArray(taskDetailsData?.taskDetails)) {
            const selectedTeam = teamsData.teams.find(team => team.id === Number(selectedTeamId));

            if (selectedTeam) {
                const teamName = selectedTeam.name;

                // Filter task details based on the team name and extract the 'ticket_number'
                const filteredTaskDetails = taskDetailsData.taskDetails.filter(task => task.team === teamName);
                const ticketNumbers = filteredTaskDetails.map(task => task.ticket_number); // Using 'ticket_number' from the task data

                setTicketNumbers(ticketNumbers);
            }
        }

        if (departmentsData) {
            setDepartments(departmentsData.departments || []);
            setLoading(false); // Set loading to false after fetching all data
        }

        if (productTypeData) {
            setProductTypes(productTypeData.productTypes || []);
        }

        if (taskTypeData) {
            setTaskTypes(taskTypeData.taskTypes || []);
        }

        if (revisionTypeData) {
            setRevisionTypes(revisionTypeData.revisionTypes || []);
        }

        if (priorityData) {
            setPriorities(priorityData.priorities || []);
        }

        if (regionData) {
            setRegions(regionData.regions || []);
        }

        if (reporterData) {
            setReporters(reporterData.reporters || []);
        }

        if (recordTypeData) {
            setRecordTypes(recordTypeData.recordTypes || []);
        }

        if (reviewReleaseData) {
            setReleaseReview(reviewReleaseData.reviews || []);
        }

    }, [teamsData, reportersData, schedulesData, loggedUsersData, taskDetailsData, departmentsData, selectedTeamId, ticketNumber, productTypeData, taskTypeData, revisionTypeData, priorityData, regionData, reporterData, recordTypeData, reviewReleaseData]);

   // Filter teams based on logged-in user's teams
   const filteredTeams = teams.filter(team => loggedInUsersTeamIds.includes(team.id)); // Show all teams the user is part of


    // Filter schedules based on the selected team ID
    const filteredSchedules = schedules.filter(schedule => {
        const scheduleTeamIds = schedule.teams ? schedule.teams.map(team => team.id) : [];
        const isScheduleForSelectedTeam = scheduleTeamIds.includes(Number(selectedTeamId));

        return isScheduleForSelectedTeam;
    });

    // Date Formations
    const currentDate = new Date();
    const formattedCurrentDate = currentDate.toISOString().split('T')[0];

    // Date 3 days ago for min value in input
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(currentDate.getDate() - 3);
    const formattedThreeDaysAgo = threeDaysAgo.toISOString().split('T')[0];

    // Set initial entry date when taskDetailsData is available
    useEffect(() => {
        if (taskDetailsData && taskDetailsData.taskDetails && taskDetailsData.taskDetails.length > 0) {
            const taskDate = taskDetailsData.taskDetails[0].date || formattedCurrentDate;
            const formattedTaskDate = new Date(taskDate).toISOString().split('T')[0];
            setEntryDate(formattedTaskDate);
        } else {
            setEntryDate(formattedCurrentDate);
        }

        const formattedHour = currentDate.toISOString().split('T')[1].substring(0, 5);
        setHour(formattedHour);

    }, [taskDetailsData]);

    // Check if the selected task type is "Revision"
    const isRevisionTaskType = () => {
        const selectedTaskTypeObj = taskTypes.find(task => task.id === parseInt(selectedTaskType));
        return selectedTaskTypeObj && selectedTaskTypeObj.name === 'Revision';
    };

    // Disable revisionType select if the task type is not "Revision"
    const revisionTypeDisabled = !isRevisionTaskType();

    // Handle task type change
    const handleTaskTypeChange = (e) => {
        const selectedTaskTypeId = e.target.value;
        setSelectedTaskType(selectedTaskTypeId);
        if (!isRevisionTaskType()) {
            setSelectedRevisionType(''); // Reset revision type if it's not "Revision"
        }
    };

    // Handle time change (Hour and Minutes)
    const handleChange = (date) => {
        
        if (date) {
            // Get the hours and minutes
            const hours = date.getHours();
            const minutes = date.getMinutes().toString().padStart(2, '0');  // Ensure two digits for minutes

            // Format the time as HH:mm (24-hour format)
            const time = `${hours.toString().padStart(2, '0')}:${minutes}`;

            setHour(time);  // Set the formatted hour in HH:mm format
            setStartDate(date);  // Store the full date object (if necessary for other purposes)
        } else {
            setHour('');  // Clear if no date is selected
            setStartDate(null);  // Reset date if cleared
        }
    };

    //For editing custom time
    // useEffect(() => {
    //     if (hour) {
    //         const [h, m] = hour.split(':');
    //         const date = new Date();
    //         date.setHours(parseInt(h));
    //         date.setMinutes(parseInt(m));
    //         date.setSeconds(0);
    //         date.setMilliseconds(0);
    //         setStartDate(date);
    //     }
    // }, [hour]);
    


    // Fetch task data and set state
    useEffect(() => {
        if (dataItemsId) {
            const fetchTaskData = async () => {
                try {
                    const response = await fetch(`${API_URL}/time-card/${dataItemsId}`, {
                        method: "GET",
                        headers: {
                            Authorization: `Bearer ${token}`,
                            "Content-Type": "application/json",
                        },
                    });
    
                    if (!response.ok) {
                        throw new Error("Failed to fetch time card data.");
                    }
    
                    const data = await response.json();
                    console.log('Time Card', data);
    
                    const timeCardData = data.timeCard;
    
                    // Set initial data from fetched task
                    setTicketNumber(timeCardData.ticket);
                    setTimeCardTeam(timeCardData.team_id || []);
                    setUnit(timeCardData.unit || "");
                    setAccountName(timeCardData.account || "");
                    setCampaignName(timeCardData.campaign || "");
                    setClientError(timeCardData.client_error ?? "0");
                    setInternalError(timeCardData.internal_error ?? "0");
                    setNotes(timeCardData.notes || "");
                    
                    setSlaAchieve(timeCardData.sla || "");
                    setHighPriority(timeCardData.high_priority || "");
                    setEntryDate(timeCardData.date || "");
    
                    setSelectedTeamId(timeCardData.team_id || "");
    
                    setSelectedProductType(timeCardData.product_type_id);
                    setSelectedTaskType(timeCardData.task_type_id);
                    setSelectedRevisionType(timeCardData.revision_type_id);
                    setSelectedRegion(timeCardData.region_id);
                    setSelectedPriority(timeCardData.priority_id);
                    setSelectedReporter(timeCardData.reporter_id);
                    setSelectedRecordType(timeCardData.record_type_id);
                    setSelectedReviewRelease(timeCardData.review_id);
    
                    // If timeCardData.hour exists, format it into HH:mm (strip seconds)
                    if (timeCardData.hour) {
                        const [hour, minute] = timeCardData.hour.split(":");
                        const formattedTime = `${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`; // Ensure both hour and minute are two digits
                        setHour(formattedTime);  // Set the hour correctly in HH:mm format
                    }
                } catch (error) {
                    console.error(error.message);
                    setError("Failed to fetch time card data.");
                }
            };
            fetchTaskData();
        }
    }, [dataItemsId]);
    

    // Handle select change to store the selected record type as an integer
    const handleSelectChange = (event) => {
        setSelectedRecordType(parseInt(event.target.value, 10)); // Ensure it's an integer
    };

    const handleSelectReviewReleaseChange = (event) => {
        setSelectedReviewRelease(parseInt(event.target.value, 10)); // Ensure it's an integer
    };

     const [updateTimeCard] = useUpdateTimeCardMutation();
    const handleSubmit = async (event) => {

        if(event){
            event.preventDefault();
        }

        const intRecordTypes = selectedRecordType ? parseInt(selectedRecordType, 10) : null;
        const intReviewRelease = selectedReviewRelease ? parseInt(selectedReviewRelease, 10) : null;

        const intProductTypes = parseInt(selectedProductType, 10);
        const intTaskTypes = parseInt(selectedTaskType, 10);
        const intRevisionTypes = parseInt(selectedRevisionType, 10);
        const intPriorities = parseInt(selectedPriority, 10);
        const intRegions = parseInt(selectedRegion, 10);
        const intReporters = parseInt(selectedReporter, 10);

        setFormData({
            team_id: event?.target.team.value || formData?.team_id || "",
            shift_id: event?.target.shift.value || formData?.shift_id || "",
            date: entryDate,
            department_id: loggedInUsersDepartmentId,
            ticket: ticketNumber,
            user_id: loggedInUserId,
            product_type_id: intProductTypes,
            task_type_id: intTaskTypes,
            record_type_id: intRecordTypes,
            revision_type_id: intRevisionTypes,
            priority_id: intPriorities,
            region_id: intRegions,
            reporter_id: intReporters,
            review_id: intReviewRelease,
            sla: slaAchieve,
            high_priority: highPriority,
            unit: unit,
            hour: hour,
            account: accountName,
            campaign: campaignName, 
            client_error: clientError,
            internal_error: internalError,
            notes: notes,
            created_by: loggedInUserId,
        });



        if(!confirmation){
            // setViewData(formData);
            setViewData({
                "Date": entryDate, 
                "Ticket Number": ticketNumber, 
                "Unit": unit, 
                "Duration": hour, 
                "Client Error": clientError, 
                "Internal Error": internalError
            })
            
            return false;
        }

        try {
            setViewData(null);

            
            // const response = await fetch(`${API_URL}/time-card/${dataItemsId}`, {
            //     method: "PUT",
            //     headers: {
            //         Authorization: `Bearer ${token}`,
            //         "Content-Type": "application/json",
            //     },
            //     body: JSON.stringify(formData),
            // });

            const response = await updateTimeCard({
                id: dataItemsId,
                ...formData,
                });

             if (response && response?.error) {
                 setError(response.error.data || "Failed to update time card.");
                return;
            }

            //setSuccessMessage("Time Card updated successfully!");
            // ✅ Success alert
            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: response?.message || 'Time card updated successfully.',
            });

            setTimeout(() => {
                setVisible(false);  // Close the modal after success
                setSuccessMessage('');
            }, 2000); 

        } catch (error) {
            //setError(error.message);
            alertMessage('error');
        }
    };

    const handleClose = () => {
        setVisible(false);
    };

    // Modal rendering
    return isVisible ? (
        <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
            <div className="bg-white rounded-lg shadow-md w-full max-w-4xl relative">
                <div className="flex justify-between items-center mb-4 bg-gray-100 px-4 py-2">
                    <h4 className="text-base text-left font-medium text-gray-800">Edit Time Card</h4>
                    <button
                        className="text-3xl text-gray-500 hover:text-gray-800"
                        onClick={handleClose}
                    >
                        &times;
                    </button>
                </div>

                        {loading && <LoadingIcon />}
                <form onSubmit={handleSubmit}>
                    <div className="flex flex-wrap gap-6 text-left p-6 overflow-y-auto max-h-[70vh] scrollbar-vertical">
                        {/* Date */}
                        <div className="mb-4 w-full sm:w-[47%]">
                            <label htmlFor="entryDate" className="block text-sm font-medium text-gray-700 pb-4">
                                Date <span className="text-red-600">*</span>
                            </label>
                            <input
                                id="entryDate"
                                type="date"
                                name="entryDate"
                                value={entryDate || ""}  // Use entryDate or default to empty string if undefined
                                onChange={(e) => setEntryDate(e.target.value)}  // Update the state when a new date is selected
                                min={formattedThreeDaysAgo}  // Set the min date to 3 days ago
                                max={formattedCurrentDate}  // Set the max date to today
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>

                        {/* Assigned Department */}
                        <div className="mb-4 w-full sm:w-[47%]">
                            <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">
                                Department <span className="text-red-600">*</span>
                            </label>
                            <select
                                id="department"
                                name="department"
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                                value={loggedInUsersDepartmentId || ""} // Ensure department is pre-filled if in edit mode
                                onChange={(e) => setDepartments(e.target.value)} // Handle department change
                            >
                                <option value="" disabled>Select a department</option>
                                {loggedInUsersDepartment && (
                                    <option value={loggedInUsersDepartment}>
                                        {loggedInUsersDepartment}
                                    </option>
                                )}
                            </select>
                        </div>

                        {/* Teams */}
                        <div className="mb-4 w-full sm:w-[47%]">
                            <label htmlFor="team" className="block text-sm font-medium text-gray-700 pb-4">
                                Team <span className="text-red-600">*</span>
                            </label>
                            <select
                                id="team"
                                name="team"
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                onChange={(e) => setSelectedTeamId(e.target.value)}  // Handle team selection
                                required
                                value={selectedTeamId || ""}  // Ensure the selected team is pre-filled when editing
                            >
                                <option value="">Select a team</option>
                                {filteredTeams.map((team) => (
                                    <option key={team.id} value={team.id}>
                                        {team.name}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Task Ticket Numbers */}
                        <div className="mb-4 w-full sm:w-[47%]">
                            <label htmlFor="ticket" className="block text-sm font-medium text-gray-700 pb-4">
                                Ticket Number <span className="text-red-600">*</span>
                            </label>
                            <select
                                id="ticket"
                                name="ticket"
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                                value={ticketNumber || ""}  // Ensure ticket number is pre-selected when editing
                                onChange={(e) => setTicketNumber(e.target.value)} // Handle ticket number change
                            >
                                <option value="">Select a Ticket</option>
                                {ticketNumbers.map((ticket, index) => (
                                    <option key={index} value={ticket}>
                                        {ticket}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Shift */}
                        <div className="mb-4 w-full sm:w-[47%]">
                            <label htmlFor="shift" className="block text-sm font-medium text-gray-700 pb-4">
                                Shift <span className="text-red-600">*</span>
                            </label>
                            <select
                                id="shift"
                                name="shift"
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                                value={shiftId || ""}  // Ensure shift is pre-selected when editing
                                onChange={(e) => setShiftId(e.target.value)} // Handle shift selection
                            >
                                {filteredSchedules.map((schedule) => (
                                    <option key={schedule.id} value={schedule.id}>
                                        {schedule.shift_name}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Unit */}
                        {ticketNumber !== "N/A" && (
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="unit" className="block text-sm font-medium text-gray-700 pb-4">
                                    Unit <span className="text-red-600">*</span>
                                </label>
                                <input
                                    id="unit"
                                    type="number"
                                    name="unit"
                                    value={unit || ""}  // Ensure unit is pre-filled when editing
                                    onChange={(e) => setUnit(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>
                        )}

                        {/* Hour */}
                        <div className="mb-4 w-full sm:w-[47%]">
                            <label htmlFor="hour" className="block text-sm font-medium text-gray-700 pb-4">
                                Duration <span className="text-red-600">*</span>
                            </label>
                            <DatePicker
                                selected={startDate}
                                onChange={handleChange}
                                showTimeSelect
                                showTimeSelectOnly
                                timeFormat="HH:mm"
                                timeIntervals={5}
                                dateFormat="HH:mm"
                                value={hour} //stop passing the value on the above ussEffect with custom edit
                                timeCaption="Time"
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                placeholderText="Select time"
                            />
                        </div>

                        {/* Task Type */}
                        {ticketNumber && (
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="taskType" className="block text-sm font-medium text-gray-700 pb-4">
                                    Task Type <span className="text-red-600">*</span>
                                </label>
                                <select
                                    id="taskType"
                                    name="taskType"
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                    value={selectedTaskType || ''}
                                    onChange={handleTaskTypeChange}
                                >
                                    <option value="">Select task type</option>
                                    {taskTypes &&
                                    taskDetails
                                        .filter(task => task.product_type_id === Number(selectedProductType))
                                        .flatMap(task =>
                                        taskTypes
                                            .filter(taskType => taskType.id === task.task_type_id)
                                            .map(taskType => (
                                            <option key={taskType.id} value={taskType.id}>
                                                {taskType.name}
                                            </option>
                                            ))
                                        )}
                                </select>
                            </div>
                        )}


                        {/* Account Name */}
                        {ticketNumber !== "N/A" && (
                            <div className="w-full flex flex-wrap gap-6">                              
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="productType" className="block text-sm font-medium text-gray-700 pb-4">
                                        Product Type <span className="text-red-600">*</span>
                                    </label>
                                    <select
                                        id="productType"
                                        name="productType"
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                        required
                                        value={selectedProductType}  // Ensure product type is pre-filled when editing
                                        onChange={(e) => setProductTypeId(e.target.value)}
                                    >
                                        <option value="">Select product type</option>
                                        {ticketNumber && taskDetails
                                            .filter(task => task.ticket_number === ticketNumber)
                                            .map(task => 
                                                productTypes.filter(productType => productType.id === task.product_type_id)
                                                    .map(productType => (
                                                        <option key={productType.id} value={productType.id}>
                                                            {productType.name}
                                                        </option>
                                                    ))
                                            )}
                                    </select>
                                </div>

                                {/* Revision Type */}
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="revisionType" className="block text-sm font-medium text-gray-700 pb-4">
                                        Revision Type
                                    </label>
                                    <select
                                        id="revisionType"
                                        name="revisionType"
                                        className={`block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 ${revisionTypeDisabled ? 'bg-gray-100' : ''}`}
                                        value={selectedRevisionType || ''}
                                        onChange={(e) => setSelectedRevisionType(e.target.value)}
                                        disabled={revisionTypeDisabled} // Disable if task type is not "Revision"
                                    >
                                        <option value="">Select revision type</option>
                                        {taskDetails
                                            .filter(task => task.ticket_number === ticketNumber)
                                            .map(task =>
                                                revisionTypes.filter(revisionType => revisionType.id === task.revision_type_id)
                                                    .map(revisionType => (
                                                        <option key={revisionType.id} value={revisionType.id}>
                                                            {revisionType.name}
                                                        </option>
                                                    ))
                                            )}
                                    </select>
                                </div>


                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="region" className="block text-sm font-medium text-gray-700 pb-4">
                                        Region <span className="text-red-600">*</span>
                                    </label>
                                    <select
                                        id="region"
                                        name="region"
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                        required
                                        value={selectedRegion} 
                                        onChange={(e) => setRegionId(e.target.value)}
                                    >
                                        <option value="">Select region</option>
                                        {ticketNumber && taskDetails
                                            .filter(task => task.ticket_number === ticketNumber)
                                            .map(task => 
                                                regions.filter(region => region.id === task.region_id)
                                                    .map(region => (
                                                        <option key={region.id} value={region.id}>
                                                            {region.name}
                                                        </option>
                                                    ))
                                            )}
                                    </select>
                                </div>

                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="priority" className="block text-sm font-medium text-gray-700 pb-4">
                                        Priority <span className="text-red-600">*</span>
                                    </label>
                                    <select
                                        id="priority"
                                        name="priority"
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                        required
                                        value={selectedPriority} 
                                        onChange={(e) => setPriorityId(e.target.value)}
                                    >
                                        <option value="">Select priority</option>
                                        {ticketNumber && taskDetails
                                            .filter(task => task.ticket_number === ticketNumber)
                                            .map(task => 
                                                priorities.filter(priority => priority.id === task.priority_id)
                                                    .map(priority => (
                                                        <option key={priority.id} value={priority.id}>
                                                            {priority.name}
                                                        </option>
                                                    ))
                                            )}
                                    </select>
                                </div>

                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="reporter" className="block text-sm font-medium text-gray-700 pb-4">
                                        Reporter <span className="text-red-600">*</span>
                                    </label>
                                    <select
                                        id="reporter"
                                        name="reporter"
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                        required
                                        value={selectedReporter} 
                                        onChange={(e) => setReporterId(e.target.value)}
                                    >
                                        <option value="">Select reporter</option>
                                        {ticketNumber && taskDetails
                                            .filter(task => task.ticket_number === ticketNumber)
                                            .map(task => 
                                                reporters.filter(reporter => reporter.id === task.reporter_id)
                                                    .map(reporter => (
                                                        <option key={reporter.id} value={reporter.id}>
                                                            {reporter.name}
                                                        </option>
                                                    ))
                                            )}
                                    </select>
                                </div>

                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="account_name" className="block text-sm font-medium text-gray-700 pb-4">
                                        Account Name <span className="text-red-600">*</span>
                                    </label>
                                    <input
                                        id="account_name"
                                        type="text"
                                        value={accountName || ""}  // Ensure account name is pre-filled when editing
                                        onChange={(e) => setAccountName(e.target.value)}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                        readOnly
                                    />
                                </div>

                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="campaign_name" className="block text-sm font-medium text-gray-700 pb-4">
                                        Campaign Name <span className="text-red-600">*</span>
                                    </label>
                                    <input
                                        id="campaign_name"
                                        type="text"
                                        value={campaignName || ""}  // Ensure campaign name is pre-filled when editing
                                        onChange={(e) => setCampaignName(e.target.value)}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                        readOnly
                                    />
                                </div>

                                {/* Client Error */}
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="client_error" className="block text-sm font-medium text-gray-700 pb-4">
                                        Client Error
                                    </label>
                                    <input
                                        id="client_error"
                                        type="number"
                                        name="client_error"
                                        value={clientError}  // Ensure client error is pre-filled when editing
                                        onChange={(e) => setClientError(e.target.value)}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    />
                                </div>

                                {/* Internal Error */}
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="internal_error" className="block text-sm font-medium text-gray-700 pb-4">
                                        Internal Error
                                    </label>
                                    <input
                                        id="internal_error"
                                        type="number"
                                        name="internal_error"
                                        value={internalError}  // Ensure internal error is pre-filled when editing
                                        onChange={(e) => setInternalError(e.target.value)}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    />
                                </div>

                                {/* SLA Achieves */}
                                <div className="mb-4 w-full sm:w-[47%]">
                                    <label htmlFor="slaAchieves" className="block text-sm font-medium text-gray-700 pb-4">
                                        SLA Achieves <span className="text-red-600">*</span>
                                    </label>
                                    <select
                                        id="slaAchieves"
                                        name="slaAchieves"
                                        value={slaAchieve || ""} // This should be your state variable to hold the selected value
                                        onChange={(e) => setSlaAchieve(e.target.value)} // This updates the state when the selection changes
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    >
                                        <option value="">Select</option> {/* Optional, if you want to allow no selection */}
                                        <option value="Yes">Yes</option>
                                        <option value="No">No</option>
                                    </select>
                                </div>


                            </div>
                        )}

                        {/* Record Type */}
                        <div className="mb-4 w-full sm:w-[47%]">
                            <label htmlFor="recordType" className="block text-sm font-medium text-gray-700 pb-4">
                                Record Type <span className="text-red-600">*</span>
                            </label>
                            <select
                                id="recordType"
                                value={selectedRecordType !== null ? selectedRecordType : ''} // Ensure the value is either the selected type or an empty string
                                onChange={handleSelectChange}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            >
                                <option value="">Select a Record Type</option>
                                {recordTypes.length === 0 ? (
                                    <option disabled>No record types available</option>
                                ) : (
                                    recordTypes.map((recordType) => (
                                        <option key={recordType.id} value={recordType.id}>
                                            {recordType.name}
                                        </option>
                                    ))
                                )}
                            </select>
                        </div>

                        {/* Release & Review */}
                        {ticketNumber !== "N/A" && (
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="releaseReview" className="block text-sm font-medium text-gray-700 pb-4">
                                    Release & Review 
                                </label>
                                <select
                                    id="releaseReview"
                                    value={selectedReviewRelease !== null ? selectedReviewRelease : ''}
                                    onChange={handleSelectReviewReleaseChange}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"

                                >
                                    <option value="">Select a Review & release</option>
                                    {releaseReview.length === 0 ? (
                                        <option disabled>No release & review available</option>
                                    ) : (
                                        releaseReview.map((review) => (
                                            <option key={review.id} value={review.id}>
                                                {review.name}
                                            </option>
                                        ))
                                    )}
                                </select>
                            </div>
                        )}

                        {/* Notes */}
                        <div className="mb-4 w-full sm:w-[100%]">
                            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 pb-4">
                                Notes
                            </label>
                            <textarea
                                id="notes"
                                name="notes"
                                value={notes || ""}  // Ensure notes are pre-filled when editing
                                onChange={(e) => setNotes(e.target.value)}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>
                    </div>

                    <div>
                        {error && <p className="text-red-500 text-sm">{error}</p>}
                        {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                    </div>

                    <div className='text-left p-6'>
                        <button
                            type="submit"
                            className="w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4"
                        >
                            <span class="material-symbols-rounded text-white text-xl font-regular">add_circle</span>
                            {loading ? 'Updating...' : 'Update Work Hour'}
                        </button>
                    </div>
                </form>

            </div>

            {viewData && (
                <ViewSubmitFormData
                    item={viewData}
                    setViewData={setViewData}
                    setConfirmation={setConfirmation}
                    handleSubmit={handleSubmit}
                />
            )}

        </div>
    ) : null;
};

export default EditTimeCard;
