<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('billing_status_user', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');        // Foreign key column for users
            $table->unsignedBigInteger('billing_status_id');  // Foreign key column for departments
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade'); 

            $table->foreign('billing_status_id')
                  ->references('id')
                  ->on('billing_statuses')
                  ->onDelete('cascade');

            // Optional: Unique constraint to prevent duplicate entries
            $table->unique(['user_id', 'billing_status_id']);
            
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('billing_status_user');
    }
};
