# Password Manager API Testing Guide

## Prerequisites
1. **Authentication**: You need a valid Sanctum token
2. **Base URL**: `http://localhost/creativeapp/creativeApp-2.0_server/public/api`
3. **Headers**: 
   - `Content-Type: application/json`
   - `Authorization: Bearer YOUR_TOKEN_HERE`

## Step 1: Get Authentication Token

**POST** `/login`
```json
{
    "email": "<EMAIL>",
    "password": "your-password"
}
```

**Expected Response:**
```json
{
    "status": "success",
    "message": "Login successful",
    "token": "YOUR_SANCTUM_TOKEN",
    "user": { ... }
}
```

Copy the token and use it in all subsequent requests.

## Step 2: Test Password Manager Endpoints

### 2.1 GET /api/password-managers
**Purpose**: List all password managers for authenticated user

**Headers:**
- `Authorization: Bearer YOUR_TOKEN`

**Expected Response (Success):**
```json
{
    "status": "success",
    "data": [
        {
            "id": 1,
            "password_title": "Example Password",
            "password_strength": "Strong Password",
            "created_at": "2025-07-21 10:00:00",
            "updated_at": "2025-07-21 10:00:00",
            "department": null,
            "team": null,
            "user": {
                "id": 1,
                "fname": "John",
                "lname": "Doe",
                "email": "<EMAIL>"
            }
        }
    ],
    "meta": {
        "current_page": 1,
        "per_page": 15,
        "total": 1,
        "last_page": 1
    }
}
```

**Expected Response (Unauthorized):**
```json
{
    "message": "Unauthenticated."
}
```

### 2.2 GET /api/password-managers?user_id=3&department_id=1
**Purpose**: Filter password managers by user and department

**Headers:**
- `Authorization: Bearer YOUR_TOKEN`

**Expected Response**: Same format as above, but filtered results

### 2.3 POST /api/password-managers
**Purpose**: Create new password manager

**Headers:**
- `Authorization: Bearer YOUR_TOKEN`
- `Content-Type: application/json`

**Request Body:**
```json
{
    "password_title": "My New Password",
    "password": "SecurePassword123!",
    "department_id": 1,
    "team_id": 2
}
```

**Expected Response (Success):**
```json
{
    "status": "success",
    "message": "Password record created successfully",
    "data": {
        "id": 2,
        "password_title": "My New Password",
        "password_strength": "Strong Password",
        "created_at": "2025-07-21 10:30:00",
        "updated_at": "2025-07-21 10:30:00",
        "department": {
            "id": 1,
            "name": "IT Department"
        },
        "team": {
            "id": 2,
            "name": "Development Team"
        },
        "user": {
            "id": 1,
            "fname": "John",
            "lname": "Doe",
            "email": "<EMAIL>"
        }
    }
}
```

**Expected Response (Validation Error):**
```json
{
    "status": "error",
    "message": "Validation failed",
    "errors": {
        "password_title": ["Password title is required."],
        "password": ["Password is required."]
    }
}
```

### 2.4 GET /api/password-managers/{id}
**Purpose**: Get specific password manager

**Headers:**
- `Authorization: Bearer YOUR_TOKEN`

**Expected Response (Success):**
```json
{
    "status": "success",
    "data": {
        "id": 1,
        "password_title": "Example Password",
        "password_strength": "Strong Password",
        "created_at": "2025-07-21 10:00:00",
        "updated_at": "2025-07-21 10:00:00",
        "department": null,
        "team": null,
        "user": {
            "id": 1,
            "fname": "John",
            "lname": "Doe",
            "email": "<EMAIL>"
        }
    }
}
```

**Expected Response (Unauthorized - not your record):**
```json
{
    "status": "error",
    "message": "Unauthorized to view this password record"
}
```

### 2.5 PUT /api/password-managers/{id}
**Purpose**: Update password manager (only creator can update)

**Headers:**
- `Authorization: Bearer YOUR_TOKEN`
- `Content-Type: application/json`

**Request Body:**
```json
{
    "password_title": "Updated Password Title",
    "password": "NewSecurePassword456!",
    "department_id": 2
}
```

**Expected Response (Success):**
```json
{
    "status": "success",
    "message": "Password record updated successfully",
    "data": {
        "id": 1,
        "password_title": "Updated Password Title",
        "password_strength": "Strong Password",
        "created_at": "2025-07-21 10:00:00",
        "updated_at": "2025-07-21 11:00:00",
        "department": {
            "id": 2,
            "name": "HR Department"
        },
        "team": null,
        "user": {
            "id": 1,
            "fname": "John",
            "lname": "Doe",
            "email": "<EMAIL>"
        }
    }
}
```

**Expected Response (Unauthorized - not creator):**
```json
{
    "status": "error",
    "message": "Unauthorized. Only the creator can update this password record"
}
```

### 2.6 DELETE /api/password-managers/{id}
**Purpose**: Delete password manager (only creator can delete)

**Headers:**
- `Authorization: Bearer YOUR_TOKEN`

**Expected Response (Success):**
```json
{
    "status": "success",
    "message": "Password record deleted successfully"
}
```

**Expected Response (Unauthorized - not creator):**
```json
{
    "status": "error",
    "message": "Unauthorized. Only the creator can delete this password record"
}
```

### 2.7 GET /api/password-managers/{id}/password
**Purpose**: Get decrypted password (only creator can access)

**Headers:**
- `Authorization: Bearer YOUR_TOKEN`

**Expected Response (Success):**
```json
{
    "status": "success",
    "password": "ActualDecryptedPassword123!",
    "strength": "Strong Password"
}
```

**Expected Response (Unauthorized - not creator):**
```json
{
    "status": "error",
    "message": "Unauthorized. Only the creator can view this password"
}
```

## Testing Scenarios

### Scenario 1: Complete CRUD Flow
1. Create a password manager (POST)
2. List all password managers (GET)
3. Get specific password manager (GET /{id})
4. Update the password manager (PUT /{id})
5. Get decrypted password (GET /{id}/password)
6. Delete the password manager (DELETE /{id})

### Scenario 2: Authorization Testing
1. Create password manager with User A
2. Try to access/update/delete with User B (should fail)
3. Verify only creator can perform operations

### Scenario 3: Validation Testing
1. Try to create without required fields
2. Try to create with invalid department_id/team_id
3. Try to create with duplicate password_title

## Expected HTTP Status Codes
- **200**: Success (GET, PUT, DELETE)
- **201**: Created (POST)
- **401**: Unauthenticated (missing/invalid token)
- **403**: Forbidden (not authorized for this specific resource)
- **404**: Not Found (resource doesn't exist)
- **422**: Validation Error (invalid input data)
- **500**: Server Error (unexpected error)
