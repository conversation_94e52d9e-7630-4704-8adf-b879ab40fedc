import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import { useNavigate } from 'react-router-dom';
import EditLocation from './EditLocation';  // Assuming you have an EditLocation component for editing locations

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const LocationList = () => {
    const [locations, setLocations] = useState([]); // Renamed 'teams' to 'locations'
    const [error, setError] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedLocationId, setSelectedLocationId] = useState(null); // Renamed 'selectedTeamId' to 'selectedLocationId'
    const navigate = useNavigate();

    // Update column names for locations
    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Location Name", key: "locations_name" }, // Updated to reflect location name
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchLocations = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}locations`, { // Updated endpoint to '/locations'
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();

                setLocations(data.locations.map(location => ({ // Updated from 'teams' to 'locations'
                    id: location.id,
                    locations_name: location.locations_name,
                    created_by: location.created_by,
                    updated_by: location.updated_by,
                }))); 
            } catch (error) {
                setError(error.message);
            }
        };

        fetchLocations();
    }, []);

    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}locations/${id}`, { // Updated to '/locations'
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete location: ' + response.statusText);
            }

            // Update the locations list after deletion
            setLocations(prevLocations => prevLocations.filter(location => location.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedLocationId(id); // Set the selected location ID
        setModalVisible(true); // Show the modal for editing
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={locations} // Updated from 'teams' to 'locations'
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible} // Pass modal state functions if needed
                setSelectedServiceId={setSelectedLocationId} // Pass the selected location ID
            />
            {modalVisible && (
                <EditLocation // Assuming EditLocation component exists for editing locations
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    locationId={selectedLocationId} // Pass the selected location ID to EditLocation
                />
            )}
        </div>
    );
};

export default LocationList;
