import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const GiveFeedbackList = () => {
    const navigate = useNavigate();
    const [feedbacks, setFeedbacks] = useState([]);
    const [statusList, setStatusList] = useState({});
    const [editingFeedback, setEditingFeedback] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const getAuthHeaders = () => ({
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json',
    });

    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            setError(null);
            try {
                const [statusRes, feedbackRes] = await Promise.all([
                    fetch(`${API_URL}status`, { headers: getAuthHeaders() }),
                    fetch(`${API_URL}give-feedbacks/`, { headers: getAuthHeaders() })
                ]);

                if (!statusRes.ok || !feedbackRes.ok) {
                    throw new Error('Failed to fetch data');
                }

                const statusData = await statusRes.json();
                const feedbackData = await feedbackRes.json();

                const statusArray = Array.isArray(statusData) 
                    ? statusData 
                    : statusData[Object.keys(statusData).find(key => Array.isArray(statusData[key]))] || [];

                const statusMap = statusArray.reduce((acc, status) => {
                    if (status.id && status.name) acc[status.id] = status.name;
                    return acc;
                }, {});

                const updatedFeedbacks = (feedbackData.feedbacks || []).map(fb => ({
                    ...fb,
                    status: statusMap[fb.status] || "New"
                }));

                setStatusList(statusMap);
                setFeedbacks(updatedFeedbacks);
            } catch (err) {
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    const handleEdit = (id) => {
        setEditingFeedback(feedbacks.find(fb => fb.id === id));
    };

    const handleDelete = async (id) => {
        try {
            const response = await fetch(`${API_URL}give-feedback/${id}/`, {
                method: 'DELETE',
                headers: getAuthHeaders(),
            });

            if (!response.ok) throw new Error('Failed to delete feedback');

            setFeedbacks(prevFeedbacks => prevFeedbacks.filter(fb => fb.id !== id));
        } catch (err) {
            setError(err.message);
        }
    };

    const closeModal = () => setEditingFeedback(null);

    const handleSave = async () => {
        if (!editingFeedback) return;

        try {
            const response = await fetch(`${API_URL}give-feedback/${editingFeedback.id}/`, {
                method: 'PUT',
                headers: getAuthHeaders(),
                body: JSON.stringify(editingFeedback),
            });

            if (!response.ok) throw new Error('Failed to update feedback');

            setFeedbacks(prevFeedbacks => 
                prevFeedbacks.map(fb => fb.id === editingFeedback.id ? { ...editingFeedback } : fb)
            );
            closeModal();
        } catch (err) {
            setError(err.message);
        }
    };

    return (
        <div className="p-4 bg-white rounded-lg shadow">
            <h2 className="text-2xl font-bold">Give Feedback</h2>
            {error && <p className="text-red-500">{error}</p>}
            <div className="flex justify-end mb-4">
                <button className="px-4 py-2 border border-gray-300 text-black rounded-md hover:bg-gray-100" onClick={() => navigate('/add-feedback')}>
                    Add Feedback
                </button>
            </div>
            {loading ? (
                <p>Loading feedback...</p>
            ) : feedbacks.length === 0 ? (
                <p>No feedback available.</p>
            ) : (
                <table className="w-full text-sm text-left text-gray-500 border">
                    <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th className="px-4 py-3">SL</th>
                            <th className="px-4 py-3">Subject</th>
                            <th className="px-4 py-3">Message</th>
                            <th className="px-4 py-3">Status</th>
                            <th className="px-4 py-3 text-right">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {feedbacks.map((feedback, index) => (
                            <tr key={feedback.id} className="border-b">
                                <td className="px-4 py-3">{index + 1}</td>
                                <td className="px-4 py-3">{feedback.subject}</td>
                                <td className="px-4 py-3">{feedback.message}</td>
                                <td className="px-4 py-3">{statusList[feedback.status] || feedback.status}</td>
                                <td className="px-4 py-3 text-right">
                                    <button onClick={() => handleEdit(feedback.id)} className="px-3 py-1 bg-blue-500 text-white rounded">Edit</button>
                                    <button onClick={() => handleDelete(feedback.id)} className="px-3 py-1 bg-red-500 text-white rounded ml-2">Delete</button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            )}
            {editingFeedback && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex justify-center items-center">
                    <div className="bg-white p-4 rounded shadow-lg w-1/3">
                        <h2 className="text-xl font-bold mb-4">Edit Feedback</h2>
                        <input type="text" value={editingFeedback.subject} onChange={(e) => setEditingFeedback({...editingFeedback, subject: e.target.value})} className="border p-2 w-full mb-2" />
                        <textarea value={editingFeedback.message} onChange={(e) => setEditingFeedback({...editingFeedback, message: e.target.value})} className="border p-2 w-full mb-2" />
                        <select value={editingFeedback.status} onChange={(e) => setEditingFeedback({...editingFeedback, status: e.target.value})} className="border p-2 w-full mb-2">
                            {Object.entries(statusList).map(([id, name]) => (
                                <option key={id} value={id}>{name}</option>
                            ))}
                        </select>
                        <div className="flex justify-end">
                            <button onClick={closeModal} className="mr-2 bg-gray-300 px-4 py-2 rounded">Cancel</button>
                            <button onClick={handleSave} className="bg-blue-500 text-white px-4 py-2 rounded">Save</button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default GiveFeedbackList;
