<?php

namespace Tests\Feature;

use Tests\TestCase;

class PasswordManagerManualTest extends TestCase
{
    public function test_password_manager_routes_are_registered()
    {
        // Test GET route
        $response = $this->getJson('/api/password-managers');
        // Should return 401 (unauthorized) not 404 (not found)
        $this->assertNotEquals(404, $response->getStatusCode(), 'GET route should exist');

        // Test POST route
        $response = $this->postJson('/api/password-managers', []);
        // Should return 401 (unauthorized) not 404 (not found)
        $this->assertNotEquals(404, $response->getStatusCode(), 'POST route should exist');
    }

    public function test_validation_error_format()
    {
        // Test that validation errors return proper format
        $response = $this->postJson('/api/password-managers', []);
        
        if ($response->getStatusCode() === 422) {
            $data = $response->json();
            $this->assertArrayHasKey('status', $data);
            $this->assertArrayHasKey('message', $data);
            $this->assertArrayHasKey('errors', $data);
            $this->assertEquals('error', $data['status']);
            $this->assertEquals('Validation failed', $data['message']);
        }
    }

    public function test_unauthorized_access_format()
    {
        // Test that unauthorized access returns proper format
        $response = $this->getJson('/api/password-managers');
        
        if ($response->getStatusCode() === 401) {
            $data = $response->json();
            $this->assertArrayHasKey('message', $data);
        }
    }
}
