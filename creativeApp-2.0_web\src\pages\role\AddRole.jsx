import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
const API_URL = process.env.REACT_APP_BASE_API_URL;
// Function to check if the authentication token is valid
const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const AddRole = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [roles, setRoles] = useState([]);
    const [roleName, setRoleName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    // Fetch roles when component mounts
    useEffect(() => {
        const fetchRoles = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}roles`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                setRoles(data.roles);
            } catch (error) {
                setError(error.message);
            }
        };

        fetchRoles();
    }, []); // Empty dependency array ensures this runs once when the component is mounted

    // Handle form submission to create a new role
    const handleSubmit = async (event) => {
        event.preventDefault(); // Prevent default form submission behavior
        const trimmedRoleName = roleName.trim();

        // Check if the role already exists
        const roleExists = roles.some((role) => {
            return role.name.toLowerCase() === trimmedRoleName.toLowerCase();
        });

        if (roleExists) {
            setError('Role already exists. Please add a different role.');
            setTimeout(() => setError(''), 3000);
            return; // Exit if the role already exists
        }

        setError(''); // Clear any previous error

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return; // Exit if token is not available
            }

            // Retrieve first and last name from localStorage and combine them into a fullname
            const firstName = localStorage.getItem('fname');
            const lastName = localStorage.getItem('lname');
            if (!firstName || !lastName) {
                setError('User details (first and last name) are missing.');
                return; // Exit if first or last name is missing
            }

            const fullName = `${firstName} ${lastName}`; // Combine the names

            // Send the role data along with created_by and updated_by as fullName
            const response = await fetch(`${API_URL}roles`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedRoleName,
                    created_by: fullName, // Include the full name in the request
                    updated_by: fullName, // Include the full name in the request
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to save role: ' + response.statusText);
            }

            const result = await response.json();
            setSuccessMessage(`Role "${result.name || trimmedRoleName}" added successfully!`);
            setRoleName(''); // Clear the role name input field

            // Refetch the roles list after adding the new role
            const newRolesResponse = await fetch(`${API_URL}roles`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!newRolesResponse.ok) {
                throw new Error('Failed to fetch roles: ' + newRolesResponse.statusText);
            }

            const newRolesData = await newRolesResponse.json();
            setRoles(newRolesData.roles); // Update the roles list

        } catch (error) {
            setError(error.message); // Set the error message if something fails
        }
    };

    // Check if the current location is for the modal
    const isModalOpen = location.pathname === '/add-role';

    const handleClose = () => {
        navigate('/settings'); // Navigate to the roles route when closing the modal
    };

    return (
        <>
            {isModalOpen && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative">
                        <button onClick={handleClose} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                            &times; {/* Close icon */}
                        </button>
                        <h4 className="text-xl font-semibold mb-4 py-4">Add New Role</h4>
                        <form onSubmit={handleSubmit}>
                            <div className="mb-4">
                                <label htmlFor="roleName" className="block text-sm font-medium text-gray-700 pb-4">
                                    Role Name
                                </label>
                                <input
                                    type="text"
                                    id="roleName"
                                    value={roleName}
                                    onChange={(e) => setRoleName(e.target.value)}
                                    required
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                                {error && <p className="text-red-500 text-sm">{error}</p>}
                            </div>
                            <div className='py-4'>
                                <button
                                    type="submit"
                                    className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                                >
                                    Add Role
                                </button>
                            </div>
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default AddRole;
