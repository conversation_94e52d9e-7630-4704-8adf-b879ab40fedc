<?php

namespace App\Http\Controllers;
use App\Models\Department;
use App\Models\Team;
use App\Models\User;
use App\Models\Designation;
use App\Models\Schedule;
use App\Models\Location;
use Illuminate\Http\JsonResponse;

use Illuminate\Http\Request;

class ListDataController extends Controller
{
    /**
     * Get list of departments.
     */
    public function getDepartments(): JsonResponse
    {
        $departments = Department::with(['teams:id,name'])->orderBy('name', 'asc')->get();

        return response()->json($departments);
    }
    /**
     * Get list of locations.
     */
    public function getOfficelocation(): JsonResponse
    {
        $locations = Location::orderBy('locations_name', 'asc')->get();

        return response()->json($locations);
    }

    /**
     * Get list of teams.
     */
    public function getTeams(): JsonResponse
    {
        $teams = Team::with('departments:id')
        ->select('teams.id', 'name',)
        ->orderBy('name', 'asc')
        ->get()
        ->map(function ($team) {
            $team->department_ids = $team->departments->pluck('id')->toArray();
            return $team;
        });

        return response()->json($teams);
    }
    
    /**
     * Get list of shifts.
     */
    public function getShifts(): JsonResponse
    {
        $teams = Schedule::with('teams')
        ->select('schedules.id', 'shift_name','shift_start','shift_end')
        ->orderBy('shift_name', 'asc')
        ->get()
        ->map(function ($team) {
            $team->team_ids = $team->teams->pluck('id')->toArray();
            return $team;
        });

        return response()->json($teams);
    }

    
    /**
     * Get list of users with their default teams and shift information.
     */
    public function getUsersByDefaultTeam(): JsonResponse
    {
        try {
            $users = User::with([
                'teams' => function ($query) {
                    $query->select('teams.id', 'teams.name', 'team_user.is_default')
                          ->withPivot('is_default');
                },
                'designations:id,name'
            ])
            ->select('users.id', 'users.fname', 'users.lname', 'users.eid')
            ->orderBy('users.fname', 'asc')
            ->get()
            ->map(function ($user) {
                $teams = $user->teams;

                if ($teams->count() > 1) {
                    // If multiple teams, pick the one where is_default = 1
                    $defaultTeam = $teams->where('pivot.is_default', 1)->first();
                    $user->team_id = $defaultTeam ? $defaultTeam->id : $teams->first()->id;
                    $user->team_name = $defaultTeam ? $defaultTeam->name : $teams->first()->name;
                } else {
                    // If only one team, return that team's ID
                    $firstTeam = $teams->first();
                    $user->team_id = $firstTeam ? $firstTeam->id : null;
                    $user->team_name = $firstTeam ? $firstTeam->name : null;
                }

                // Get user's primary designation for role categorization
                $primaryDesignation = $user->designations->first();
                $user->designation_name = $primaryDesignation ? $primaryDesignation->name : 'Unassigned';

                // Categorize user role for shift summary
                $designationLower = strtolower($user->designation_name);
                if (str_contains($designationLower, 'designer') || str_contains($designationLower, 'design')) {
                    $user->role_category = 'designer';
                } elseif (str_contains($designationLower, 'developer') || str_contains($designationLower, 'dev')) {
                    $user->role_category = 'developer';
                } elseif (str_contains($designationLower, 'qa') || str_contains($designationLower, 'quality') || str_contains($designationLower, 'test')) {
                    $user->role_category = 'qa';
                } else {
                    $user->role_category = 'other';
                }

                unset($user->teams, $user->designations); // Remove full relationships
                return $user;
            });

            return response()->json($users);

        } catch (\Exception $e) {
            \Log::error('Error fetching users by default team:', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to fetch users data'], 500);
        }
    }


    /**
     * Get list of designations.
     */
    public function getDesignations(): JsonResponse
    {
        $designations = Designation::select('id', 'name')->orderBy('name', 'asc')->get();

        return response()->json($designations);
    }

    /**
     * Get shift statistics with user counts by role.
     */
    public function getShiftStats(): JsonResponse
    {
        try {
            // Get all schedules (shifts) with their associated teams and users
            $shifts = Schedule::with([
                'teams.users.designations'
            ])->get();

            // Get all users with their schedule assignments from SchedulePlanner
            $schedulePlanners = \App\Models\SchedulePlanner::with([
                'user.designations',
                'schedule',
                'team'
            ])->get();

            // Process shift statistics
            $shiftStats = [];
            $shiftNames = ['Evening', 'Morning', 'Night'];

            foreach ($shiftNames as $shiftName) {
                // Find matching schedule
                $schedule = $shifts->first(function ($shift) use ($shiftName) {
                    return stripos($shift->shift_name, $shiftName) !== false;
                });

                $designerCount = 0;
                $developerCount = 0;
                $qaCount = 0;

                if ($schedule) {
                    // Get users assigned to this shift from SchedulePlanner
                    $assignedUsers = $schedulePlanners->where('schedule_id', $schedule->id);

                    foreach ($assignedUsers as $planner) {
                        if ($planner->user && $planner->user->designations) {
                            $designation = $planner->user->designations->first();
                            if ($designation) {
                                $designationLower = strtolower($designation->name);
                                if (str_contains($designationLower, 'designer') || str_contains($designationLower, 'design')) {
                                    $designerCount++;
                                } elseif (str_contains($designationLower, 'developer') || str_contains($designationLower, 'dev')) {
                                    $developerCount++;
                                } elseif (str_contains($designationLower, 'qa') || str_contains($designationLower, 'quality') || str_contains($designationLower, 'test')) {
                                    $qaCount++;
                                }
                            }
                        }
                    }
                }

                $shiftStats[] = [
                    'name' => $shiftName,
                    'designer' => $designerCount,
                    'developer' => $developerCount,
                    'qa' => $qaCount,
                    'total' => $designerCount + $developerCount + $qaCount
                ];
            }

            return response()->json($shiftStats);

        } catch (\Exception $e) {
            \Log::error('Error fetching shift stats:', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to fetch shift statistics'], 500);
        }
    }
}
