"use strict";(self.webpackChunkcreativeapp=self.webpackChunkcreativeapp||[]).push([[260],{2260:(e,t,a)=>{a.r(t),a.d(t,{default:()=>h});var s=a(65043),o=a(13826),n=a(7726),r=a(91121),i=a(40350),l=(a(68756),a(70579));const c="https://creative.sebpo.net/api/",d=e=>{let{isVisible:t,setVisible:a,categoryId:o}=e;const[n,r]=(0,s.useState)(""),[i,d]=(0,s.useState)(""),[u,m]=(0,s.useState)("");(0,s.useEffect)((()=>{(async()=>{if(o){const e=localStorage.getItem("token");try{const t=await fetch(`${c}notice-board-category/${o}`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch category: "+t.statusText);const a=await t.json();r(a.category.name)}catch(i){d(i.message)}}})()}),[o]);return t?(0,l.jsx)("div",{className:"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50",onClick:()=>a(!1),children:(0,l.jsxs)("div",{className:"relative bg-white rounded-lg shadow-lg max-w-md w-full p-5",onClick:e=>e.stopPropagation(),children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Update Notice Board Category"}),(0,l.jsx)("button",{className:"text-gray-500 hover:text-gray-800",onClick:()=>a(!1),children:"\xd7"})]}),i&&(0,l.jsx)("div",{className:"text-red-500",children:i}),u&&(0,l.jsx)("div",{className:"text-green-500",children:u}),(0,l.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t=localStorage.getItem("token");if(!t)return void d("Authentication token is missing.");let s=localStorage.getItem("fname"),r=localStorage.getItem("lname");s&&r||(console.warn("User first and last name are missing in localStorage. Setting default values."),s=s||"Unknown",r=r||"User");const l=`${s} ${r}`;try{const e=await fetch(`${c}notice-board-category/${o}`,{method:"PUT",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({name:n.trim(),updated_by:l})});if(!e.ok)throw new Error("Failed to update category: "+e.statusText);const s=await e.json();m(`Category "${s.name}" updated successfully!`),setTimeout((()=>{a(!1),m("")}),1e3)}catch(i){d(i.message)}},children:[(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsx)("label",{htmlFor:"name",className:"block mb-2",children:"Category Name"}),(0,l.jsx)("input",{type:"text",id:"name",value:n,onChange:e=>r(e.target.value),className:"border rounded w-full p-2",required:!0})]}),(0,l.jsx)("button",{type:"submit",className:"bg-primary hover:bg-secondary text-white rounded-md px-4 py-2",children:"Update Category"})]})]})}):null},u=()=>null!==localStorage.getItem("token"),m="https://creative.sebpo.net/api/",g=()=>{const[e,t]=(0,s.useState)([]),[a,o]=(0,s.useState)(!1),[n,r]=(0,s.useState)(!0),[c,g]=(0,s.useState)(null),[h,p]=(0,s.useState)(null);(0,s.useEffect)((()=>{(async()=>{if(!u())return p("No authentication token found."),void r(!1);const e=localStorage.getItem("token");try{const a=await fetch(`${m}notice-board-category`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error("Network response was not ok: "+a.statusText);const s=await a.json();t(s.categories.map((e=>({id:e.id,name:e.name,created_by:e.created_by,updated_by:e.updated_by}))))}catch(h){p(h.message)}finally{r(!1)}})()}),[]);return h?(0,l.jsx)("div",{className:"text-red-500",children:h}):n?(0,l.jsx)("div",{className:"text-gray-500",children:"Loading..."}):0===e.length?(0,l.jsx)("div",{className:"text-gray-500",children:"No data available"}):(0,l.jsxs)("div",{children:[(0,l.jsx)(i.A,{tableContent:e,columnNames:[{label:"SL",key:"id"},{label:"Category Name",key:"name"},{label:"Created By",key:"created_by"},{label:"Updated By",key:"updated_by"}],onDelete:async e=>{if(!u())return void p("No authentication token found.");const a=localStorage.getItem("token");try{const s=await fetch(`${m}notice-board-category/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"}});if(!s.ok)throw new Error("Failed to delete category: "+s.statusText);t((t=>t.filter((t=>t.id!==e))))}catch(h){p(h.message)}},onEdit:e=>{g(e),o(!0)},setModalVisible:o,setSelectedServiceId:g}),a&&(0,l.jsx)(d,{isVisible:a,setVisible:o,categoryId:c})]})},h=()=>(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-xl",children:(0,l.jsxs)(o.A,{children:[(0,l.jsx)(n.A,{routeName:"/add-notice-category",buttonName:"Add Notice Category"}),(0,l.jsx)(g,{}),(0,l.jsx)(r.A,{})]})})})},68756:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(65043),o=a(56025),n=a(70579);const r="https://creative.sebpo.net/api/",i=e=>{let{isVisible:t,setVisible:a,dataItemsId:i}=e;const[l,c]=(0,s.useState)(""),[d,u]=(0,s.useState)(""),[m,g]=(0,s.useState)(""),[h,p]=(0,s.useState)(null);(0,s.useEffect)((()=>{const e=localStorage.getItem("user_id");e&&p(e)}),[]),(0,s.useEffect)((()=>{(async()=>{if(i){const e=localStorage.getItem("token");try{const t=await fetch(`${r}bloods/${i}`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch blood group: "+t.statusText);const a=await t.json();c(a.blood.name)}catch(d){u(d.message)}}})()}),[i]);return t?(0,n.jsx)("div",{className:"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50",onClick:()=>a(!1),children:(0,n.jsxs)("div",{className:"relative bg-white rounded-lg shadow-lg max-w-md w-full p-5",onClick:e=>e.stopPropagation(),children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold",children:"Update Blood Group"}),(0,n.jsx)("button",{className:"text-gray-500 hover:text-gray-800",onClick:()=>a(!1),children:"\xd7"})]}),d&&(0,n.jsx)("div",{className:"text-red-500",children:d}),m&&(0,n.jsx)("div",{className:"text-green-500",children:m}),(0,n.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t=localStorage.getItem("token"),s=h;if(s)if(t)try{const e=await fetch(`${r}bloods/${i}`,{method:"PUT",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({name:l.trim(),updated_by:s})});if(!e.ok)throw new Error("Failed to update blood group: "+e.statusText);const n=await e.json();(0,o.GW)({icon:"success",title:"Success!",text:(null===n||void 0===n?void 0:n.message)||"Blood group updated successfully."}),setTimeout((()=>{a(!1),g("")}),1e3)}catch(d){(0,o.GW)("error")}else u("Authentication token is missing.");else u("User is not logged in.")},children:[(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("label",{htmlFor:"name",className:"block mb-2",children:"Blood Group Name"}),(0,n.jsx)("input",{type:"text",id:"name",value:l,onChange:e=>c(e.target.value),className:"border rounded w-full p-2",required:!0})]}),(0,n.jsx)("button",{type:"submit",className:"bg-primary hover:bg-secondary text-white rounded-md px-4 py-2",children:"Update Blood Group"})]})]})}):null}}}]);
//# sourceMappingURL=260.0b846481.chunk.js.map