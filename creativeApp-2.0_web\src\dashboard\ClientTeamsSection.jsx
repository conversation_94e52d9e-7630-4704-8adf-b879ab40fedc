import React, { useEffect, useState, useRef } from "react";
import Loading from "../common/Loading";
import { API_URL } from "../common/fetchData/apiConfig";

// Component to display a single team card
const TeamCard = ({ team }) => (
  <div className="rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow p-4">
    <div className="flex items-center justify-between">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">{team.name}</h3>
      </div>
      <div className="flex items-center gap-2">
        {team.logo ? (
          <img
            src={team.logo.startsWith('http') ? team.logo : `${API_URL}${team.logo}`}
            alt={`${team.name} logo`}
            className="w-12 h-12 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-600"
            onError={(e) => {
              e.target.style.display = 'none';
              e.target.nextSibling.style.display = 'flex';
            }}
          />
        ) : null}
        <div
          className="w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-lg font-medium text-gray-500"
          style={{ display: team.logo ? 'none' : 'flex' }}
        >
          {team.name?.[0]?.toUpperCase() || "T"}
        </div>
      </div>
    </div>

    <div className="mt-4 space-y-2">
      <p className="text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2">
        <span className="material-symbols-outlined text-gray-400">person</span>
        <span>Team Lead:</span>
        <span className="font-medium text-gray-900 dark:text-gray-100">{team.teamLead}</span>
      </p>
    </div>

    <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 grid grid-cols-2 gap-3">
      <div className="rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3">
        <p className="text-xs text-gray-600 dark:text-gray-300">Total Members</p>
        <div className="mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100">
          <span>👥</span>
          <span className="font-semibold">{String(team.totalMembers).padStart(2, '0')}</span>
        </div>
      </div>
      <div className="rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3">
        <p className="text-xs text-gray-600 dark:text-gray-300">Billable Hours</p>
        <div className="mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100">
          <span>⏱️</span>
          <span className="font-semibold">{team.billableHours}hr</span>
        </div>
      </div>
    </div>
  </div>
);

// Infinite Slider Component
const InfiniteSlider = ({ teams }) => {
  const sliderRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);

  // Create infinite loop by duplicating teams (more copies for smoother loop)
  const infiniteTeams = [...teams, ...teams, ...teams, ...teams];

  // Auto-scroll functionality
  useEffect(() => {
    if (!autoScrollEnabled || isDragging || teams.length === 0) return;

    const interval = setInterval(() => {
      if (sliderRef.current) {
        const slider = sliderRef.current;
        const cardWidth = 320; // Approximate card width + gap
        const maxScroll = cardWidth * teams.length;

        // Smooth infinite loop
        if (slider.scrollLeft >= maxScroll) {
          slider.scrollTo({ left: 0, behavior: 'auto' });
        } else {
          slider.scrollLeft += 0.5; // Slower, smoother scroll
        }
      }
    }, 20); // Faster interval for smoother animation

    return () => clearInterval(interval);
  }, [autoScrollEnabled, isDragging, teams.length]);

  // Mouse drag handlers
  const handleMouseDown = (e) => {
    setIsDragging(true);
    setAutoScrollEnabled(false);
    setStartX(e.pageX - sliderRef.current.offsetLeft);
    setScrollLeft(sliderRef.current.scrollLeft);
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    e.preventDefault();
    const x = e.pageX - sliderRef.current.offsetLeft;
    const walk = (x - startX) * 2;
    sliderRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setTimeout(() => setAutoScrollEnabled(true), 2000); // Resume auto-scroll after 2s
  };

  // Touch handlers for mobile
  const handleTouchStart = (e) => {
    setIsDragging(true);
    setAutoScrollEnabled(false);
    setStartX(e.touches[0].pageX - sliderRef.current.offsetLeft);
    setScrollLeft(sliderRef.current.scrollLeft);
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;
    const x = e.touches[0].pageX - sliderRef.current.offsetLeft;
    const walk = (x - startX) * 2;
    sliderRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
    setTimeout(() => setAutoScrollEnabled(true), 2000);
  };

  return (
    <div className="relative overflow-hidden">
      <div
        ref={sliderRef}
        className="flex gap-4 overflow-x-auto scrollbar-hide cursor-grab active:cursor-grabbing"
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          WebkitOverflowScrolling: 'touch'
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {infiniteTeams.map((team, index) => (
          <div key={`${team.id}-${index}`} className="flex-shrink-0 w-80 sm:w-72 md:w-80">
            <TeamCard team={team} />
          </div>
        ))}
      </div>

      {/* Gradient overlays for smooth edges */}
      <div className="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-100 to-transparent pointer-events-none" />
      <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-100 to-transparent pointer-events-none" />
    </div>
  );
};

// Main component
function ClientTeamsSection() {
  const [teams, setTeams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchTeamsData = async () => {
      const token = localStorage.getItem("token");
      if (!token) {
        setError("No auth token found");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch teams data
        const teamsResponse = await fetch(`${API_URL}/teams`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
        });

        if (!teamsResponse.ok) {
          throw new Error('Failed to fetch teams data');
        }

        const teamsData = await teamsResponse.json();
        const teamsArray = teamsData?.teams || [];

        // The teams data now comes with calculated fields from the backend
        const processedTeams = teamsArray.map(team => ({
          id: team.id,
          name: team.name,
          logo: team.logo,
          icon: team.icon,
          teamLead: team.teamLead || 'Not Assigned',
          totalMembers: team.totalMembers || 0,
          billableHours: team.billableHours || 0,
          departments: team.departments || []
        }));

        setTeams(processedTeams);
        setError(null);
      } catch (err) {
        console.error("Error fetching teams:", err);
        setError("Unable to load teams. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchTeamsData();
  }, []);

  if (loading) return <Loading />;
  if (error) return <div className="text-red-500 text-center py-4">{error}</div>;
  if (!teams.length) return <div className="text-gray-500 text-center py-4">No teams available</div>;

  return <InfiniteSlider teams={teams} />;
}

// Add CSS for hiding scrollbars
const style = document.createElement('style');
style.textContent = `
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
`;
if (!document.head.querySelector('style[data-scrollbar-hide]')) {
  style.setAttribute('data-scrollbar-hide', 'true');
  document.head.appendChild(style);
}

// Export the component
export default ClientTeamsSection;
