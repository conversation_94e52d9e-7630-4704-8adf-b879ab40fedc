import React, { useState, useEffect } from 'react';
import { RefreshCw } from 'lucide-react';

const PasswordGenerator = ({ onPasswordGenerated }) => {
  const [password, setPassword] = useState('xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn');
  const [length, setLength] = useState(64);
  const [options, setOptions] = useState({
    uppercase: true,
    lowercase: true,
    numbers: true,
    symbols: true
  });
  const [strength, setStrength] = useState('Strong Password');

  const generatePassword = () => {
    let charset = '';
    if (options.uppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (options.lowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
    if (options.numbers) charset += '0123456789';
    if (options.symbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';

    if (charset === '') {
      setPassword('');
      return;
    }

    let newPassword = '';
    for (let i = 0; i < length; i++) {
      newPassword += charset.charAt(Math.floor(Math.random() * charset.length));
    }

    setPassword(newPassword);
    
    // Calculate strength
    const newStrength = calculatePasswordStrength(newPassword);
    setStrength(newStrength);
    
    // Notify parent component
    if (onPasswordGenerated) {
      onPasswordGenerated(newPassword, newStrength);
    }
  };

  const calculatePasswordStrength = (pwd) => {
    let score = 0;
    
    // Length check
    if (pwd.length >= 12) score += 2;
    else if (pwd.length >= 8) score += 1;
    
    // Character variety checks
    if (/[a-z]/.test(pwd)) score += 1;
    if (/[A-Z]/.test(pwd)) score += 1;
    if (/[0-9]/.test(pwd)) score += 1;
    if (/[^A-Za-z0-9]/.test(pwd)) score += 1;
    
    // Additional complexity
    if (pwd.length >= 16) score += 1;
    
    if (score >= 6) return 'Strong Password';
    if (score >= 4) return 'Moderate Password';
    return 'Weak Password';
  };

  const getStrengthColor = () => {
    switch (strength) {
      case 'Strong Password':
        return 'bg-green/10 text-[#22C55E]';
      case 'Moderate Password':
        return 'bg-yellow/10 text-[#F59E0B]';
      case 'Weak Password':
        return 'bg-red/10 text-[#EF4444]';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(password);
    // You could add a toast notification here
  };

  const handleOptionChange = (option) => {
    const newOptions = { ...options, [option]: !options[option] };
    setOptions(newOptions);
  };

  const handleLengthChange = (e) => {
    const newLength = parseInt(e.target.value);
    setLength(newLength);
  };

  // Auto-generate when options change
  useEffect(() => {
    generatePassword();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [length, options]);

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      {/* Title outside container */}
      <h1 className="text-left text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6">
        Password Manager
      </h1>

      {/* Main rounded container */}
      <div className="rounded-2xl border border-[#DFECF1] p-4 sm:p-6 lg:p-8">
        {/* Password Generator Section - Center */}
        <div className="text-center mb-6 sm:mb-8 rounded-2xl p-3 sm:p-6">
          <div className="relative inline-block w-full max-w-6xl">
            <input
              type="text"
              value={password}
              readOnly
              className="w-full px-4 sm:px-6 py-2 pr-12 sm:pr-16 bg-gray-50 border border-gray-200 rounded-full text-sm sm:text-lg text-gray-700 font-mono text-center break-all"
            />
            <button
              onClick={generatePassword}
              className="absolute right-3 sm:right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <RefreshCw className="w-4 h-4 sm:w-5 sm:h-5" />
            </button>
          </div>

          {/* Strong Password badge (bottom-left) and Copy Password button (right) */}
          <div className="flex flex-col sm:flex-row items-center justify-between gap-3 sm:gap-0 mt-4 max-w-6xl mx-auto">
            <span
              className={`px-3 sm:px-4 py-2 rounded-full text-xs sm:text-sm font-medium ${getStrengthColor()}`}
            >
              {strength}
            </span>
            <button
              onClick={copyToClipboard}
              className="w-full sm:w-auto px-8 sm:px-12 py-3 bg-[#FF9F19] hover:bg-yellow-500 text-white rounded-lg text-sm font-medium transition-colors"
            >
              Copy Password
            </button>
          </div>
        </div>

       {/* Password Length Section */}
<div className="mb-6 sm:mb-8">
  <div className="flex flex-col gap-2 max-w-2xl mx-auto">
    
    {/* Row: Label and Slider in the same line */}
    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
      <span className="text-base sm:text-lg font-semibold text-primary-600 whitespace-nowrap">
        Password Length: {length}
      </span>

      <input
        type="range"
        min="4"
        max="128"
        value={length}
        onChange={handleLengthChange}
        className="slider w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
        style={{
          background: `linear-gradient(to right, #006f99 0%, #006f99 ${
            ((length - 4) / (128 - 4)) * 100
          }%, #e5e7eb ${
            ((length - 4) / (128 - 4)) * 100
          }%, #e5e7eb 100%)`,
        }}
      />
    </div>

    {/* Hint row */}
    <div className="text-xs sm:text-sm text-gray-600 text-center sm:text-right">
      *Between 4 and 128 characters
    </div>
  </div>
</div>


        {/* Character Options - Single Row */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:flex lg:items-center lg:justify-center gap-4 lg:gap-8 max-w-4xl mx-auto">
          <div className="flex items-center">
            <div className="relative">
              <input
                id="uppercase"
                type="checkbox"
                checked={options.uppercase}
                onChange={() => handleOptionChange("uppercase")}
                className="w-5 h-5 appearance-none rounded border-2 border-gray-300 bg-white checked:bg-primary checked:border-transparent"
              />
            </div>
            <label
              htmlFor="uppercase"
              className="ml-2 text-xs sm:text-sm font-medium text-gray-700 whitespace-nowrap"
            >
              Uppercase (A-Z)
            </label>
          </div>

          <div className="flex items-center">
            <div className="relative">
              <input
                id="lowercase"
                type="checkbox"
                checked={options.lowercase}
                onChange={() => handleOptionChange("lowercase")}
                className="w-4 h-4 sm:w-5 sm:h-5 appearance-none rounded border-2 border-gray-300 bg-white checked:bg-primary checked:border-transparent"
              />
              
            </div>
            <label
              htmlFor="lowercase"
              className="ml-2 text-xs sm:text-sm font-medium text-gray-700 whitespace-nowrap"
            >
              Lowercase (a-z)
            </label>
          </div>

          <div className="flex items-center">
            <div className="relative">
              <input
                id="numbers"
                type="checkbox"
                checked={options.numbers}
                onChange={() => handleOptionChange("numbers")}
                className="w-4 h-4 sm:w-5 sm:h-5 appearance-none rounded border-2 border-gray-300 bg-white checked:bg-primary checked:border-transparent"
              />

            </div>
            <label
              htmlFor="numbers"
              className="ml-2 text-xs sm:text-sm font-medium text-gray-700 whitespace-nowrap"
            >
              Number (0-9)
            </label>
          </div>

          <div className="flex items-center">
            <div className="relative">
              <input
                id="symbols"
                type="checkbox"
                checked={options.symbols}
                onChange={() => handleOptionChange("symbols")}
                className="w-4 h-4 sm:w-5 sm:h-5 appearance-none rounded border-2 border-gray-300 bg-white checked:bg-primary checked:border-transparent"
              />
            </div>
            <label
              htmlFor="symbols"
              className="ml-2 text-xs sm:text-sm font-medium text-gray-700 whitespace-nowrap"
            >
              Symbols (!@#$%^&*)
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PasswordGenerator;