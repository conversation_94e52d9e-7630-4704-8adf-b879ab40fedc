<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TimeCard extends Model
{
    use HasFactory;

    protected $fillable = [
        'team_id',
        'department_id',
        'date',
        'month',
        'week',
        'year',
        'quarter',
        'shift_id',
        'user_id',
        'ticket',
        'product_type_id',
        'task_type_id',
        'record_type_id',
        'revision_type_id',
        'priority_id',
        'unit',
        'hour',
        'reporter_id',
        'region_id',
        'review_id',
        // 'workflow_id',
        'account',
        'campaign',
        'sla',
        'high_priority',
        'client_error',
        'internal_error',
        'notes',
        'created_by',
        'updated_by'
    ];

    /**
     * Relationship to get the user who created this record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship to get the user who updated this record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Relationship to get the product type associated with the time card.
     */
    public function product_type()
    {
        return $this->belongsTo(ProductType::class, 'product_type_id');
    }

    /**
     * Relationship to get the task type associated with the time card.
     */
    public function task_type()
    {
        return $this->belongsTo(TaskType::class, 'task_type_id');
    }

    /**
     * Relationship to get the revision type associated with the time card.
     */
    public function revision_type()
    {
        return $this->belongsTo(RevisionType::class, 'revision_type_id');
    }

    /**
     * Relationship to get the record type associated with the time card.
     */
    public function record_type()
    {
        return $this->belongsTo(RecordType::class, 'record_type_id');
    }

    /**
     * Relationship to get the priority associated with the time card.
     */
    public function priority()
    {
        return $this->belongsTo(Priority::class, 'priority_id');
    }

    /**
     * Relationship to get the region associated with the time card.
     */
    public function region()
    {
        return $this->belongsTo(Region::class, 'region_id');
    }

    /**
     * Relationship to get the reporter associated with the time card.
     */
    public function reporter()
    {
        return $this->belongsTo(Reporter::class, 'reporter_id');
    }

    /**
     * Relationship to get the team associated with the time card.
     */
    public function team()
    {
        return $this->belongsTo(Team::class, 'team_id');
    }

    public function departments()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

    public function schedule()
    {
        return $this->belongsTo(Schedule::class, 'shift_id');
    }

    public function users()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Relationship to get the review & release associated with the time card.
     */
    public function review()
    {
        return $this->belongsTo(ReviewRelease::class, 'review_id');
    }

    /**
     * Relationship to get the Workflow associated with the time card.
     */
    public function workflow()
    {
        return $this->belongsTo(Workflow::class, 'workflow_id');
    }
}
