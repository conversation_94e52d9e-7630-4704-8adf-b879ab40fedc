import React, { useEffect, useState } from 'react';
import { alertMessage } from '../../common/coreui';
import useFetchApiData from '../../common/fetchData/useFetchApiData';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditMember = ({ isVisible, setVisible, dataItemsId }) => {
    const [eid, setEid] = useState(''); 
    const [email, setEmail] = useState('');
    const [roles, setRoles] = useState([]); 
    const [selectedRoles, setSelectedRoles] = useState([]); 
    const [teams, setTeams] = useState([]); 
    const [selectedTeams, setSelectedTeams] = useState([]); 
    const [defaultTeamId, setDefaultTeamId] = useState(null);
    const [hoveredTeamId, setHoveredTeamId] = useState(null);
    const [designations, setDesignations] = useState([]); 
    const [selectedDesignations, setSelectedDesignations] = useState(''); 
    const [resourceTypes, setResourceTypes] = useState([]); 
    const [selectedResourceTypes, setSelectedResourceTypes] = useState(''); 
    const [departments, setDepartments] = useState([]); 
    const [selectedDepartments, setSelectedDepartments] = useState([]); 
    const [contactTypes, setContactTypes] = useState([]); 
    const [selectedContactTypes, setSelectedContactTypes] = useState([]);
    const [resourceStatuses, setResourceStatuses] = useState([]); 
    const [selectedResourceStatuses, setSelectedResourceStatuses] = useState([]); 
    const [billingStatuses, setBillingStatuses] = useState([]); 
    const [selectedBillingStatuses, setSelectedBillingStatuses] = useState([]);
    const [availableStatuses, setAvailableStatuses] = useState([]);
    const [selectedAvailableStatuses, setSelectedAvailableStatuses] = useState([]);
    const [memberStatuses, setMemberStatuses] = useState([]);
    const [selectedMemberStatuses, setSelectedMemberStatuses] = useState([]);
    const [branches, setBranches] = useState([]);
    const [selectedBranches, setSelectedBranches] = useState([]);
    const [onsiteStatuses, setOnsiteStatuses] = useState([]);
    const [selectedOnsiteStatuses, setSelectedOnsiteStatuses] = useState([]);
    const [error, setError] = useState('');
    const [errorMessage, setErrorMessage] = useState("");
    const [successMessage, setSuccessMessage] = useState('');
    const [loading, setLoading] = useState(false);

    // Regular Users
    const [fname, setFname] = useState('');
    const [lname, setLname] = useState('');
    const [about, setAbout] = useState('');
    const [birthday, setBirthday] = useState('');
    const [birthdayCelebration, setBirthdayCelebration] = useState('');
    const [birthdayCelebrationDate, setBirthdayCelebrationDate] = useState('');
    const [gender, setGender] = useState('');
    const [maritalStatus, setMaritalStatus] = useState('');
    const [nickName, setNickName] = useState('');
    const [primaryContact, setPrimaryContact] = useState('');
    const [secondaryContact, setSecondaryContact] = useState('');
    const [emergencyContact, setEmergencyContact] = useState('');
    const [relationContact, setRelationContact] = useState('');
    const [presentAddress, setPresentAddress] = useState('');
    const [permanentAddress, setPermanentAddress] = useState('');
    const [bloodDonate, setBloodDonate] = useState('');
    const [prevDesignation, setPrevDesignation] = useState('');
    const [deskId, setDeskId] = useState('');
    const [joiningDate, setJoiningDate] = useState('');
    const [terminationDate, setTerminationDate] = useState('');
    const [employmentEndDate, setEmploymentEndDate] = useState('');
    const [bloodsGroup, setBloodsGroup] = useState([]);
    const [selectedBloods, setSelectedBloods] = useState([]);
    const [workAnniversary, setWorkAnniversary] = useState('');
    const [photo, setPhoto] = useState(null);
    const [existingPhoto, setExistingPhoto] = useState('');
    const [newPhoto, setNewPhoto] = useState(null);

    const token = localStorage.getItem('token');

    // Fetching data using the custom hook
    const { data: bloodsGroupData } = useFetchApiData(`${API_URL}bloods`, token);

    console.log("Edited Id", dataItemsId);

    useEffect(() => {
        if (bloodsGroupData && bloodsGroupData.bloods) {
            setBloodsGroup(bloodsGroupData.bloods || []);
        }
    }, [bloodsGroupData]);


    useEffect(() => {
        const fetchMember = async () => {
            console.log("Edited Id", dataItemsId);
            if (dataItemsId) {
                const token = localStorage.getItem('token');
                try {
                    const response = await fetch(`${API_URL}/users/${dataItemsId}`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    });
        
                    if (!response.ok) {
                        throw new Error('Failed to fetch member: ' + response.statusText);
                    }
        
                    const data = await response.json();

                    console.log("Fetched Member Data:", data.user);
        
                    // Initialize fields
                    setEid(data.eid || ''); // Set eid, default to empty string
                    setEmail(data.email || '');
        
                    // Initialize roles based on fetched member data
                    const memberRoles = data.roles || [];
                    const rolesMap = {};
                    memberRoles.forEach(role => {
                        rolesMap[role.id] = true; // Store the selected role IDs as checked
                    });
                    setSelectedRoles(rolesMap);
        
                    // Initialize teams
                    const memberTeams = data.teams || [];
                    const teamsMap = {};
                    let defaultTeam = null;
        
                    memberTeams.forEach(team => {
                        console.log(`Team: ${team.name}, is_default: ${team.pivot.is_default}`);  // Debug log for pivot.is_default
                        teamsMap[team.id] = true; // Store selected teams as checked
                        if (team.pivot.is_default === 1) {  // Check is_default inside pivot
                            defaultTeam = team.id;  // Set the default team based on pivot.is_default
                            console.log(`Found default team: ${team.name} (ID: ${team.id})`);  // Debug log
                        }
                    });
        
                    if (defaultTeam === null && memberTeams.length > 0) {
                        defaultTeam = memberTeams[0].id; // Fallback to first team as default if no default is found
                        console.log(`No default team found. Setting first team as default: ${memberTeams[0].name}`);
                    }

                    // Utility function to format date as yyyy-MM-dd
                    const formatDate = (date) => {
                        if (!date) return '';
                        const formattedDate = new Date(date);
                        return formattedDate.toISOString().split('T')[0]; // Extracts yyyy-MM-dd
                    };
        
                    console.log("Default Team ID after loop:", defaultTeam);  // Debug log
                    setSelectedTeams(teamsMap);
                    setDefaultTeamId(defaultTeam);  // Set the default team
        
                    // Log after state updates
                    console.log("Selected Teams:", teamsMap);
                    console.log("Default Team ID:", defaultTeam);
        
                    // Initialize designations (Single selection)
                    const memberDesignations = data.designations || [];
                    const selectedDesignationIds = memberDesignations.map(designation => designation.id);
                    setSelectedDesignations(selectedDesignationIds[0] || '');  // Set first value if any
        
                    // Initialize resourceTypes (Single selection)
                    const memberResourceTypes = data.resource_types || [];
                    const selectedResourceTypeIds = memberResourceTypes.map(resourceType => resourceType.id);
                    setSelectedResourceTypes(selectedResourceTypeIds[0] || '');  // Set first value if any
        
                    // Initialize other fields that handle multiple data
                    setSelectedDepartments(data.departments?.map(department => department.id) || []);
                    setSelectedResourceStatuses(data.resource_statuses?.map(resourceStatus => resourceStatus.id) || []);
                    setSelectedBillingStatuses(data.billing_statuses?.map(billingStatus => billingStatus.id) || []);
                    setSelectedContactTypes(data.contact_types?.map(contactType => contactType.id) || []);
                    setSelectedAvailableStatuses(data.available_statuses?.map(availableStatus => availableStatus.id) || []);
                    setSelectedMemberStatuses(data.member_statuses?.map(memberStatus => memberStatus.id) || []);
                    setSelectedBranches(data.branches?.map(branch => branch.id) || []);
                    setSelectedOnsiteStatuses(data.onsite_statuses?.map(onsiteStatus => onsiteStatus.id) || []);

                    // Regular Users Information
                    setFname(data.fname || '');
                    setLname(data.lname || '');
                    setAbout(data.about || '');
                    setBirthday(formatDate(data.birthday) || ''); // Format the birthday
                    setBirthdayCelebration(data.birthday_celebration || '');
                    setBirthdayCelebrationDate(formatDate(data.birthday_celebration_date) || ''); // Format the celebration date
                    setGender(data.gender || '');
                    setMaritalStatus(data.marital_status || '');
                    setNickName(data.nick_name || '');
                    setPrimaryContact(data.primary_contact || '');
                    setSecondaryContact(data.secondary_contact || '');
                    setEmergencyContact(data.emergency_contact || '');
                    setRelationContact(data.relation_contact || '');
                    setPresentAddress(data.present_address || '');
                    setPermanentAddress(data.permanent_address || '');
                    setBloodDonate(data.blood_donate || '');
                    setPrevDesignation(data.prev_designation || '');
                    setDeskId(data.desk_id || '');
                    setJoiningDate(formatDate(data.joining_date) || ''); // Format the joining date
                    setTerminationDate(formatDate(data.termination_date) || ''); // Format the joining date
                    setEmploymentEndDate(formatDate(data.employment_end) || ''); // Format the joining date
                    setWorkAnniversary(formatDate(data.work_anniversary) || ''); // Format the work anniversary date
            
                    // Set the selected bloods (only IDs) from the fetched data
                    setSelectedBloods(data.bloods ? data.bloods.map(blood => blood.id) : []);

                    // Set the existing photo URL if available
                    setExistingPhoto(data.photo || '');
        
                } catch (error) {
                    setError(error.message);
                }
            }
        };
        
        
        const fetchRoles = async () => {
            const token = localStorage.getItem('token');
            try {
                const response = await fetch(`${API_URL}roles`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    const errorMessage = await response.text();
                    throw new Error(`Network response was not ok: ${errorMessage}`);
                }

                const data = await response.json();

                setRoles(data.roles);

            } catch (error) {
                setError(error.message);
            }
        };

        // Fetching all the teams to select for users
        const fetchTeams = async () => {
                  
            const token = localStorage.getItem('token');
        
            try {
                const response = await fetch(`${API_URL}/teams`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
                }
        
                const data = await response.json();
        
                setTeams(data.teams);
           
            } catch (error) {
                setError(error.message);
            }
        };

        // Fetching all the departments to select for users
        const fetchDepartments = async () => {
        
            const token = localStorage.getItem('token');
        
            try {
                const response = await fetch(`${API_URL}departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
                }
        
                const data = await response.json();
        
                setDepartments(data.departments);

            } catch (error) {
                setError(error.message);
            }
        };

        // Fetching all the designations to select for users
        const fetchDesignations = async () => {

            const token = localStorage.getItem('token');
        
            try {
                const response = await fetch(`${API_URL}designations`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
                }
        
                const data = await response.json();
        
                setDesignations(data.designations);

            } catch (error) {
                setError(error.message);
            }
        };

        // Fetching all the Resource Type (Responsibility Level) to select for users
        const fetchResourceTypes = async () => {

            const token = localStorage.getItem('token');
        
            try {
                const response = await fetch(`${API_URL}resource_types`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
                }
        
                const data = await response.json();
        
                setResourceTypes(data['Resource Types']);

            } catch (error) {
                setError(error.message);
            }
        };

        // Fetching all the resourceStatuses to select for users
        const fetchResourceStatuses = async () => {

            const token = localStorage.getItem('token');
        
            try {
                const response = await fetch(`${API_URL}resource_statuses`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
                }
        
                const data = await response.json();

        
                setResourceStatuses(data.resource_status);

            } catch (error) {
                setError(error.message);
            }
        };

        // Fetching all the billing statuses to select for users
        const fetchBillingStatuses = async () => {

            const token = localStorage.getItem('token');
        
            try {
                const response = await fetch(`${API_URL}billing_statuses`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
                }
        
                const data = await response.json();
                console.log('Billing Status:', data);
        
                setBillingStatuses(data['billing statuses']);

            } catch (error) {
                setError(error.message);
            }
        };

        // Fetching all the contact types to select for users
        const fetchContactTypes = async () => {

            const token = localStorage.getItem('token');
        
            try {
                const response = await fetch(`${API_URL}contact_types`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
                }
        
                const data = await response.json();
        
                setContactTypes(data.contact_types);

            } catch (error) {
                setError(error.message);
            }
        };

        // Fetching all the available status to select for users
        const fetchAvailableStatuses = async () => {

            const token = localStorage.getItem('token');
        
            try {
                const response = await fetch(`${API_URL}available_statuses`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
                }
        
                const data = await response.json();
        
                setAvailableStatuses(data.available_statuses);

            } catch (error) {
                setError(error.message);
            }
        };

        // Fetching all the team member status to select for users
        const fetchMemberStatuses = async () => {

            const token = localStorage.getItem('token');
        
            try {
                const response = await fetch(`${API_URL}member_statuses`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
                }
        
                const data = await response.json();
        
                setMemberStatuses(data.member_statuses);

            } catch (error) {
                setError(error.message);
            }
        };
        
        // Fetching all the branch to select for users
        const fetchBranches = async () => {

            const token = localStorage.getItem('token');
        
            try {
                const response = await fetch(`${API_URL}branches`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
                }
        
                const data = await response.json();
        
                setBranches(data.branches);

            } catch (error) {
                setError(error.message);
            }
        };

        // Fetching all the branch to select for users
        const fetchOnsiteStatuses = async () => {

            const token = localStorage.getItem('token');
        
            try {
                const response = await fetch(`${API_URL}onsite_statuses`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
                }
        
                const data = await response.json();
                console.log('Onsite:', data);
        
                setOnsiteStatuses(data.onsite_statuses);

            } catch (error) {
                setError(error.message);
            }
        };

        fetchMember();
        fetchRoles();
        fetchTeams();
        fetchBranches();
        fetchDepartments();
        fetchBillingStatuses();
        fetchContactTypes();
        fetchDesignations();
        fetchMemberStatuses();
        fetchOnsiteStatuses();
        fetchResourceStatuses();
        fetchResourceTypes();
        fetchAvailableStatuses();
    }, [dataItemsId]);

    const handleRoleChange = (roleId) => {
        setSelectedRoles(prev => ({
            ...prev,
            [roleId]: !prev[roleId],
        }));
    };
    
    // Handle department change (single selection)
    const handleDepartmentChange = (departmentId) => {
        setSelectedDepartments([departmentId]);  // For single selection (radio button), just set the selected department
    };

    // Handle team change (multiple selections)
    const handleTeamChange = (teamId) => {
        setSelectedTeams((prevSelectedTeams) => {
            const updatedTeams = { ...prevSelectedTeams, [teamId]: !prevSelectedTeams[teamId] };
            return updatedTeams;
        });
    };

    // Handle the default team change (radio button selection)
    const handleDefaultTeamChange = (teamId) => {
        setDefaultTeamId(teamId); // Set selected team as the default
    };

    // Filter teams based on selected departments
    const filteredTeams = teams.filter(team => 
        selectedDepartments.some(departmentId =>
        team.departments?.some(department => department.id === departmentId)
        )
    );
    
    const handleResourceStatusChange = (resourceStatusId) => {
        // For single selection (radio or dropdown), set only one resource status
        setSelectedResourceStatuses([resourceStatusId]);
    };
    
    const handleBillingStatusChange = (billingStatusId) => {
        // For single selection (radio or dropdown), set only one billing status
        setSelectedBillingStatuses([billingStatusId]);
    };
    
    const handleContactTypeChange = (contactTypeId) => {
        // For single selection (radio or dropdown), set only one contact type
        setSelectedContactTypes([contactTypeId]);
    };
    
    const handleAvailableStatusChange = (availableStatusId) => {
        // For single selection (radio or dropdown), set only one available status
        setSelectedAvailableStatuses([availableStatusId]);
    };
    
    const handleMemberStatusChange = (memberStatusId) => {
        // For single selection (radio or dropdown), set only one member status
        setSelectedMemberStatuses([memberStatusId]);
    };
    
    const handleOnsiteStatusChange = (onsiteStatusId) => {
        setSelectedOnsiteStatuses([onsiteStatusId]);
    };

    const handleBranchChange = (branchId) => {
        setSelectedBranches([branchId]);
    };

    // Handle Photo
    const handlePhotoChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            // Check file size
            if (file.size > 100 * 1024) { // 100KB limit
                setErrorMessage("File size should be less than 100KB");
                setPhoto(null); // Clear the photo if it exceeds the size
                return;
            }

            // Create a temporary image element to check dimensions
            const img = new Image();
            img.onload = () => {
                if (img.width > 300 || img.height > 300) { // 300x300 dimensions limit
                    setErrorMessage("Image dimensions should be 300x300 pixels or smaller");
                    setPhoto(null); // Clear the photo if it exceeds the dimensions
                } else {
                    setErrorMessage(""); // Clear error message if valid
                    setPhoto(file); // Set the photo if valid
                }
            };
            img.onerror = () => {
                setErrorMessage("Invalid image file");
                setPhoto(null); // Clear the photo if it's not a valid image
            };
            img.src = URL.createObjectURL(file); // Load the image
        }
    };  

    const handleSubmit = async (event) => {
        event.preventDefault();

        setLoading(true);

        const token = localStorage.getItem('token');
    
        const trimmedEid = String(eid).trim();
        const trimmedEmail = String(email).trim();
    
        console.log("Submitting EID:", trimmedEid);
    
        // Prepare selected data as arrays
        const assignedRoles = Object.keys(selectedRoles).filter(roleId => selectedRoles[roleId]);
        const assignedTeams = Object.keys(selectedTeams).filter(teamId => selectedTeams[teamId]);
    
        // Single select fields, directly push as array (ensures we pass an array even for single selections)
        const assignedDepartments = Array.isArray(selectedDepartments) ? selectedDepartments : [selectedDepartments];
        const assignedDesignations = selectedDesignations ? [selectedDesignations] : [];
        const assignedResourceTypes = selectedResourceTypes ? [selectedResourceTypes] : [];
        const assignedResourceStatuses = Array.isArray(selectedResourceStatuses) ? selectedResourceStatuses : [selectedResourceStatuses];
        const assignedBillingStatuses = Array.isArray(selectedBillingStatuses) ? selectedBillingStatuses : [selectedBillingStatuses];
        const assignedContactTypes = Array.isArray(selectedContactTypes) ? selectedContactTypes : [selectedContactTypes];
        const assignedAvailableStatuses = Array.isArray(selectedAvailableStatuses) ? selectedAvailableStatuses : [selectedAvailableStatuses];
        const assignedMemberStatuses = Array.isArray(selectedMemberStatuses) ? selectedMemberStatuses : [selectedMemberStatuses];
        const assignedBranches = Array.isArray(selectedBranches) ? selectedBranches : [selectedBranches];
        const assignedOnsiteStatuses = Array.isArray(selectedOnsiteStatuses) ? selectedOnsiteStatuses : [selectedOnsiteStatuses];
    
        try {

            const token = localStorage.getItem('token');
            const userId = localStorage.getItem('user_id');
            if (!token || !userId) {
                setError('Authentication token or user ID is missing.');
                return;
            }

            const requestData = {
                eid: trimmedEid,  // Trimmed EID
                email: trimmedEmail,
                designations: assignedDesignations,
                resource_types: assignedResourceTypes,
                roles: assignedRoles,
                teams: assignedTeams,
                default_team_id: defaultTeamId,
                departments: assignedDepartments,
                resource_statuses: assignedResourceStatuses,
                billing_statuses: assignedBillingStatuses,
                contact_types: assignedContactTypes,
                available_statuses: assignedAvailableStatuses,
                member_statuses: assignedMemberStatuses,
                branches: assignedBranches,
                onsite_statuses: assignedOnsiteStatuses,

                // Regular users information
                fname,
                lname,
                about,
                birthday,
                birthday_celebration: birthdayCelebration,
                birthday_celebration_date: birthdayCelebrationDate,
                gender,
                marital_status: maritalStatus,
                nick_name: nickName,
                primary_contact: primaryContact,
                secondary_contact: secondaryContact,
                emergency_contact: emergencyContact,
                relation_contact: relationContact,
                present_address: presentAddress,
                permanent_address: permanentAddress,
                blood_donate: bloodDonate,
                prev_designation: prevDesignation,
                desk_id: deskId,
                joining_date: joiningDate,
                termination_date: terminationDate,
                employment_end: employmentEndDate,
                work_anniversary: workAnniversary,
                bloods: selectedBloods,
                updated_by: userId,
            };

            // Convert the photo to Base64 if provided
            if (photo && photo instanceof File) {
                const photoBase64 = await convertToBase64(photo);
                requestData.photo = photoBase64;
            } else {
                console.log("No valid photo file selected.");
            }

            const response = await fetch(`${API_URL}/users/${dataItemsId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData),
            });
    
            if (!response.ok) {
                throw new Error(`Failed to update member: ${response.statusText}`);
            }
    
            const result = await response.json();
            //setSuccessMessage(`Member "${result.eid}" updated successfully!`);
            alertMessage('success')

            setLoading(false);
    
            // Hide the modal after 1 second and reset success message
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage('');
            }, 2000);
    
        } catch (error) {
            //setError(error.message);
            alertMessage('error')
            setLoading(false);
        }
    };

    // Helper function to convert a file to Base64
    const convertToBase64 = (file) => {
        return new Promise((resolve, reject) => {
            if (file instanceof File) {  // Check if the parameter is a valid File object
                const reader = new FileReader();
                reader.onloadend = () => resolve(reader.result); // resolve with the Base64 string
                reader.onerror = reject;
                reader.readAsDataURL(file); // Convert file to Base64
            } else {
                reject('The provided object is not a valid File.');
            }
        });
    };
    

    if (!isVisible) return null;

    return (
        <div
            className="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50 overflow-hidden"
            onClick={() => setVisible(false)}
        >
            <div
                className="relative bg-white rounded-lg shadow-lg w-full max-w-6xl"
                onClick={(e) => e.stopPropagation()}
            >
                <div className="flex justify-between items-center mb-4 bg-gray-100 px-4 py-2">
                    <h4 className="text-base font-medium text-left text-gray-800">Edit Team Member</h4>
                    <button
                        className="text-3xl text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>
                <form onSubmit={handleSubmit}>
                    <div className='flex flex-wrap gap-4 p-5 overflow-y-auto max-h-[80vh] scrollbar-vertical'>
                        {/* Employee ID */}
                        <div className='w-full md:max-w-[23%] text-left'>
                            <div className="">
                                <label htmlFor="eid" className="block pb-2 text-base text-gray-600">Employee ID</label>
                                <input
                                    type="text"
                                    id="eid"
                                    value={eid}
                                    onChange={(e) => setEid(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                />
                            </div>
                        </div>
                        {/* Email */}
                        <div className='w-full md:max-w-[23%] text-left'>
                            <div className="">
                                <label htmlFor="email" className="block pb-2 text-base text-gray-600">Email</label>
                                <input
                                    type="email"
                                    id="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                />
                            </div>
                        </div>
                        {/* Select Designations */}
                        <div className="w-full md:max-w-[23%] text-left">
                            <label htmlFor="designations" className="block pb-2 text-base text-gray-600">Designation</label>
                            <select
                                id="designations"
                                value={selectedDesignations || ''} // Handle the single value
                                onChange={(e) => setSelectedDesignations(e.target.value)} // Update with selected value
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            >
                                <option value="" disabled>Select a designation</option>
                                {designations.map((designation) => (
                                    <option key={designation.id} value={designation.id}>
                                        {designation.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                        {/* Select Resource Types */}
                        <div className="w-full md:max-w-[23%] text-left">
                            <label htmlFor="resourceTypes" className="block pb-2 text-base text-gray-600">Responsibility Level</label>
                            <select
                                id="resourceTypes"
                                value={selectedResourceTypes || ''} // Handle the single value
                                onChange={(e) => setSelectedResourceTypes(e.target.value)} // Update with selected value
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            >
                                <option value="" disabled>Select a Responsibility Level</option>
                                {resourceTypes.map((resourceType) => (
                                    <option key={resourceType.id} value={resourceType.id}>
                                        {resourceType.name}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Select Roles */}
                        <div className='mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4'>
                            <div className="">
                                <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">Roles</label>
                                <div className='flex flex-col p-4'>
                                    {roles.map(role => (
                                        <div key={role.id} className="flex items-center mb-2">
                                            <input
                                                type="checkbox"
                                                id={`role-${role.id}`}
                                                checked={selectedRoles[role.id] || false}
                                                onChange={() => handleRoleChange(role.id)}
                                            />
                                            <label htmlFor={`role-${role.id}`} className="ml-2">{role.name}</label>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Select Department */}
                        <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                            <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                            Department
                            </label>
                            <div className="flex flex-col p-4">
                            {departments.map(department => (
                                <label className="inline-flex items-center" key={department.id}>
                                <input
                                    type="radio"
                                    name="department"
                                    value={department.id}
                                    checked={selectedDepartments.includes(department.id)}
                                    onChange={() => handleDepartmentChange(department.id)}
                                    className="form-radio my-2"
                                />
                                <span className="ml-2">{department.name}</span>
                                </label>
                            ))}
                            </div>
                        </div>

                        {/* Select Teams */}
                        <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                            <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                Teams
                            </label>
                            <div className="flex flex-col p-4">
                                {filteredTeams.length > 0 ? (
                                    filteredTeams.map((team) => (
                                        <div
                                            className="flex items-center justify-between"
                                            key={team.id}
                                            onMouseEnter={() => setHoveredTeamId(team.id)}  // Set hovered team ID
                                            onMouseLeave={() => setHoveredTeamId(null)}  // Reset hovered team ID
                                        >
                                            <label className="inline-flex items-center">
                                                <input
                                                    type="checkbox"
                                                    checked={selectedTeams[team.id] || false}
                                                    onChange={() => handleTeamChange(team.id)}
                                                    className="form-checkbox my-2"
                                                />
                                                <span className="ml-2">{team.name}</span>
                                            </label>

                                            {/* Show the default team checkbox and hide the popup for default team */}
                                            {defaultTeamId !== team.id && hoveredTeamId === team.id && (
                                                <div className="relative flex flex-row gap-1 w-24 border border-gray-200 rounded p-1 justify-center items-center">
                                                    <span className="text-[10px] text-gray-500 whitespace-nowrap">Make Default</span>
                                                    <input
                                                        type="radio"
                                                        checked={defaultTeamId === team.id}
                                                        onChange={() => handleDefaultTeamChange(team.id)}
                                                        className="form-radio"
                                                    />
                                                </div>
                                            )}

                                            {/* Always show checkbox for the default team */}
                                            {defaultTeamId === team.id && (
                                                <div className="relative flex flex-row gap-1 w-24 border border-gray-200 rounded p-1 justify-center items-center">
                                                    <span className="text-[10px] text-gray-500 whitespace-nowrap">
                                                        Default Team
                                                    </span>
                                                    <input
                                                        type="radio"
                                                        checked={true}
                                                        disabled
                                                        className="form-radio"
                                                    />
                                                </div>
                                            )}
                                        </div>
                                    ))
                                ) : (
                                    <p>Select a department to show teams</p>
                                )}
                            </div>
                        </div>
                        {/* Resource Status */}
                        <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                            <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                Resource Status
                            </label>
                            <div className="flex flex-col p-4">
                                {resourceStatuses.map(resourceStatus => (
                                    <label className="inline-flex items-center" key={resourceStatus.id}>
                                        <input
                                            type="radio"
                                            name="resourceStatus"
                                            value={resourceStatus.id}
                                            checked={selectedResourceStatuses.includes(resourceStatus.id)}
                                            onChange={() => handleResourceStatusChange(resourceStatus.id)}
                                            className="form-radio my-2"
                                        />
                                        <span className="ml-2">{resourceStatus.name}</span>
                                    </label>
                                ))}
                            </div>
                        </div>
                        {/* Billing Status */}
                        <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                            <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                Billing Status
                            </label>
                            <div className="flex flex-col p-4">
                                {billingStatuses.map(billingStatus => (
                                    <label className="inline-flex items-center" key={billingStatus.id}>
                                        <input
                                            type="radio"
                                            name="billingStatus"
                                            value={billingStatus.id}
                                            checked={selectedBillingStatuses.includes(billingStatus.id)}
                                            onChange={() => handleBillingStatusChange(billingStatus.id)}
                                            className="form-radio my-2"
                                        />
                                        <span className="ml-2">{billingStatus.name}</span>
                                    </label>
                                ))}
                            </div>
                        </div>
                        {/* Contact Type */}
                        <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                            <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                Contact Type
                            </label>
                            <div className="flex flex-col p-4">
                                {contactTypes.map(contactType => (
                                    <label className="inline-flex items-center" key={contactType.id}>
                                        <input
                                            type="radio"
                                            name="contactType"
                                            value={contactType.id}
                                            checked={selectedContactTypes.includes(contactType.id)}
                                            onChange={() => handleContactTypeChange(contactType.id)}
                                            className="form-radio my-2"
                                        />
                                        <span className="ml-2">{contactType.name}</span>
                                    </label>
                                ))}
                            </div>
                        </div>
                        
                        {/* Available Status */}
                        <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                            <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                Available Status
                            </label>
                            <div className="flex flex-col p-4">
                                {availableStatuses.map(availableStatus => (
                                    <label className="inline-flex items-center" key={availableStatus.id}>
                                        <input
                                            type="radio"
                                            name="availableStatus"
                                            value={availableStatus.id}
                                            checked={selectedAvailableStatuses.includes(availableStatus.id)}
                                            onChange={() => handleAvailableStatusChange(availableStatus.id)}
                                            className="form-radio my-2"
                                        />
                                        <span className="ml-2">{availableStatus.name}</span>
                                    </label>
                                ))}
                            </div>
                        </div>
                        {/* Member Status */}
                        <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                            <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                Member Status
                            </label>
                            <div className="flex flex-col p-4">
                                {memberStatuses.map(memberStatus => (
                                    <label className="inline-flex items-center" key={memberStatus.id}>
                                        <input
                                            type="radio"
                                            name="memberStatus"
                                            value={memberStatus.id}
                                            checked={selectedMemberStatuses.includes(memberStatus.id)}
                                            onChange={() => handleMemberStatusChange(memberStatus.id)}
                                            className="form-radio my-2"
                                        />
                                        <span className="ml-2">{memberStatus.name}</span>
                                    </label>
                                ))}
                            </div>
                        </div>
                        {/* Branch */}
                        <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                            <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                Branch
                            </label>
                            <div className="flex flex-col p-4">
                                {branches.map(branch => (
                                    <label className="inline-flex items-center" key={branch.id}>
                                        <input
                                            type="radio"
                                            name="branch"
                                            value={branch.id}
                                            checked={selectedBranches.includes(branch.id)}
                                            onChange={() => handleBranchChange(branch.id)}
                                            className="form-radio my-2"
                                        />
                                        <span className="ml-2">{branch.name}</span>
                                    </label>
                                ))}
                            </div>
                        </div>

                        {/* Onsite Status */}
                        <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                            <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                Onsite Status
                            </label>
                            <div className="flex flex-col p-4">
                                {onsiteStatuses.map(onsiteStatus => (
                                    <label className="inline-flex items-center" key={onsiteStatus.id}>
                                        <input
                                            type="radio"
                                            name="onsiteStatus"
                                            value={onsiteStatus.id}
                                            checked={selectedOnsiteStatuses.includes(onsiteStatus.id)}
                                            onChange={() => handleOnsiteStatusChange(onsiteStatus.id)}
                                            className="form-radio my-2"
                                        />
                                        <span className="ml-2">{onsiteStatus.name}</span>
                                    </label>
                                ))}
                            </div>
                        </div>

                        {/* Team Members Regular Information */}
                        <div className='flex flex-wrap gap-4 bg-gray-100 p-4 w-full justify-between text-left rounded-xl'>
                            <h4 className='text-primary font-medium text-left w-full'>Optional users data</h4>
                            {/* First Name */}
                            <div className='w-full md:max-w-[23%] text-left'>
                                <label htmlFor="fname" className="block mb-2 text-sm text-gray-600">First Name</label>
                                <input
                                    type="text"
                                    id="fname"
                                    value={fname || ""}
                                    onChange={(e) => setFname(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    placeholder={fname === '' ? 'Enter first name' : ''}
                                />
                            </div>

                            {/* Last Name */}
                            <div className='w-full md:max-w-[23%] text-left'>
                                <label htmlFor="lname" className="block mb-2 text-sm text-gray-600">Last Name</label>
                                <input
                                    type="text"
                                    id="lname"
                                    value={lname}
                                    onChange={(e) => setLname(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    placeholder={lname === '' ? 'Enter last name' : ''}
                                />
                            </div>

                            {/* Birthday */}
                            <div className='w-full md:max-w-[23%] text-left'>
                                <label htmlFor="birthday" className="block mb-2 text-sm text-gray-600">Birthday</label>
                                <input
                                    type="date"
                                    id="birthday"
                                    value={birthday}
                                    onChange={(e) => setBirthday(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    placeholder={birthday === '' ? 'Select your birthday' : ''}
                                />
                            </div>

                            {/* Celebrate Your Birthday? */}
                            <div className="w-full md:max-w-[23%] text-left">
                                <label htmlFor="birthday_celebration" className="block mb-2 text-sm text-gray-600">Would you like to celebrate your birthday?</label>
                                <select
                                    id="birthday_celebration"
                                    value={birthdayCelebration}
                                    onChange={(e) => setBirthdayCelebration(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                >
                                    <option value="" disabled>Select celebrate birthday?</option>
                                    <option value="Yes">Yes</option>
                                    <option value="No">No</option>
                                </select>
                            </div>

                            {/* Birthday Celebration Date */}
                            <div className='w-full md:max-w-[23%] py-2 text-left'>
                                <label htmlFor="birthday_celebration_date" className="block mb-2">Birthday Celebration Date</label>
                                <input
                                    type="date"
                                    id="birthday_celebration_date"
                                    value={birthdayCelebrationDate}
                                    onChange={(e) => setBirthdayCelebrationDate(e.target.value)}
                                    disabled={birthdayCelebration === "No"}
                                    className={`py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 ${
                                        birthdayCelebration === "No" ? "bg-gray-100 cursor-not-allowed" : ""
                                    }`}
                                />
                            </div>

                            {/* Gender */}
                            <div className="w-full md:max-w-[23%] text-left">
                                <label htmlFor="gender" className="block mb-2 text-sm text-gray-600">Gender</label>
                                <select
                                    id="gender"
                                    value={gender}
                                    onChange={(e) => setGender(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                >
                                    <option value="" disabled>Select Gender</option>
                                    <option value="Male">Male</option>
                                    <option value="Female">Female</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>

                            {/* Marital Status */}
                            <div className="w-full md:max-w-[23%] text-left">
                                <label htmlFor="marital_status" className="block mb-2 text-sm text-gray-600">Marital Status</label>
                                <select
                                    id="marital_status"
                                    value={maritalStatus}
                                    onChange={(e) => setMaritalStatus(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                >
                                    <option value="" disabled>Select Marital Status</option>
                                    <option value="single">Single</option>
                                    <option value="married">Married</option>
                                    <option value="divorced">Divorced</option>
                                </select>
                            </div>

                            {/* Nick Name */}
                            <div className='w-full md:max-w-[23%] text-left'>
                                <label htmlFor="nick_name" className="block mb-2 text-sm text-gray-600">Preferred Nickname</label>
                                <input
                                    type="text"
                                    id="nick_name"
                                    value={nickName}
                                    onChange={(e) => setNickName(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>

                            {/* Contacts */}
                            <div className="w-full md:max-w-[23%] py-2">
                                <label htmlFor="primary_contact" className="block mb-2 text-sm text-gray-600">Primary Phone Number</label>
                                <input
                                    type="text"
                                    id="primary_contact"
                                    value={primaryContact}
                                    onChange={(e) => setPrimaryContact(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    placeholder="Primary Contact"
                                />
                            </div>

                            <div className="w-full md:max-w-[23%] py-2">
                                <label htmlFor="secondary_contact" className="block mb-2 text-sm text-gray-600">Secondary Phone Number</label>
                                <input
                                    type="text"
                                    id="secondary_contact"
                                    value={secondaryContact}
                                    onChange={(e) => setSecondaryContact(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    placeholder="Secondary Contact"
                                />
                            </div>

                            <div className="w-full md:max-w-[23%] py-2">
                                <label htmlFor="emergency_contact" className="block mb-2 text-sm text-gray-600">Emergency Contact Number</label>
                                <input
                                    type="text"
                                    id="emergency_contact"
                                    value={emergencyContact}
                                    onChange={(e) => setEmergencyContact(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    placeholder="Emergency Contact"
                                />
                            </div>

                            <div className="w-full md:max-w-[23%] py-2">
                                <label htmlFor="relation_contact" className="block mb-2 text-sm text-gray-600">Emergency Contact Relationshiop</label>
                                <input
                                    type="text"
                                    id="relation_contact"
                                    value={relationContact}
                                    onChange={(e) => setRelationContact(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    placeholder="Relation Contact"
                                />
                            </div>

                            {/* Blood Group */}
                            <div className="w-full md:max-w-[23%] text-left">
                                <label htmlFor="bloods" className="block mb-2 text-sm text-gray-600">Blood Group</label>
                                <select
                                    id="bloods"
                                    
                                    value={selectedBloods}
                                    onChange={(e) => setSelectedBloods(Array.from(e.target.selectedOptions, option => option.value))}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                >
                                    <option value="" disabled>Select Blood Group(s)</option>
                                    {bloodsGroup.map((blood) => (
                                        <option key={blood.id} value={blood.id}>
                                            {blood.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            {/* Blood Donation */}
                            <div className="w-full md:max-w-[23%] text-left">
                                <label htmlFor="blood_donate" className="block mb-2 text-sm text-gray-600">Would you like to permit blood donation?</label>
                                <select
                                    id="blood_donate"
                                    value={bloodDonate}
                                    onChange={(e) => setBloodDonate(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                >
                                    <option value="" disabled>Select Blood Donation</option>
                                    <option value="Yes">Yes</option>
                                    <option value="No">No</option>
                                </select>
                            </div>

                            {/* Previous Designation */}
                            <div className="w-full md:max-w-[23%] py-2">
                                <label htmlFor="prev_designation" className="block mb-2 text-sm text-gray-600">Previous Designation in the Company</label>
                                <input
                                    type="text"
                                    id="prev_designation"
                                    value={prevDesignation}
                                    onChange={(e) => setPrevDesignation(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>

                            {/* Desk ID */}
                            <div className="w-full md:max-w-[23%] py-2">
                                <label htmlFor="desk_id" className="block mb-2 text-sm text-gray-600">Desk ID</label>
                                <input
                                    type="text"
                                    id="desk_id"
                                    value={deskId}
                                    onChange={(e) => setDeskId(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>

                            {/* Joining Date */}
                            <div className="w-full md:max-w-[23%] py-2">
                                <label htmlFor="joining_date" className="block mb-2 text-sm text-gray-600">Joining Date</label>
                                <input
                                    type="date"
                                    id="joining_date"
                                    value={joiningDate}
                                    onChange={(e) => setJoiningDate(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>

                            {/* Termination Date */}
                            <div className="w-full md:max-w-[23%] py-2">
                                <label htmlFor="termination_date" className="block mb-2 text-sm text-gray-600">Termination Date</label>
                                <input
                                    type="date"
                                    id="termination_date"
                                    value={terminationDate}
                                    onChange={(e) => setTerminationDate(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>

                            {/* Employment End Date */}
                            <div className="w-full md:max-w-[23%] py-2">
                                <label htmlFor="employment_end" className="block mb-2 text-sm text-gray-600">Employment End Date</label>
                                <input
                                    type="date"
                                    id="employment_end"
                                    value={employmentEndDate}
                                    onChange={(e) => setEmploymentEndDate(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>

                            {/* Work Anniversary */}
                            <div className="w-full md:max-w-[23%] py-2">
                                <label htmlFor="work_anniversary" className="block mb-2 text-sm text-gray-600">Work Anniversary</label>
                                <input
                                    type="date"
                                    id="work_anniversary"
                                    value={workAnniversary}
                                    onChange={(e) => setWorkAnniversary(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>
                            
                            {/* About */}
                            <div className='w-full md:max-w-[48%] py-2 text-left'>
                                <label htmlFor="about" className="block mb-2 text-sm text-gray-600">About</label>
                                <textarea
                                    id="about"
                                    value={about}
                                    onChange={(e) => setAbout(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    placeholder={about === '' ? 'Tell us something about yourself' : ''}
                                />
                            </div>

                            {/* Address */}
                            <div className="w-full md:max-w-[48%] py-2">
                                <label htmlFor="present_address" className="block mb-2 text-sm text-gray-600">Present Address</label>
                                <textarea
                                    id="present_address"
                                    value={presentAddress}
                                    onChange={(e) => setPresentAddress(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    placeholder="Present Address"
                                />
                            </div>

                            <div className="w-full md:max-w-[48%] py-2">
                                <label htmlFor="permanent_address" className="block mb-2 text-sm text-gray-600">Permanent Address</label>
                                <textarea
                                    id="permanent_address"
                                    value={permanentAddress}
                                    onChange={(e) => setPermanentAddress(e.target.value)}
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    placeholder="Permanent Address"
                                />
                            </div>

                            {/* Handle Photo Upload */}
                            <div className="w-full md:max-w-[48%] py-2">
                                <label htmlFor="photo" className="block mb-2 text-left">Profile Photo</label>
                                <div className="flex flex-row justify-start items-center gap-4">
                                    {/* Display existing photo if available */}
                                    {(existingPhoto || photo) && (
                                    <div className="w-40 h-40 overflow-hidden bg-gray-200 p-4 rounded-lg">
                                        <img
                                        // Show the new photo preview if available, else fallback to the existing photo
                                        src={photo ? URL.createObjectURL(photo) : `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingPhoto}`}
                                        alt="Profile"
                                        className="w-auto h-auto object-cover"
                                        />
                                    </div>
                                    )}

                                    {/* Custom file input */}
                                    <label htmlFor="photo" className="cursor-pointer flex flex-col items-center justify-center gap-2 rounded-lg bg-gray-200 text-gray-800 w-40 h-40 p-4 hover:bg-green-100">
                                    <span className="material-symbols-rounded text-6xl text-gray-300">photo_camera</span>
                                    <span className="text-sm text-gray-400 text-regular">Upload new Photo</span> {/* Custom text */}
                                    </label>
                                    <input
                                    type="file"
                                    name="photo"
                                    id="photo"
                                    className="hidden" // Hide the default file input button
                                    onChange={handlePhotoChange} // Handle the file input change
                                    />
                                </div>

                                {/* Display error message if file exceeds limits */}
                                {errorMessage && (
                                    <div className="text-red-600 text-sm mt-2 text-left">{errorMessage}</div>
                                )}
                            </div>
                        </div>


                    </div>

                    {error && <div className="text-red-500">{error}</div>}
                    {successMessage && <div className="text-green-500">{successMessage}</div>}

                    <button
                        type="submit"
                        className="bg-primary text-white hover:bg-secondary rounded-md px-4 py-3 my-8 w-auto m-0"
                    >
                        {loading ? 'Updating...' : 'Update Team Member'}
                    </button>
                </form>
            </div>
        </div>
    );
};

export default EditMember;
