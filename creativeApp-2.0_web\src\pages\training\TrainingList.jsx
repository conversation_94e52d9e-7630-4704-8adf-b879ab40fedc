import React, { useEffect, useState } from 'react';
import EditTraining from './EditTraining';
import TableContent from '../../common/table/TableContent';
import { API_URL } from './../../common/fetchData/apiConfig'; 
import useFetchApiData from './../../common/fetchData/useFetchApiData'

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const TrainingList = () => {
    const [trainings, setTrainings] = useState([]);
    const [error, setError] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedTrainingId, setSelectedTrainingId] = useState(null);
    const [users, setUsers] = useState([]);
    const [trainingCategories, setTrainingCategories] = useState([]);
    const [trainingTopics, setTrainingTopics] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);

    const token = localStorage.getItem('token');

    // Fetching time cards using the custom hook
    const { data: usersData } = useFetchApiData(`${API_URL}/users`, token);
    const { data: trainingCategoryData } = useFetchApiData(`${API_URL}training-categories`, token);
    const { data: trainingTopicsData } = useFetchApiData(`${API_URL}training-topics`, token);
    const { data: departmentsData } = useFetchApiData(`${API_URL}departments`, token);
    const { data: teamssData } = useFetchApiData(`${API_URL}/teams`, token);


    const columnNames = [
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Title", key: "title" },
        { label: "Trainer", key: "trainer" },
        { label: "Arrange By", key: "arrange_by" },
        { label: "Category", key: "category_id" },
        { label: "Topic", key: "topic_id" },
        { label: "Date", key: "date" },
        { label: "Time", key: "time" },
        { label: "Duration", key: "duration" },
        { label: "Presentation URL", key: "presentation_url" },
        { label: "Record URL", key: "record_url" },
        { label: "Access Passcode", key: "access_passcode" },
        { label: "Location", key: "location" },
        { label: "Tags", key: "tags" },
        { label: "Evaluation Form", key: "evaluation_form" },
        { label: "Response", key: "response" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];
    

    useEffect(() => {

        if (usersData) {
            setUsers(usersData.users || []);
        }
        if (trainingCategoryData) {
            setTrainingCategories(trainingCategoryData.trainingCategories || []);
        }
        if (trainingTopicsData) {
            setTrainingTopics(trainingTopicsData.trainingTopics || []);
        }
        if (departmentsData) {
            setDepartments(departmentsData.departments || []);
        }
        if (teamssData) {
            setTeams(teamssData.teams || []);
        }

        const fetchTrainings = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}trainings`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                console.log('Trainings data:', data);

                //Map id's with value
                const usersMap = usersData?.reduce((map, user) => {
                    const fname = user.fname || 'User name not found';
                    const lname = user.lname || '';
                    const fullName = `${fname} ${lname}`.trim();
                    map[user.id] = fullName;
                    return map;
                }, {}) || {};

                const trainingCategoryMap = trainingCategories.reduce((map, category) => {
                    map[category.id] = category.name;
                    return map;
                }, {});

                const departmentsMap = departments.reduce((map, department) => {
                    map[department.id] = department.name;
                    return map;
                }, {});

                const teamsMap = teams.reduce((map, team) => {
                    map[team.id] = team.name;
                    return map;
                }, {});
            
                const trainingTopicsMap = trainingTopics.reduce((map, topic) => {
                    map[topic.id] = topic.name;
                    return map;
                }, {});

                setTrainings(data.trainings.map(training => ({
                    department: departmentsMap[training.department] || '',
                    team: teamsMap[training.team] || '',
                    title: training.title,
                    trainer: usersMap[training.trainer] || '',
                    arrange_by: usersMap[training.arrange_by] || '',
                    category_id: trainingCategoryMap[training.category_id] || '',
                    topic_id: trainingTopicsMap[training.topic_id] || '',
                    date: training.date,
                    time: training.time,
                    duration: training.duration,
                    presentation_url: training.presentation_url,
                    record_url: training.record_url,
                    access_passcode: training.access_passcode,
                    location: training.location,
                    tags: training.tags,
                    evaluation_form: training.evaluation_form,
                    response: training.response,
                    created_by: usersMap[training.created_by] || '',
                    updated_by: usersMap[training.updated_by] || '',
                })));
            } catch (error) {
                setError(error.message);
            }
        };

        fetchTrainings();
    }, [usersData, trainingCategoryData, trainingTopicsData, departmentsData, teamssData]);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}training/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete training: ' + response.statusText);
            }

            // Update the trainings list after deletion
            setTrainings(prevTrainings => prevTrainings.filter(training => training.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedTrainingId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={trainings}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedTrainingId}
            />
            {modalVisible && (
                <EditTraining
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    trainingId={selectedTrainingId}
                />
            )}
        </div>
    );
};

export default TrainingList;
