import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';

// Helper function to convert 24-hour time to 12-hour format with AM/PM
const convertTo12HourFormat = (time24) => {
    let [hours, minutes] = time24.split(':');
    hours = parseInt(hours, 10);
    const suffix = hours >= 12 ? 'PM' : 'AM';
    if (hours > 12) hours -= 12;
    if (hours === 0) hours = 12;
    return `${hours}:${minutes} ${suffix}`;
};

// Helper function to convert 12-hour time format to 24-hour
const convertTo24HourFormat = (time12) => {
    const [time, suffix] = time12.split(' ');
    let [hours, minutes] = time.split(':');
    hours = parseInt(hours, 10);
    if (suffix === 'PM' && hours < 12) hours += 12;
    if (suffix === 'AM' && hours === 12) hours = 0;
    return `${hours.toString().padStart(2, '0')}:${minutes}`;
};

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditSchedule = ({ isVisible, setVisible, dataItemsId }) => {
    const [teams, setTeams] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [shiftName, setShiftName] = useState('');
    const [shiftStart, setShiftStart] = useState('');
    const [shiftEnd, setShiftEnd] = useState('');
    const [selectedTeam, setSelectedTeam] = useState('');
    const [selectedDepartment, setSelectedDepartment] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);
    
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    // Fetch Department and the current schedule data based on dataItemsId
    useEffect(() => {
        const fetchDepartments = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');
            try {
                const departmentsResponse = await fetch(`${API_URL}departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!departmentsResponse.ok) {
                    throw new Error('Failed to fetch departments');
                }

                const departmentsData = await departmentsResponse.json();
                setDepartments(departmentsData.departments);
            } catch (error) {
                setError(error.message);
            }
        };

        const fetchTeams = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');
            try {
                const teamsResponse = await fetch(`${API_URL}/teams`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!teamsResponse.ok) {
                    throw new Error('Failed to fetch teams');
                }

                const teamsData = await teamsResponse.json();
                setTeams(teamsData.teams);
            } catch (error) {
                setError(error.message);
            }
        };

        const fetchScheduleData = async () => {
            if (!dataItemsId) return;
        
            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }
        
            try {
                const scheduleResponse = await fetch(`${API_URL}schedules/${dataItemsId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
        
                if (!scheduleResponse.ok) {
                    throw new Error('Failed to fetch schedule data');
                }
        
                const scheduleData = await scheduleResponse.json();
                
                if (scheduleData) {
                    setShiftName(scheduleData.shift_name || '');
                    setShiftStart(scheduleData.shift_start || '');
                    setShiftEnd(scheduleData.shift_end || '');
                    setSelectedDepartment(scheduleData.department_id || '');
                    setSelectedTeam(scheduleData.team_id || '');
                } else {
                    setError('Schedule data is not available.');
                }
                
            } catch (error) {
                setError(`Error fetching schedule data: ${error.message}`);
            }
        };
        

        fetchDepartments();
        fetchTeams();
        fetchScheduleData();
    }, [dataItemsId]);

    const convertTo12HourFormat = (time24) => {
        let [hours, minutes] = time24.split(':');
        hours = parseInt(hours, 10);
        const suffix = hours >= 12 ? 'PM' : 'AM';
        if (hours > 12) hours -= 12;
        if (hours === 0) hours = 12;
        return `${hours}:${minutes} ${suffix}`;
    };
    
    const handleSubmit = async (event) => {
        event.preventDefault();
        setError('');
        setSuccessMessage('');

        const updatedBy = loggedInUser;

        if (!updatedBy) {
            setError('User is not logged in.');
            return;
        }

    
        // Validate inputs
        if (!shiftName || !shiftStart || !shiftEnd || !selectedTeam || !selectedDepartment) {
            setError('Please fill all fields.');
            return;
        }
    
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }
    
            // Convert the 12-hour times to 24-hour format before submitting
            const shiftStartFormatted = convertTo12HourFormat(shiftStart);  // Use AM/PM format
            const shiftEndFormatted = convertTo12HourFormat(shiftEnd);      // Use AM/PM format
    
            // Send the updated schedule data to the backend
            const response = await fetch(`${API_URL}schedules/${dataItemsId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    shift_name: shiftName,
                    shift_start: shiftStartFormatted,  // Send AM/PM format
                    shift_end: shiftEndFormatted,      // Send AM/PM format
                    department_id: selectedDepartment,
                    team_id: selectedTeam,
                    updated_by: updatedBy,
                }),
            });
    
            const result = await response.json();
    
            if (result.status === false) {
                setError(`Error: ${result.message}`);
                if (result.errors) {
                    setError(JSON.stringify(result.errors));
                }
                return;
            }
    
            //setSuccessMessage(`Schedule for "${shiftName}" updated successfully!`);

            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Office schedule updated successfully.',
            });

            setTimeout(() => {
                setVisible(false);
                setSuccessMessage('');
            }, 2000); 

        } catch (error) {
            alertMessage('error');
        }
    };
    

    const handleClose = () => {
        setVisible(false); // Close the modal when user cancels
    };

    return (
        <>
            {isVisible && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative overflow-y-auto h-[80vh] mt-10">
                        <button onClick={handleClose} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                            &times;
                        </button>
                        <h4 className="text-xl font-semibold mb-4 py-4">Edit Schedule</h4>
                        <form onSubmit={handleSubmit} className='text-left'>

                            {/* Department Selection */}
                            <div className="mb-4">
                                <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">Select Team</label>
                                <select
                                    id="department"
                                    value={selectedDepartment}
                                    onChange={(e) => setSelectedDepartment(e.target.value)}
                                    required
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                >
                                    <option value="">Select a Department</option>
                                    {departments.map((department) => (
                                        <option key={department.id} value={department.id}>{department.name}</option>
                                    ))}
                                </select>
                            </div>

                            {/* Team Selection */}
                            <div className="mb-4">
                                <label htmlFor="team" className="block text-sm font-medium text-gray-700 pb-4">Select Team</label>
                                <select
                                    id="team"
                                    value={selectedTeam}
                                    onChange={(e) => setSelectedTeam(e.target.value)}
                                    required
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                >
                                    <option value="">Select a Team</option>
                                    {teams.map((team) => (
                                        <option key={team.id} value={team.id}>{team.name}</option>
                                    ))}
                                </select>
                            </div>
                            {/* Shift Name */}
                            <div className="mb-4">
                                <label htmlFor="shiftName" className="block text-sm font-medium text-gray-700 pb-4">
                                    Shift Name
                                </label>
                                <input
                                    type="text"
                                    id="shiftName"
                                    value={shiftName}
                                    onChange={(e) => setShiftName(e.target.value)}
                                    required
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>

                            {/* Time Selection */}
                            <div className="flex flex-row justify-center gap-4">
                                {/* Shift Start */}
                                <div className="mb-4 w-full sm:w-1/2">
                                    <label htmlFor="start-time" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Start Time</label>
                                    <div className="relative">
                                        <div className="absolute inset-y-0 end-0 top-0 flex items-center pe-3.5 pointer-events-none">
                                            <svg className="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                                <path fillRule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clipRule="evenodd"/>
                                            </svg>
                                        </div>
                                        <input
                                            type="time"
                                            id="shiftStart"
                                            value={shiftStart}
                                            onChange={(e) => setShiftStart(e.target.value)}
                                            required
                                            className="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                        />
                                    </div>
                                </div>

                                {/* Shift End */}
                                <div className="mb-4 w-full sm:w-1/2">
                                    <label htmlFor="end-time" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">End Time</label>
                                    <div className="relative">
                                        <div className="absolute inset-y-0 end-0 top-0 flex items-center pe-3.5 pointer-events-none">
                                            <svg className="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                                <path fillRule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clipRule="evenodd"/>
                                            </svg>
                                        </div>
                                        <input
                                            type="time"
                                            id="shiftEnd"
                                            value={shiftEnd}
                                            onChange={(e) => setShiftEnd(e.target.value)}
                                            required
                                            className="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Error and Success Messages */}
                            <div className="pb-4 text-center">
                                {error && <p className="text-red-500">{error}</p>}
                                {successMessage && <p className="text-green-500">{successMessage}</p>}
                            </div>

                            {/* Submit Button */}
                            <button
                                type="submit"
                                className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600"
                            >
                                Update Schedule
                            </button>
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default EditSchedule;
