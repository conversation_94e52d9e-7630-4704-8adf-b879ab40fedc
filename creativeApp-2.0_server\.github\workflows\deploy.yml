name: Deploy <PERSON><PERSON> (Development)

on:
  push:
    branches:
      - development
  workflow_dispatch:

jobs:
  deploy-laravel:
    runs-on: self-hosted

    env:
      DEPLOY_PATH: /var/www/html/creativeapp-v2/creativeApp-2.0_server

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4


      - name: Information
        run: |
          echo "Current directory:"
          pwd
          echo "Contents:"
          ls -la

       # Step 6: Deploy build to server
      - name: Deploy build to server
        run: |
          echo "Deploying to $DEPLOY_PATH"          
          sudo chown -R $USER:$USER "$DEPLOY_PATH"
          # cp -r build/* "$DEPLOY_PATH"/
          echo "Deployment complete. Contents:"
          ls -la "$DEPLOY_PATH"

      - name: Go to deploy path
        run: |
          # echo "Deploying to `${{ env.DEPLOY_PATH }}"
          cd ${DEPLOY_PATH}
          pwd
          ls -la
      

      - name: Install Composer dependencies
        run: |
          ls -la
          composer install --no-interaction --prefer-dist --optimize-autoloader

      # - name: Run migrations
      #   run: |
      #     cd $DEPLOY_PATH
      #     php artisan migrate --force

      - name: Clear caches
        run: |
          # cd ${DEPLOY_PATH}
          php artisan config:cache
          php artisan route:cache
          php artisan view:cache
          php artisan optimize

      # - name: PM2 restart
      #   run: |
      #     pm2 status
      #     pm2 restart all
      #     pm2 status
      #     pm2 logs
