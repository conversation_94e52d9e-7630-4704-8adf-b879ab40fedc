import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
const API_URL = process.env.REACT_APP_BASE_API_URL;
const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const AddMember = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [users, setUsers] = useState([]);
    const [roles, setRoles] = useState([]);
    const [eid, setEid] = useState('');
    const [email, setEmail] = useState('');
    const [selectedRoles, setSelectedRoles] = useState({});
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    const fetchUsers = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/users`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }

            const data = await response.json();
            setUsers(data);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the roles to select for users
    const fetchRoles = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}roles`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!response.ok) {
                const errorMessage = await response.text(); // Get error message
                throw new Error(`Only Super Admin and Admin are permitted to create new user!`);
            }
    
            const data = await response.json();
     
    
            // Access the roles array from the data object
            const rolesData = data.roles; // Get the roles array
    
            const rolesMap = rolesData.reduce((acc, role) => {
                acc[role.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setRoles(rolesData);
            setSelectedRoles(rolesMap);
        } catch (error) {
            setError(error.message);
        }
    };    

    useEffect(() => {
        fetchUsers();
        fetchRoles(); // Call fetchRoles on component mount
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedEid = eid.trim();
        const trimmedEmail = email.trim();
    
        if (!trimmedEid || !trimmedEmail) {
            setError('EID and Email are required.');
            return;
        }
    
        const eidExists = users.some(user => typeof user.eid === 'string' && user.eid.toLowerCase().trim() === trimmedEid.toLowerCase());
        const emailExists = users.some(user => typeof user.email === 'string' && user.email.toLowerCase().trim() === trimmedEmail.toLowerCase());
    
        if (eidExists || emailExists) {
            let message = 'The ';
            if (eidExists) message += 'EID ';
            if (emailExists) message += (message.endsWith('The ') ? '' : 'or ') + 'Email ';
            message += 'already exists. Please add a new EID and/or Email.';
            setError(message);
            setTimeout(() => setError(''), 3000);
            return;
        }
    
        setError('');

        // Prepare roles array based on selected roles
        const selectedRoleIds = Object.keys(selectedRoles).filter(roleId => selectedRoles[roleId]);

        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}/users`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    eid: trimmedEid,
                    email: trimmedEmail,
                    roles: selectedRoleIds.map(Number), // Send the roles array as numbers
                }),
            });
    
            if (!response.ok) {
                throw new Error('Failed to save user: ' + response.statusText);
            }
    
            const result = await response.json();
            setSuccessMessage(`User with EID "${trimmedEid}" added successfully!`);
            setEid('');
            setEmail('');
            setSelectedRoles({}); // Reset roles
    
            fetchUsers();
        } catch (error) {
            setError(error.message);
        }
    };

    const isModalOpen = location.pathname === '/add-member';

    const handleClose = () => {
        navigate('/team-members');
    };

    return (
        <>
            {isModalOpen && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-xl relative">
                        <button onClick={handleClose} className="absolute top-0 right-2 text-gray-400 hover:text-gray-900 text-4xl">
                            &times;
                        </button>
                        <h4 className="text-xl font-semibold mb-4 py-4">Add New User</h4>
                        <form onSubmit={handleSubmit}>
                            <div className='flex flex-wrap gap-4'>
                                <div className="mb-4 w-full">
                                    <label htmlFor="eid" className="block text-sm font-medium text-gray-700 pb-4">
                                        EID
                                    </label>
                                    <input
                                        type="text"
                                        id="eid"
                                        value={eid}
                                        onChange={(e) => setEid(e.target.value)}
                                        required
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    />
                                </div>
                                <div className="mb-4 w-full">
                                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 pb-4">
                                        Email
                                    </label>
                                    <input
                                        type="email"
                                        id="email"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        required
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    />
                                    {error && <p className="text-red-500 text-sm pt-4">{error}</p>}
                                </div>
                                <div className="mb-4 w-full">
                                    <label className="block text-sm font-medium text-gray-700 pb-4">
                                        Roles
                                    </label>
                                    <div className="flex flex-col w-40 m-auto">
                                        {roles.map(role => (
                                            <label className="inline-flex items-center" key={role.id}>
                                                <input
                                                    type="checkbox"
                                                    checked={selectedRoles[role.id] || false}
                                                    onChange={() => setSelectedRoles(prev => ({ ...prev, [role.id]: !prev[role.id] }))}
                                                    className="form-checkbox"
                                                />
                                                <span className="ml-2">{role.name}</span> {/* Assuming role has a 'name' property */}
                                            </label>
                                        ))}
                                    </div>
                                </div>
                            </div>
                            <div className='py-4'>
                                <button
                                    type="submit"
                                    className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                                >
                                    Add User
                                </button>
                            </div>
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default AddMember;
