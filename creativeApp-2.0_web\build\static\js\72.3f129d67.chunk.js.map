{"version": 3, "file": "static/js/72.3f129d67.chunk.js", "mappings": "oQAGA,MAAMA,EAAUC,kCA2PhB,EAzPmBC,IAA6C,IAA5C,UAAEC,EAAS,WAAEC,EAAU,YAAEC,GAAaH,EACtD,MAAOI,EAAYC,IAAiBC,EAAAA,EAAAA,UAAS,KACtCC,EAAkBC,IAAuBF,EAAAA,EAAAA,UAAS,KAClDG,EAAUC,IAAeJ,EAAAA,EAAAA,UAAS,KAClCK,EAAWC,IAAgBN,EAAAA,EAAAA,UAAS,KACpCO,EAAOC,IAAYR,EAAAA,EAAAA,UAAS,KAC5BS,EAAgBC,IAAqBV,EAAAA,EAAAA,UAAS,KAC9CW,EAAcC,IAAmBZ,EAAAA,EAAAA,UAAS,OAC1Ca,EAASC,IAAcd,EAAAA,EAAAA,WAAS,IAGvCe,EAAAA,EAAAA,YAAU,KACN,MAAMC,EAASC,aAAaC,QAAQ,WAChCF,GACAJ,EAAgBI,EACpB,GACD,KAEHD,EAAAA,EAAAA,YAAU,KACkBI,WACpB,IAAKtB,EAAa,OAElB,MAAMuB,EAAQH,aAAaC,QAAQ,SACnC,GAAKE,EAKL,IACI,MAAMC,QAAiBC,MAAM,GAAG9B,YAAmB,CAC/C+B,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUJ,IAC3B,eAAgB,sBAIxB,IAAKC,EAASI,GACV,MAAM,IAAIC,MAAM,6BAA+BL,EAASM,YAG5D,MAEMC,SAFaP,EAASQ,QAEO,SACnC,IAAKC,MAAMC,QAAQH,GACf,MAAM,IAAIF,MAAM,qCAIpB,MAAMM,EAAaJ,EAAYK,MAAKC,GAAUA,EAAOC,KAAOtC,IAC5D,IAAImC,EAMA,MAAM,IAAIN,MAAM,0CANJ,CAAC,IAADU,EAAAC,EACZtC,EAAciC,EAAWM,MAEzB,MAAMC,GAAwC,QAApBH,EAAAJ,EAAW3B,iBAAS,IAAA+B,GAAK,QAALC,EAApBD,EAAuB,UAAE,IAAAC,OAAL,EAApBA,EAA2BF,KAAM,GAC3DjC,EAAoBqC,EACxB,CAGJ,CAAE,MAAOhC,GACLC,EAASD,EAAMiC,QACnB,MApCIhC,EAAS,iCAoCb,EA0BJiC,GAvBuBtB,WACnB,MAAMC,EAAQH,aAAaC,QAAQ,SAEnC,IACI,MAAMG,QAAiBC,MAAM,GAAG9B,aAAoB,CAChD+B,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUJ,IAC3B,eAAgB,sBAIxB,IAAKC,EAASI,GACV,MAAM,IAAIC,MAAM,6BAGpB,MAAMgB,QAAarB,EAASQ,OAC5BvB,EAAaoC,EAAKrC,WAAa,GACnC,CAAE,MAAOE,GACLC,EAASD,EAAMiC,QACnB,GAIJG,EAAgB,GACjB,CAAC9C,IA0FJ,OAAKF,GAGDiD,EAAAA,EAAAA,KAAA,OACIC,UAAU,sGACVC,QAASA,IAAMlD,GAAW,GAAOmD,UAEjCC,EAAAA,EAAAA,MAAA,OACIH,UAAU,yDACVC,QAAUG,GAAMA,EAAEC,kBAAmBH,SAAA,EAErCC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,yDAAwDE,SAAA,EACnEH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gDAA+CE,SAAC,mBAC9DH,EAAAA,EAAAA,KAAA,UACIC,UAAU,6CACVC,QAASA,IAAMlD,GAAW,GAAOmD,SACpC,YAIJxC,IAASqC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcE,SAAExC,IACxCE,IAAkBmC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBE,SAAEtC,KACpDuC,EAAAA,EAAAA,MAAA,QAAMG,SA9GGhC,UACjBiC,EAAMC,iBACN,MAAMC,EAAoBxD,EAAWyD,OAE/BC,EAAY7C,EAElB,GAAK6C,EAAL,CAKA,GAAI1B,MAAMC,QAAQ5B,GAAW,CAMzB,GALqBA,EAASsD,MAAKvB,GACPA,EAAOI,KAAKoB,cAAcH,SACvBD,EAAkBI,gBAG/B,CACdlD,EAAS,yDACT,MAAMmD,EAAYC,YAAW,IAAMpD,EAAS,KAAK,KACjD,MAAO,IAAMqD,aAAaF,EAC9B,CACJ,CAEAnD,EAAS,IAET,IACI,MAAMY,EAAQH,aAAaC,QAAQ,SAE7BG,QAAiBC,MAAM,GAAG9B,aAAmBK,IAAe,CAC9D0B,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUJ,IAC3B,eAAgB,oBAEpB0C,KAAMC,KAAKC,UAAU,CACjB1B,KAAMgB,EACNW,YAAahE,EACbiE,WAAYV,MAIpB,IAAKnC,EAASI,GACV,MAAM,IAAIC,MAAM,4BAA8BL,EAASM,YAG3D,MAAMwC,QAAe9C,EAASQ,OAEJsC,EAAOjC,OAAOI,MAIxC8B,EAAAA,EAAAA,IAAa,CACTC,KAAM,UACNC,MAAO,WACPC,MAAY,OAANJ,QAAM,IAANA,OAAM,EAANA,EAAQ3B,UAAW,wCAG7BzC,EAAc,IACdG,EAAoB,IAGpB,MAAMsE,QAA4BlD,MAAM,GAAG9B,YAAmB,CAC1D+B,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUJ,IAC3B,eAAgB,sBAIxB,IAAKoD,EAAoB/C,GACrB,MAAM,IAAIC,MAAM,6BAA+B8C,EAAoB7C,YAGvE,MAAM8C,QAAwBD,EAAoB3C,OAClDzB,EAAYqE,EAA0B,UAAK,IAG3Cb,YAAW,KACPhE,GAAW,GACXc,EAAkB,GAAG,GACtB,IAEP,CAAE,MAAOH,IACL6D,EAAAA,EAAAA,IAAa,QACjB,CA5EA,MAFI5D,EAAS,yBA8Eb,EAyBsCqC,UAAU,MAAKE,SAAA,EACzCC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,OAAME,SAAA,EACjBH,EAAAA,EAAAA,KAAA,SAAO8B,QAAQ,OAAO7B,UAAU,aAAYE,SAAC,iBAC7CH,EAAAA,EAAAA,KAAA,SACI+B,KAAK,OACLxC,GAAG,OACHyC,MAAO9E,EACP+E,SAAW5B,GAAMlD,EAAckD,EAAE6B,OAAOF,OACxC/B,UAAU,4BACVkC,UAAQ,QAIhB/B,EAAAA,EAAAA,MAAA,OAAKH,UAAU,OAAME,SAAA,EACjBH,EAAAA,EAAAA,KAAA,SAAO8B,QAAQ,WAAW7B,UAAU,aAAYE,SAAC,qBACjDC,EAAAA,EAAAA,MAAA,UACIb,GAAG,WACHyC,MAAO3E,EACP4E,SAAW5B,GAAM/C,EAAoB+C,EAAE6B,OAAOF,OAC9C/B,UAAU,4BACVkC,UAAQ,EAAAhC,SAAA,EAERH,EAAAA,EAAAA,KAAA,UAAQgC,MAAM,GAAE7B,SAAC,sBACK,IAArB1C,EAAU2E,QACPpC,EAAAA,EAAAA,KAAA,UAAQqC,UAAQ,EAAAlC,SAAC,2BAEjB1C,EAAU6E,KAAKC,IACXvC,EAAAA,EAAAA,KAAA,UAA0BgC,MAAOO,EAAShD,GAAGY,SACxCoC,EAASC,gBADDD,EAAShD,aAOtCS,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeE,UACtBC,EAAAA,EAAAA,MAAA,UACI2B,KAAK,SACL9B,UAAU,8HAA6HE,SAAA,EAEvIH,EAAAA,EAAAA,KAAA,QAAMyC,MAAM,2DAA0DtC,SAAC,eACtElC,EAAU,cAAgB,6BA9DhC,IAmEb,E,qCCjOd,MAAMyE,EAAc,SAqhBpB,EAlhBuBC,KAErB,MAAOC,EAAeC,IAAoBzF,EAAAA,EAAAA,UAAS,CAAC,IAC7C0F,EAAuBC,IAA4B3F,EAAAA,EAAAA,UAAS,CAAC,IAC7D4F,EAAkBC,IAAuB7F,EAAAA,EAAAA,UAAS,KAClD8F,EAAaC,IAAkB/F,EAAAA,EAAAA,UAAS,KACxCgG,EAAcC,IAAmBjG,EAAAA,EAAAA,WAAS,IAC1CkG,EAAqBC,IAA0BnG,EAAAA,EAAAA,WAAS,IACxDH,EAAauG,IAAkBpG,EAAAA,EAAAA,UAAS,OACxCO,EAAOC,IAAYR,EAAAA,EAAAA,UAAS,OAC5BqG,EAAUC,IAAetG,EAAAA,EAAAA,UAAS,OAElCuG,EAAiBC,KADPC,EAAAA,EAAAA,OAC6BzG,EAAAA,EAAAA,WAAS,KAIhD0G,EAAYC,IAAiB3G,EAAAA,EAAAA,UAAS,eACtC4G,EAAeC,IAAoB7G,EAAAA,EAAAA,UAAS,SAC5C8G,EAASC,IAAc/G,EAAAA,EAAAA,UAAS,OAChCgH,EAAaC,IAAkBjH,EAAAA,EAAAA,UAAS,IAGvC0C,KAAMwE,EAAS,WAAEC,EAAY5G,MAAO6G,IAAeC,EAAAA,EAAAA,KAAsB,CAAEC,QAASZ,EAAYa,MAAOX,EAAeY,KAAMR,EAAaS,SAAUX,EAASY,MAAO5B,KAEpK6B,GAAwBjF,KAAMkF,EAAWrH,MAAOsH,KAAoBC,EAAAA,EAAAA,QAEpEC,IAAgBC,EAAAA,EAAAA,OAGjBC,EAAoBC,IACxB,IAAIC,EAAIC,OAAOC,QAAQH,GAAiBI,QAAO,CAACC,EAAG7I,KAAoB,IAAjB8I,EAAK5D,GAAMlF,EAC/D,GAAqB,kBAAVkF,EACT,OAAO2D,EAAM,IAAIC,KAAO5D,IAE1B,GAAI9C,MAAMC,QAAQ6C,GAAQ,CAExB,OAAO2D,EAAM,IAAIC,KADJ5D,EAAMM,KAAKuD,GAAMA,EAAE7D,QAAO8D,KAAK,MAE9C,CACA,OAAOH,CAAG,GACT,IAEHxC,EAAeoC,EAAE,EAGbQ,EAAcjG,KAEEkG,EAAAA,EAAAA,IAAWlG,EADV,CAAC,KAAM,OAAQ,aAAc,aAAc,aAAc,UAAW,aAAc,UAAW,aAAc,eAEhI4D,EAAY,MACZL,GAAgB,EAAK,EAGjB4C,EAAc1G,IAClBmE,EAAY,MACZF,EAAejE,GACf8D,GAAgB,EAAK,EAGjB6C,GAAgB3G,KACpB4G,EAAAA,EAAAA,IAAkB,CAACC,UAAWA,KAE1BjB,EAAa5F,GACbmE,EAAY,KAAK,GAChB,EAIP,IAAI2C,GAAe,EAEnB,MAAM,gBAAEC,KAAoBC,EAAAA,EAAAA,MAGrBC,GAASC,KAAcrJ,EAAAA,EAAAA,WAAS,IAAM,CAC3C,CACImC,GAAI8G,KACN3G,KAAM,SACNgH,MAAO,QACPzG,UAAW,aACX0G,KAAOC,IACLxG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,sCAAqCE,SAAA,EAElDH,EAAAA,EAAAA,KAAA,UACEC,UAAU,wLACVC,QAASA,IAAMwD,EAAYkD,GAAMzG,UAEjCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,kBAItC,OAAfmG,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAChB7G,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAM+F,EAAWW,EAAKrH,IAAIY,UAEnCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,mBAKxC,OAAfmG,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAChB7G,EAAAA,EAAAA,KAAA,UACEC,UAAU,sLACVC,QAASA,IAAM6F,EAAWa,GAAMzG,UAEhCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,oBAKxC,OAAfmG,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAChB7G,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAMgG,GAAaU,EAAKrH,IAAIY,UAErCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,iBAM9D,CACIZ,GAAI8G,KACN3G,KAAM,OACNoH,SAAUA,CAACC,EAAKC,KAAW5C,EAAc,GAAKF,EAAU8C,EAAQ,EAChEN,MAAO,OACPO,MAAM,GAER,CACI1H,GAAI8G,KACJ3G,KAAM,WACNwH,SAAU,YACVJ,SAAWC,GACTA,EAAItJ,WAAayB,MAAMC,QAAQ4H,EAAItJ,WAC/BsJ,EAAItJ,UAAU6E,KAAI6E,GAAOA,EAAI3E,iBAAgBsD,KAAK,MAClD,MACNmB,MAAM,EACNG,UAAU,EACVC,YAAY,GAEhB,CACI9H,GAAI8G,KACN3G,KAAM,cACNwH,SAAU,OACVJ,SAAWC,GAAQA,EAAIrH,MAAQ,GAC/BuH,MAAM,EACNG,UAAU,EACVC,YAAY,GAEd,CACI9H,GAAI8G,KACJ3G,KAAM,aACNoH,SAAWC,IAAG,IAAAO,EAAAC,EAAA,MAAK,IAAc,QAAXD,EAAAP,EAAIS,eAAO,IAAAF,OAAA,EAAXA,EAAaG,QAAS,OAAiB,QAAXF,EAAAR,EAAIS,eAAO,IAAAD,OAAA,EAAXA,EAAaG,QAAS,IAAI,EAC5ER,SAAU,aACVD,MAAM,EACNG,UAAU,EACVC,YAAY,GAEhB,CACE9H,GAAI8G,KACJ3G,KAAM,eACNoH,SAAWC,IAAQY,EAAAA,EAAAA,IAAkBZ,EAAIa,YACzCV,SAAU,aACVD,MAAM,EACNG,UAAU,EACVC,YAAY,GAEd,CACI9H,GAAI8G,KACJ3G,KAAM,eACNoH,SAAWC,IAAQc,EAAAA,EAAAA,IAAmBd,EAAIa,YAC1CV,SAAU,aACVD,MAAM,EACNG,UAAU,EACVC,YAAY,GAEd,CACE9H,GAAI8G,KACJ3G,KAAM,aACNoH,SAAWC,IAAG,IAAAe,EAAAC,EAAA,MAAK,IAAc,QAAXD,EAAAf,EAAIiB,eAAO,IAAAF,OAAA,EAAXA,EAAaL,QAAS,OAAiB,QAAXM,EAAAhB,EAAIiB,eAAO,IAAAD,OAAA,EAAXA,EAAaL,QAAS,IAAI,EAC5ER,SAAU,aACVD,MAAM,EACNG,UAAU,EACVC,YAAY,GAEhB,CACE9H,GAAI8G,KACJ3G,KAAM,eACNoH,SAAWC,IAAQY,EAAAA,EAAAA,IAAkBZ,EAAIkB,YACzCf,SAAU,aACVD,MAAM,EACNG,UAAU,EACVC,YAAY,GAEd,CACI9H,GAAI8G,KACJ3G,KAAM,eACNoH,SAAWC,IAAQc,EAAAA,EAAAA,IAAmBd,EAAIkB,YAC1Cf,SAAU,aACVD,MAAM,EACNG,UAAU,EACVC,YAAY,OAIlBlJ,EAAAA,EAAAA,YAAU,KAERsI,IAAYyB,GAAgB,IACvBA,EAAY5F,KAAK6F,GACD,WAAbA,EAAIzI,KAEC,IACFyI,EACHxB,KAAOC,IACLxG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,sCAAqCE,SAAA,EAClDH,EAAAA,EAAAA,KAAA,UACEC,UAAU,wLACVC,QAASA,IAAMwD,EAAYkD,GAAMzG,UAEjCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,kBAEtC,OAAfmG,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAChB7G,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAM+F,EAAWW,EAAKrH,IAAIY,UAEnCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,mBAIxC,OAAfmG,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAClB7G,EAAAA,EAAAA,KAAA,UACEC,UAAU,sLACVC,QAASA,IAAM6F,EAAWa,GAAMzG,UAEhCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,oBAItC,OAAfmG,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAClB7G,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAMgG,GAAaU,EAAKrH,IAAIY,UAErCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,iBAOvDgI,MAET,GACD,CAAC7B,KAKJ,MAkBM8B,IAAWC,EAAAA,EAAAA,MAqDXC,IAA8BC,EAAAA,EAAAA,cAClChK,iBAKM,IAJJiK,EAAUC,UAAArG,OAAA,QAAAsG,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EACd1G,EAAI0G,UAAArG,OAAA,QAAAsG,IAAAD,UAAA,GAAAA,UAAA,GAAG,QACPE,EAASF,UAAArG,OAAA,QAAAsG,IAAAD,UAAA,GAAAA,UAAA,GAAG,GACZG,EAASH,UAAArG,OAAA,QAAAsG,IAAAD,UAAA,GAAAA,UAAA,GAAG,SAGRI,EAAeL,EAAWtB,UAAY,QAE1C,IACEjE,EAAoB4F,GACpBtF,GAAuB,GAEvB,IAAIyB,EAAY,GAEhB,MAAMvG,QAAiBsG,EAAqB,CAAEhD,KAAMA,EAAKpB,OAAQmI,OAAQD,EAAalI,OAAQgB,KAAMgH,EAAUhI,SAM9G,GAJIlC,EAASqB,OACXkF,EAAYvG,EAASqB,MAGnBkF,EAAU5C,OAAQ,CAEpB,GAAkB,eAAdwG,EAMF,OALA/F,GAAkBkG,IAAI,IACjBA,EACH,CAACF,GAAe7D,MAGXA,EAGT,MAAMgE,EAAmBhE,EACtB1C,KAAKsE,IACJ,GAAG4B,EAAW1B,SAAS,CACrB,IAAImC,EAAQT,EAAW1B,SAASF,GAEhC,OAAGqC,GACGrC,EAAKsC,OAAStC,EAAKsC,MAAQ,IAC7BD,GAAS,KAAKrC,EAAKsC,UAGd,CAAED,QAAOjH,MAAO4E,EAAKiC,KAGzB,IACP,KACCM,OAAOC,SAOZ,OALAvG,GAAkBkG,IAAI,IACjBA,EACH,CAACP,EAAWjJ,KAAK8J,EAAAA,EAAAA,IAAYL,OAGxBA,CACT,CACF,CAAE,MAAOrL,GACPC,EAASD,EAAMiC,QACjB,CAAC,QACC2D,GAAuB,EACzB,CACF,GACA,IAGF,OACEvD,EAAAA,EAAAA,KAAA,WAASC,UAAU,gEAA+DE,UAChFC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,gBAAeE,SAAA,EAE5BC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,iGAAgGE,SAAA,EAC7GH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BE,UAC3CH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sBAAqBE,SAAEuC,OAEvCtC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,0CAAyCE,SAAA,EAEtDH,EAAAA,EAAAA,KAACsJ,EAAAA,GAAa,CAAC9C,QAASA,GAASC,WAAYA,MAG1ClC,GAAcD,GAAaiF,SAASjF,EAAU4E,OAAS,IACxDlJ,EAAAA,EAAAA,KAAAwJ,EAAAA,SAAA,CAAArJ,UACEC,EAAAA,EAAAA,MAAA,UACEH,UAAU,oZACVC,QAvIM3B,UACpB,IAEE,MAAMgD,QAAe6G,GACnBqB,EAAAA,IAAUC,UAAUC,cAAcC,SAAS,CACzClF,QAASZ,EACTa,MAAOX,EACPY,KAAMR,EACNS,UAAmB,OAATP,QAAS,IAATA,OAAS,EAATA,EAAW4E,QAAS,GAC9BpE,MAAO5B,KAET2G,SAEF,GAAW,OAANtI,QAAM,IAANA,IAAAA,EAAQ2H,OAAS3H,EAAO2H,MAAQ,EACnC,OAAO,EAGT,IAAIY,EAAK,EAET,IAAIC,EAAcxI,EAAOzB,KAAKwC,KAAKsE,IACjC,GAAIJ,GAAQpE,OAAQ,CAClB,IAAI4H,EAAM,CAAC,EAMX,OALAxD,GAAQyD,SAASnB,KACVA,EAAO7B,MAAQ6B,EAAOhC,WACzBkD,EAAIlB,EAAOpJ,MAAwB,SAAhBoJ,EAAOpJ,KAAkBoK,IAAOhB,EAAOhC,SAASF,IAAS,GAC9E,IAEKoD,CACT,KAIF,MAAME,EAAYC,EAAAA,GAAWC,cAAcL,GACrCM,EAAWF,EAAAA,GAAWG,WAC5BH,EAAAA,GAAWI,kBAAkBF,EAAUH,EAAW,UAGlD,MAAMM,EAAcL,EAAAA,GAAWE,EAAU,CACvCI,SAAU,OACV1I,KAAM,UAEF2I,EAAO,IAAIC,KAAK,CAACH,GAAc,CAAEzI,KAAM,8BAC7C6I,EAAAA,EAAAA,QAAOF,EAAM,GAAGhI,EAAYmI,QAAQ,KAAK,QAAQd,EAAY3H,cAC/D,CAAE,MAAOzE,GACPmN,QAAQnN,MAAM,4BAA6BA,EAC7C,GA0FqCwC,SAAA,CAEtBoE,IACCvE,EAAAA,EAAAA,KAAAwJ,EAAAA,SAAA,CAAArJ,UACEH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sDAAqDE,SAAC,yBAKxEoE,IACAvE,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yCAAwCE,SAAC,gBAGzD,oBACgBmE,EAAU4E,MAAM,SAKvC5C,GAAgBO,iBACf7G,EAAAA,EAAAA,KAAA,UACEC,UAAU,gYAEVC,QAASA,IAAM0D,GAAmB,GAAMzD,SACzC,mBAQPH,EAAAA,EAAAA,KAAC+K,EAAAA,GAAY,CACTvE,QAASA,GACT1D,sBAAuBA,EACvBC,yBAA0BA,EAC1BuF,4BAA6BA,GAC7B1F,cAAeA,EACfU,oBAAqBA,EACrBN,iBAAkBA,EAClBgI,UAlMQA,KAChB,GAAIxF,OAAOyF,KAAKnI,GAAuBV,OAAQ,CAC7C,IAAI8I,EAAS,CAAC,EACd1F,OAAOyF,KAAKnI,GAAuBR,KAAKsD,IACI,kBAA/B9C,EAAsB8C,GAC/BsF,EAAOtF,GAAO,GAEdsF,EAAOtF,GAAO,EAChB,IAEF7C,EAAyB,IAAKmI,IAC9B7F,EAAiB,IAAK6F,GACxB,CACA7G,EAAe,EAAE,EAsLTA,eAAgBA,EAChBgB,iBAAkBA,IAIrBb,IAAcxE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcE,SAAExC,IAE7C4G,IAAcvE,EAAAA,EAAAA,KAACmL,EAAAA,EAAO,KAKvBnL,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mDAAkDE,UAC/DH,EAAAA,EAAAA,KAACoL,EAAAA,GAAS,CACR5E,QAASA,GACT1G,MAAe,OAATwE,QAAS,IAATA,OAAS,EAATA,EAAWxE,OAAQ,GACzBG,UAAU,8BACVoL,aAAW,EAEXC,kBAAgB,EAChBC,YAAU,EACVC,YAAU,EACVC,kBAAgB,EAChBC,kBAAmBxH,EACnByH,qBAA8B,OAATrH,QAAS,IAATA,OAAS,EAATA,EAAW4E,QAAS,EACzC0C,aAAehH,IACTA,IAASR,GACXC,EAAeO,EACjB,EAEFiH,oBAAsBC,IACjBA,IAAe5H,IAChBC,EAAW2H,GACXzH,EAAe,GACjB,EAEF0H,2BAA4B,CAC1BC,mBAAmB,EACnBC,sBAAuB,OAEzBC,YAAU,EACVC,OAAQ,SAACrD,GAAkC,IAA1B9E,EAAayE,UAAArG,OAAA,QAAAsG,IAAAD,UAAA,GAAAA,UAAA,GAAC,OAC1BjD,OAAOyF,KAAKnC,GAAQ1G,SACrB2B,EAAc+E,EAAO5B,UAAY4B,EAAOpJ,MAAQ,cAChDuE,EAAiBD,GAAiB,QAEtC,MAKHL,IACG3D,EAAAA,EAAAA,KAACoM,EAAAA,EAAS,CACNrP,UAAW4G,EACX3G,WAAY4G,IAKnBR,IACCpD,EAAAA,EAAAA,KAACqM,EAAU,CACTtP,UAAWqG,EACXpG,WAAYqG,EACZpG,YAAaA,IAIhBwG,IAECzD,EAAAA,EAAAA,KAACsM,EAAAA,GAAS,CAAC1F,KAAMnD,EAAUC,YAAaA,EAAa8C,QAASA,GAASP,WAAYA,EAAYC,aAAcA,SAIzG,EC5hBd,EATeqG,KAEXvM,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCE,UACrDH,EAAAA,EAAAA,KAAC2C,EAAc,K", "sources": ["pages/branch/EditBranch.jsx", "pages/branch/BranchDataList.jsx", "dashboard/Branch.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { alertMessage } from '../../common/coreui';\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst EditBranch = ({ isVisible, setVisible, dataItemsId }) => {\r\n    const [branchName, setBranchName] = useState('');\r\n    const [selectedLocation, setSelectedLocation] = useState('');\r\n    const [branches, setBranches] = useState([]);\r\n    const [locations, setLocations] = useState([]);\r\n    const [error, setError] = useState('');\r\n    const [successMessage, setSuccessMessage] = useState('');\r\n    const [loggedInUser, setLoggedInUser] = useState(null);\r\n    const [loading, setLoading] = useState(false);\r\n\r\n    // Fetch logged-in user data (user_id)\r\n    useEffect(() => {\r\n        const userId = localStorage.getItem('user_id');\r\n        if (userId) {\r\n            setLoggedInUser(userId);\r\n        }\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        const fetchBranchName = async () => {\r\n            if (!dataItemsId) return;\r\n    \r\n            const token = localStorage.getItem('token');\r\n            if (!token) {\r\n                setError('No authentication token found.');\r\n                return;\r\n            }\r\n    \r\n            try {\r\n                const response = await fetch(`${API_URL}branches`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n    \r\n                if (!response.ok) {\r\n                    throw new Error('Failed to fetch branches: ' + response.statusText);\r\n                }\r\n    \r\n                const data = await response.json();\r\n    \r\n                const branchArray = data['branches']; // Adjusted to use 'branches' data\r\n                if (!Array.isArray(branchArray)) {\r\n                    throw new Error('Expected branches to be an array.');\r\n                }\r\n    \r\n                // Find the branch by ID\r\n                const branchData = branchArray.find(branch => branch.id === dataItemsId);\r\n                if (branchData) {\r\n                    setBranchName(branchData.name);\r\n                    // Assuming 'locations' is an array of related location objects\r\n                    const defaultLocationId = branchData.locations?.[0]?.id || '';\r\n                    setSelectedLocation(defaultLocationId);\r\n                }else {\r\n                    throw new Error('Branch not found. Please check the ID.');\r\n                }\r\n            } catch (error) {\r\n                setError(error.message);\r\n            }\r\n        };\r\n\r\n        const fetchLocations = async () => {\r\n            const token = localStorage.getItem('token');\r\n    \r\n            try {\r\n                const response = await fetch(`${API_URL}locations`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n    \r\n                if (!response.ok) {\r\n                    throw new Error('Failed to fetch locations');\r\n                }\r\n    \r\n                const data = await response.json();\r\n                setLocations(data.locations || []); // Adjust based on actual response structure\r\n            } catch (error) {\r\n                setError(error.message);\r\n            }\r\n        };\r\n\r\n        fetchBranchName();\r\n        fetchLocations();\r\n    }, [dataItemsId]);\r\n\r\n    const handleSubmit = async (event) => {\r\n        event.preventDefault();\r\n        const trimmedBranchName = branchName.trim();\r\n\r\n        const updatedBy = loggedInUser;\r\n\r\n        if (!updatedBy) {\r\n            setError('User is not logged in.');\r\n            return;\r\n        }\r\n    \r\n        if (Array.isArray(branches)) {\r\n            const branchExists = branches.some(branch => {\r\n                const branchNameLower = branch.name.toLowerCase().trim();\r\n                return branchNameLower === trimmedBranchName.toLowerCase();\r\n            });\r\n    \r\n            if (branchExists) {\r\n                setError('Branch already exists. Please add a different branch.');\r\n                const timeoutId = setTimeout(() => setError(''), 3000);\r\n                return () => clearTimeout(timeoutId);\r\n            }\r\n        }\r\n    \r\n        setError('');\r\n    \r\n        try {\r\n            const token = localStorage.getItem('token');\r\n    \r\n            const response = await fetch(`${API_URL}branches/${dataItemsId}`, { // Update branch endpoint\r\n                method: 'PUT',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify({\r\n                    name: trimmedBranchName,\r\n                    location_id: selectedLocation, // Send the selected location ID\r\n                    updated_by: updatedBy,\r\n                }),\r\n            });\r\n    \r\n            if (!response.ok) {\r\n                throw new Error('Failed to update branch: ' + response.statusText);\r\n            }\r\n    \r\n            const result = await response.json();\r\n    \r\n            const updatedBranchName = result.branch.name; // Adjust to use the 'branch' object\r\n    \r\n            //setSuccessMessage(`Branch \"${updatedBranchName}\" updated successfully!`);\r\n\r\n            alertMessage({\r\n                icon: 'success',\r\n                title: 'Success!',\r\n                text: result?.message || 'Office branch updated successfully.',\r\n            });\r\n\r\n            setBranchName('');\r\n            setSelectedLocation(''); // Reset selected location\r\n    \r\n            // Optionally, refetch branches\r\n            const newBranchesResponse = await fetch(`${API_URL}branches`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n    \r\n            if (!newBranchesResponse.ok) {\r\n                throw new Error('Failed to fetch branches: ' + newBranchesResponse.statusText);\r\n            }\r\n    \r\n            const newBranchesData = await newBranchesResponse.json();\r\n            setBranches(newBranchesData['branches'] || []);\r\n    \r\n            // Close the modal after a short delay\r\n            setTimeout(() => {\r\n                setVisible(false);\r\n                setSuccessMessage(''); // Clear the success message\r\n            }, 1000);\r\n            \r\n        } catch (error) {\r\n            alertMessage('error');\r\n        }\r\n    };\r\n\r\n    if (!isVisible) return null;\r\n\r\n    return (\r\n        <div\r\n            className=\"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50\"\r\n            onClick={() => setVisible(false)}\r\n        >\r\n            <div\r\n                className=\"relative bg-white rounded-lg shadow-lg max-w-md w-full\"\r\n                onClick={(e) => e.stopPropagation()} // Prevent click from closing the modal\r\n            >\r\n                <div className=\"flex justify-between items-center mb-4 bg-gray-100 p-4\">\r\n                    <h3 className=\"text-base text-left font-medium text-gray-800\">Update Branch</h3>\r\n                    <button\r\n                        className=\"text-2xl text-gray-500 hover:text-gray-800\"\r\n                        onClick={() => setVisible(false)}\r\n                    >\r\n                        &times;\r\n                    </button>\r\n                </div>\r\n                {error && <div className=\"text-red-500\">{error}</div>}\r\n                {successMessage && <div className=\"text-green-500\">{successMessage}</div>}\r\n                <form onSubmit={handleSubmit} className='p-6'>\r\n                    <div className=\"mb-4\">\r\n                        <label htmlFor=\"name\" className=\"block mb-2\">Branch Name</label>\r\n                        <input\r\n                            type=\"text\"\r\n                            id=\"name\"\r\n                            value={branchName}\r\n                            onChange={(e) => setBranchName(e.target.value)}\r\n                            className=\"border rounded w-full p-2\"\r\n                            required\r\n                        />\r\n                    </div>\r\n\r\n                    <div className=\"mb-4\">\r\n                        <label htmlFor=\"location\" className=\"block mb-2\">Select Location</label>\r\n                        <select\r\n                            id=\"location\"\r\n                            value={selectedLocation}\r\n                            onChange={(e) => setSelectedLocation(e.target.value)}\r\n                            className=\"border rounded w-full p-2\"\r\n                            required\r\n                        >\r\n                            <option value=\"\">Select a Location</option>\r\n                            {locations.length === 0 ? (\r\n                                <option disabled>No locations available</option>\r\n                            ) : (\r\n                                locations.map((location) => (\r\n                                    <option key={location.id} value={location.id}>\r\n                                        {location.locations_name}\r\n                                    </option>\r\n                                ))\r\n                            )}\r\n                        </select>\r\n                    </div>\r\n                    <div className='text-left p-6'>\r\n                            <button\r\n                                type=\"submit\"\r\n                                className=\"w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4\"\r\n                            >\r\n                                <span class=\"material-symbols-rounded text-white text-xl font-regular\">add_circle</span>\r\n                                {loading ? 'Updating...' : 'Update Branch'}\r\n                            </button>\r\n                        </div>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default EditBranch;\r\n", "import React, { useState, useCallback, useEffect } from \"react\";\r\n\r\n// DataTable component for rendering tabular data with features like pagination and sorting\r\nimport DataTable from \"react-data-table-component\";\r\n\r\n// Loading spinner component to show while data is loading\r\nimport Loading from \"./../../common/Loading\";\r\n\r\nimport {confirmation<PERSON><PERSON>t, ManageColumns, SearchFilter, TableView} from './../../common/coreui';\r\n\r\n\r\nimport { useDispatch } from \"react-redux\";\r\nimport { defaultDateTimeFormat, removeKeys, sortByLabel } from \"./../../utils\";\r\n\r\n// Libraries for exporting data to Excel\r\nimport { saveAs } from \"file-saver\";\r\nimport * as XLSX from \"xlsx\";\r\nimport { branchApi, useDeleteBranchMutation, useGetBranchDataQuery, useLazyFetchDataOptionsForBranchQuery } from \"./../../features/api\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport EditBranch from \"./EditBranch\";\r\nimport AddBranch from \"./AddBranch\";\r\nimport { useRoleBasedAccess } from \"./../../common/useRoleBasedAccess\";\r\nimport { DateTimeFormatDay, DateTimeFormatHour } from \"../../common/DateTimeFormatTable\";\r\n\r\n// API endpoint and configuration constants\r\nconst MODULE_NAME = \"Branch\";\r\n\r\n// Main component for listing Product Type List\r\nconst BranchDataList = () => {\r\n  // State variables for data items, filters, search text, modals, and loading status\r\n  const [filterOptions, setFilterOptions] = useState({});\r\n  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});\r\n  const [showFilterOption, setShowFilterOption] = useState(\"\");\r\n  const [queryString, setQueryString] = useState(\"\");\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [filterOptionLoading, setFilterOptionLoading] = useState(false);\r\n  const [dataItemsId, setDataItemsId] = useState(null);\r\n  const [error, setError] = useState(null);\r\n  const [viewData, setViewData] = useState(null);\r\n  const navigate = useNavigate();\r\n  const [addModalVisible, setAddModalVisible] = useState(false);\r\n\r\n  \r\n  // Sorting and pagination state\r\n  const [sortColumn, setSortColumn] = useState(\"created_at\");\r\n  const [sortDirection, setSortDirection] = useState(\"desc\");\r\n  const [perPage, setPerPage] = useState(\"10\");\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n\r\n  \r\n  const { data: dataItems, isFetching, error: fetchError } = useGetBranchDataQuery({ sort_by: sortColumn, order: sortDirection, page: currentPage, per_page: perPage, query: queryString });\r\n\r\n  const [triggerFilterByFetch, { data: groupData, error: groupDataError }] = useLazyFetchDataOptionsForBranchQuery();\r\n       \r\n  const [deleteBranch] = useDeleteBranchMutation();\r\n\r\n  // Build query parameters from selected filters\r\n  const buildQueryParams = (selectedFilters) => {\r\n    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {\r\n      if (typeof value === \"string\") {\r\n        return acc + `&${key}=${value}`;\r\n      }\r\n      if (Array.isArray(value)) {\r\n        const vals = value.map((i) => i.value).join(\",\");\r\n        return acc + `&${key}=${vals}`;\r\n      }\r\n      return acc;\r\n    }, \"\")\r\n\r\n    setQueryString(q);\r\n  }\r\n\r\n  const handleCopy = (data) => {\r\n    const keysToRemove = [\"id\", \"team\", \"department\", \"updated_at\", \"updated_by\", \"updater\", \"created_at\", \"creator\", \"created_by\", \"updated_by\"];\r\n    const cleanedData = removeKeys(data, keysToRemove);\r\n    setViewData(null)\r\n    setModalVisible(true);\r\n  }\r\n\r\n  const handleEdit = (id) => {\r\n    setViewData(null)\r\n    setDataItemsId(id); \r\n    setModalVisible(true);\r\n  }\r\n\r\n  const handleDelete = (id) => {\r\n    confirmationAlert({onConfirm: () => \r\n      {        \r\n        deleteBranch(id);\r\n        setViewData(null);\r\n      }});  \r\n  }\r\n \r\n\r\n  let columnSerial = 1;\r\n\r\n  const { rolePermissions } = useRoleBasedAccess();\r\n\r\n  // Define columns dynamically based on rolePermissions\r\n  const [columns, setColumns] = useState(() => [\r\n    {\r\n        id: columnSerial++,\r\n      name: \"Action\",\r\n      width: \"180px\",\r\n      className: \"bg-red-300\",\r\n      cell: (item) => (\r\n        <div className=\"flex gap-1 mx-2 !min-w-[200px] pl-3\">\r\n          {/* View Button */}\r\n          <button\r\n            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n            onClick={() => setViewData(item)}\r\n          >\r\n            <span className=\"material-symbols-outlined text-lg\">visibility</span>\r\n          </button>\r\n  \r\n          {/* Conditionally render Edit Button based on rolePermissions */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleEdit(item.id)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\r\n            </button>\r\n          )}\r\n  \r\n          {/* Copy Button */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleCopy(item)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-lg\">content_copy</span>\r\n            </button>\r\n          )}\r\n  \r\n          {/* Delete Button */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleDelete(item.id)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-sm\">delete</span>\r\n            </button>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n      name: \"S.No\",\r\n      selector: (row, index) => (currentPage - 1) * perPage + index + 1,\r\n      width: \"80px\",\r\n      omit: false,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Location\",\r\n        db_field: \"locations\",\r\n        selector: (row) =>\r\n          row.locations && Array.isArray(row.locations)\r\n            ? row.locations.map(loc => loc.locations_name).join(\", \")\r\n            : \"N/A\",\r\n        omit: false,\r\n        sortable: false,\r\n        filterable: true,\r\n      },      \r\n    {\r\n        id: columnSerial++,\r\n      name: \"Branch Name\",\r\n      db_field: \"name\",\r\n      selector: (row) => row.name || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Created by\",\r\n        selector: (row) => `${row.creator?.fname || \"\"} ${row.creator?.lname || \"\"}`,\r\n        db_field: \"created_by\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Created Date\",\r\n      selector: (row) => DateTimeFormatDay(row.created_at),\r\n      db_field: \"created_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Created Time\",\r\n        selector: (row) => DateTimeFormatHour(row.created_at),\r\n        db_field: \"created_at\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: false,\r\n      },\r\n      {\r\n        id: columnSerial++,\r\n        name: \"Updated by\",\r\n        selector: (row) => `${row.updater?.fname || \"\"} ${row.updater?.lname || \"\"}`,\r\n        db_field: \"updated_by\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: true,\r\n      },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Updated Date\",\r\n      selector: (row) => DateTimeFormatDay(row.updated_at),\r\n      db_field: \"updated_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Updated Time\",\r\n        selector: (row) => DateTimeFormatHour(row.updated_at),\r\n        db_field: \"updated_at\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: false,\r\n    },\r\n  ]);\r\n  \r\n  useEffect(() => {\r\n    // Recalculate or update columns if rolePermissions change\r\n    setColumns((prevColumns) => [\r\n      ...prevColumns.map((col) => {\r\n        if (col.name === \"Action\") {\r\n          // Update the \"Action\" column dynamically\r\n          return {\r\n            ...col,\r\n            cell: (item) => (\r\n              <div className=\"flex gap-1 mx-2 !min-w-[200px] pl-3\">\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => setViewData(item)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-lg\">visibility</span>\r\n                </button>\r\n                {rolePermissions?.hasManagerRole && (\r\n                  <button\r\n                    className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                    onClick={() => handleEdit(item.id)}\r\n                  >\r\n                    <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\r\n                  </button>\r\n                )}\r\n\r\n                {rolePermissions?.hasManagerRole && (\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => handleCopy(item)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-lg\">content_copy</span>\r\n                </button>\r\n                )}\r\n\r\n                {rolePermissions?.hasManagerRole && (\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => handleDelete(item.id)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-sm\">delete</span>\r\n                </button>\r\n                )}\r\n              </div>\r\n            ),\r\n          };\r\n        }\r\n        return col;\r\n      }),\r\n    ]);\r\n  }, [rolePermissions]); // Dependency array ensures this is updated whenever rolePermissions changes\r\n  \r\n  \r\n\r\n  // Resets the pagination and clear-all filter state\r\n  const resetPage = () => {\r\n    if (Object.keys(selectedFilterOptions).length) {\r\n      let newObj = {};\r\n      Object.keys(selectedFilterOptions).map((key) => {\r\n        if (typeof selectedFilterOptions[key] === \"string\") {\r\n          newObj[key] = \"\";\r\n        } else {\r\n          newObj[key] = [];\r\n        }\r\n      });\r\n      setSelectedFilterOptions({ ...newObj });\r\n      buildQueryParams({ ...newObj })\r\n    }\r\n    setCurrentPage(1);\r\n  };\r\n\r\n\r\n  // Export the fetched data into an Excel file\r\n  const dispatch = useDispatch();\r\n  const exportToExcel = async () => {\r\n    try {\r\n      // Fetch all data items for Excel export\r\n      const result = await dispatch(\r\n        branchApi.endpoints.getBranchData.initiate({\r\n          sort_by: sortColumn,\r\n          order: sortDirection,\r\n          page: currentPage,\r\n          per_page: dataItems?.total || 10, // Fallback value to avoid undefined issues\r\n          query: queryString,\r\n        })\r\n      ).unwrap(); // Wait for the API response\r\n  \r\n      if (!result?.total || result.total < 1) {\r\n        return false;\r\n      }\r\n  \r\n      var sl = 1;\r\n  \r\n      let prepXlsData = result.data.map((item) => {\r\n        if (columns.length) {\r\n          let obj = {};\r\n          columns.forEach((column) => {\r\n            if (!column.omit && column.selector) {\r\n              obj[column.name] = column.name === \"S.No\" ? sl++ : column.selector(item) || \"\";\r\n            }\r\n          });\r\n          return obj;\r\n        }\r\n      });\r\n  \r\n      // Create a worksheet from the JSON data and append to a new workbook\r\n      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);\r\n      const workbook = XLSX.utils.book_new();\r\n      XLSX.utils.book_append_sheet(workbook, worksheet, \"Sheet1\");\r\n  \r\n      // Convert workbook to a buffer and create a Blob to trigger a file download\r\n      const excelBuffer = XLSX.write(workbook, {\r\n        bookType: \"xlsx\",\r\n        type: \"array\",\r\n      });\r\n      const blob = new Blob([excelBuffer], { type: \"application/octet-stream\" });\r\n      saveAs(blob, `${MODULE_NAME.replace(/ /g,\"_\")}_${prepXlsData.length}.xlsx`);\r\n    } catch (error) {\r\n      console.error(\"Error exporting to Excel:\", error);\r\n    }\r\n  };\r\n  \r\n\r\n  /**\r\n   * Fetch filter options from API for a specific field.\r\n   */\r\n  const fetchDataOptionsForFilterBy = useCallback(\r\n    async (\r\n      itemObject = {},\r\n      type = \"group\",\r\n      searching = \"\",\r\n      fieldType = \"select\"\r\n    ) => {\r\n\r\n      let groupByField = itemObject.db_field || \"title\";\r\n\r\n      try {\r\n        setShowFilterOption(groupByField);\r\n        setFilterOptionLoading(true);\r\n\r\n        var groupData = [];\r\n\r\n        const response = await triggerFilterByFetch({ type: type.trim(), column: groupByField.trim(), text: searching.trim() });\r\n        \r\n        if (response.data) {\r\n          groupData = response.data;\r\n        }\r\n\r\n        if (groupData.length) {\r\n\r\n          if (fieldType === \"searchable\") {\r\n            setFilterOptions((prev) => ({\r\n              ...prev,\r\n              [groupByField]: groupData,\r\n            }));\r\n\r\n            return groupData;\r\n          }\r\n\r\n          const optionsForFilter = groupData\r\n            .map((item) => {\r\n              if(itemObject.selector){\r\n                let label = itemObject.selector(item);\r\n\r\n                if(label){\r\n                  if (item.total && item.total > 1) {\r\n                    label += ` (${item.total})`;\r\n                  }\r\n\r\n                  return { label, value: item[groupByField] };\r\n                }\r\n\r\n              return null;\r\n              }\r\n            }).filter(Boolean);\r\n\r\n          setFilterOptions((prev) => ({\r\n            ...prev,\r\n            [itemObject.id]: sortByLabel(optionsForFilter),\r\n          }));\r\n\r\n          return optionsForFilter;\r\n        }\r\n      } catch (error) {\r\n        setError(error.message);\r\n      } finally {\r\n        setFilterOptionLoading(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  return (\r\n    <section className=\"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\">\r\n      <div className=\"mx-auto pb-6 \">\r\n        {/* Header section with title and action buttons */}\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4\">\r\n          <div className=\"w-4/12 md:w-10/12 text-start\">\r\n            <h2 className=\"text-2xl font-bold \">{MODULE_NAME}</h2>\r\n          </div>\r\n          <div className=\"w-8/12 flex items-end justify-end gap-1\">\r\n            {/* Manage Columns dropdown */}\r\n            <ManageColumns columns={columns} setColumns={setColumns} />\r\n            \r\n            {/* Export to Excel button, only shown if data exists */}\r\n            { !isFetching && dataItems && parseInt(dataItems.total) > 0 && (\r\n              <>\r\n                <button\r\n                  className=\"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\"\r\n                  onClick={exportToExcel}\r\n                >\r\n                  {isFetching && (\r\n                    <>\r\n                      <span className=\"material-symbols-outlined animate-spin text-sm me-2\">\r\n                        progress_activity\r\n                      </span>\r\n                    </>\r\n                  )}\r\n                  {!isFetching && (\r\n                    <span className=\"material-symbols-outlined text-sm me-2\">\r\n                    file_export\r\n                    </span>\r\n                  )}\r\n                  Export to Excel ({dataItems.total})\r\n                </button>\r\n              </>\r\n            )}\r\n            {/* Button to open modal for adding a new formation */}\r\n            {rolePermissions.hasManagerRole && (\r\n              <button\r\n                className=\" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\"\r\n\r\n                onClick={() => setAddModalVisible(true)}\r\n              >\r\n                Add New\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Filter fieldset for global search and field-specific filtering */}\r\n        <SearchFilter\r\n            columns={columns}\r\n            selectedFilterOptions={selectedFilterOptions}\r\n            setSelectedFilterOptions={setSelectedFilterOptions}\r\n            fetchDataOptionsForFilterBy={fetchDataOptionsForFilterBy}\r\n            filterOptions={filterOptions}\r\n            filterOptionLoading={filterOptionLoading}\r\n            showFilterOption={showFilterOption}\r\n            resetPage={resetPage}\r\n            setCurrentPage={setCurrentPage}\r\n            buildQueryParams={buildQueryParams}\r\n        />\r\n\r\n        {/* Display error message if any error occurs */}\r\n        {fetchError && <div className=\"text-red-500\">{error}</div>}\r\n        {/* Show loading spinner when data is being fetched */}\r\n        {isFetching && <Loading />}\r\n\r\n        {/* If no data is available, display an alert message */}\r\n        \r\n        {/* Render the DataTable with the fetched data */}\r\n        <div className=\"border border-gray-200 p-0 pb-1 rounded-lg my-5 \">\r\n          <DataTable\r\n            columns={columns}\r\n            data={dataItems?.data || []}\r\n            className=\"p-0 scrollbar-horizontal-10\"\r\n            fixedHeader\r\n            \r\n            highlightOnHover\r\n            responsive\r\n            pagination\r\n            paginationServer\r\n            paginationPerPage={perPage}\r\n            paginationTotalRows={dataItems?.total || 0}\r\n            onChangePage={(page) => {\r\n              if (page !== currentPage) {\r\n                setCurrentPage(page);\r\n              }\r\n            }}\r\n            onChangeRowsPerPage={(newPerPage) => {\r\n              if(newPerPage !== perPage){\r\n                setPerPage(newPerPage);\r\n                setCurrentPage(1);\r\n              }\r\n            }}\r\n            paginationComponentOptions={{\r\n              selectAllRowsItem: true,\r\n              selectAllRowsItemText: \"ALL\",\r\n            }}\r\n            sortServer\r\n            onSort={(column, sortDirection=\"desc\") => {\r\n              if(Object.keys(column).length){\r\n                setSortColumn(column.db_field || column.name || \"created_at\");\r\n                setSortDirection(sortDirection || \"desc\");\r\n              }\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Add Modal */}\r\n        {addModalVisible && (\r\n            <AddBranch\r\n                isVisible={addModalVisible}\r\n                setVisible={setAddModalVisible}\r\n            />\r\n        )}\r\n\r\n        {/* Conditionally render the Edit modal */}\r\n        {modalVisible && (\r\n          <EditBranch\r\n            isVisible={modalVisible}\r\n            setVisible={setModalVisible}\r\n            dataItemsId={dataItemsId}\r\n          />\r\n        )}\r\n\r\n        {viewData && (\r\n          // <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\r\n          <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\r\n        )}\r\n       \r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\n\r\nexport default BranchDataList;\r\n", "import React from 'react';\r\nimport BranchDataList from '../pages/branch/BranchDataList';\r\n\r\n\r\nconst Branch = () => {\r\n  return (\r\n    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>\r\n      <BranchDataList />\r\n    </div>\r\n\r\n  );\r\n};\r\n\r\nexport default Branch;\r\n"], "names": ["API_URL", "process", "_ref", "isVisible", "setVisible", "dataItemsId", "branchName", "setBranchName", "useState", "selectedLocation", "setSelectedLocation", "branches", "setBranches", "locations", "setLocations", "error", "setError", "successMessage", "setSuccessMessage", "loggedInUser", "setLoggedInUser", "loading", "setLoading", "useEffect", "userId", "localStorage", "getItem", "async", "token", "response", "fetch", "method", "headers", "ok", "Error", "statusText", "branchArray", "json", "Array", "isArray", "branchData", "find", "branch", "id", "_branchData$locations", "_branchData$locations2", "name", "defaultLocationId", "message", "fetchBranchName", "data", "fetchLocations", "_jsx", "className", "onClick", "children", "_jsxs", "e", "stopPropagation", "onSubmit", "event", "preventDefault", "trimmedBranchName", "trim", "updatedBy", "some", "toLowerCase", "timeoutId", "setTimeout", "clearTimeout", "body", "JSON", "stringify", "location_id", "updated_by", "result", "alertMessage", "icon", "title", "text", "newBranchesResponse", "newBranchesData", "htmlFor", "type", "value", "onChange", "target", "required", "length", "disabled", "map", "location", "locations_name", "class", "MODULE_NAME", "BranchDataList", "filterOptions", "setFilterOptions", "selectedFilterOptions", "setSelectedFilterOptions", "showFilterOption", "setShowFilterOption", "queryString", "setQueryString", "modalVisible", "setModalVisible", "filterOptionLoading", "setFilterOptionLoading", "setDataItemsId", "viewData", "setViewData", "addModalVisible", "setAddModalVisible", "useNavigate", "sortColumn", "setSortColumn", "sortDirection", "setSortDirection", "perPage", "setPerPage", "currentPage", "setCurrentPage", "dataItems", "isFetching", "fetchError", "useGetBranchDataQuery", "sort_by", "order", "page", "per_page", "query", "triggerFilterByFetch", "groupData", "groupDataError", "useLazyFetchDataOptionsForBranchQuery", "deleteBranch", "useDeleteBranchMutation", "buildQueryParams", "selectedFilters", "q", "Object", "entries", "reduce", "acc", "key", "i", "join", "handleCopy", "<PERSON><PERSON><PERSON><PERSON>", "handleEdit", "handleDelete", "<PERSON><PERSON><PERSON><PERSON>", "onConfirm", "columnSerial", "rolePermissions", "useRoleBasedAccess", "columns", "setColumns", "width", "cell", "item", "hasManagerRole", "selector", "row", "index", "omit", "db_field", "loc", "sortable", "filterable", "_row$creator", "_row$creator2", "creator", "fname", "lname", "DateTimeFormatDay", "created_at", "DateTimeFormatHour", "_row$updater", "_row$updater2", "updater", "updated_at", "prevColumns", "col", "dispatch", "useDispatch", "fetchDataOptionsForFilterBy", "useCallback", "itemObject", "arguments", "undefined", "searching", "fieldType", "groupByField", "column", "prev", "optionsForFilter", "label", "total", "filter", "Boolean", "sortByLabel", "ManageColumns", "parseInt", "_Fragment", "branchApi", "endpoints", "getBranchData", "initiate", "unwrap", "sl", "prepXlsData", "obj", "for<PERSON>ach", "worksheet", "XLSX", "json_to_sheet", "workbook", "book_new", "book_append_sheet", "excelBuffer", "bookType", "blob", "Blob", "saveAs", "replace", "console", "SearchFilter", "resetPage", "keys", "newObj", "Loading", "DataTable", "fixedHeader", "highlightOnHover", "responsive", "pagination", "paginationServer", "paginationPerPage", "paginationTotalRows", "onChangePage", "onChangeRowsPerPage", "newPerPage", "paginationComponentOptions", "selectAllRowsItem", "selectAllRowsItemText", "sortServer", "onSort", "AddBranch", "EditBranch", "TableView", "Branch"], "sourceRoot": ""}