import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditTaskType from './EditTaskType';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const TaskTypeList = () => {
    const [taskTypes, setTaskTypes] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedTaskTypeId, setSelectedTaskTypeId] = useState(null);
    const [error, setError] = useState(null);

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Task Type Name", key: "name" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchTaskTypes = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}task-types`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();

                // Check response format
                if (data && Array.isArray(data.taskTypes)) {
                    setTaskTypes(data.taskTypes.map(taskType => ({
                        id: taskType.id,
                        name: taskType.name,
                        department: taskType.department,
                        team: taskType.team,
                        created_by: taskType.created_by,
                        updated_by: taskType.updated_by,
                    })));
                } else if (Array.isArray(data)) {
                    setTaskTypes(data.map(taskType => ({
                        id: taskType.id,
                        name: taskType.name,
                        department: taskType.department,
                        team: taskType.team,
                        created_by: taskType.created_by,
                        updated_by: taskType.updated_by,
                    })));
                } else {
                    console.warn('Unhandled API response format:', data);
                    setTaskTypes([]);
                }
            } catch (error) {
                setError(error.message);
            }
        };

        fetchTaskTypes();
    }, []);

    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}task-type/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete task type: ' + response.statusText);
            }

            setTaskTypes(prevTaskTypes => prevTaskTypes.filter(taskType => taskType.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    const handleEdit = (id) => {
        setSelectedTaskTypeId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={taskTypes}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedTaskTypeId}
            />
            {modalVisible && (
                <EditTaskType
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    taskTypeId={selectedTaskTypeId}
                />
            )}
        </div>
    );
};

export default TaskTypeList;
