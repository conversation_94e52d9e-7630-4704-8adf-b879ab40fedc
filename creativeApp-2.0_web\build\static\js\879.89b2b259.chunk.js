"use strict";(self.webpackChunkcreativeapp=self.webpackChunkcreativeapp||[]).push([[879],{47879:(e,t,a)=>{a.r(t),a.d(t,{default:()=>w});var r=a(65043),s=a(58786),o=a(13076),l=a(56025),n=a(83003),i=a(47554),d=a(72450),c=a(11238),u=a(32650),m=a(73216),p=a(70579);const x="https://creative.sebpo.net/api/",g=e=>{let{isVisible:t,setVisible:a,dataItemsId:s}=e;const[o,n]=(0,r.useState)(""),[i,d]=(0,r.useState)([]),[c,u]=(0,r.useState)(""),[m,g]=(0,r.useState)(""),[h,f]=(0,r.useState)(null);(0,r.useEffect)((()=>{const e=localStorage.getItem("user_id");e&&f(e)}),[]),(0,r.useEffect)((()=>{(async()=>{if(!s)return;const e=localStorage.getItem("token");if(e)try{const t=await fetch(`${x}resource_types`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch resource types: "+t.statusText);const a=(await t.json())["Resource Types"];if(!Array.isArray(a))throw new Error("Expected resource types to be an array.");const r=a.find((e=>e.id===s));if(!r)throw new Error("Resource type not found. Please check the ID.");n(r.name)}catch(c){u(c.message)}else u("No authentication token found.")})()}),[s]);return t?(0,p.jsx)("div",{className:"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50",onClick:()=>a(!1),children:(0,p.jsxs)("div",{className:"relative bg-white rounded-lg shadow-lg max-w-md w-full p-5",onClick:e=>e.stopPropagation(),children:[(0,p.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,p.jsx)("h3",{className:"text-lg font-semibold",children:"Update Resource Type"}),(0,p.jsx)("button",{className:"text-gray-500 hover:text-gray-800",onClick:()=>a(!1),children:"\xd7"})]}),c&&(0,p.jsx)("div",{className:"text-red-500",children:c}),m&&(0,p.jsx)("div",{className:"text-green-500",children:m}),(0,p.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t=o.trim(),r=h;if(r){if(Array.isArray(i)){if(i.some((e=>e.name.toLowerCase().trim()===t.toLowerCase()))){u("Resource type already exists. Please add a different type.");const e=setTimeout((()=>u("")),3e3);return()=>clearTimeout(e)}}u("");try{const e=localStorage.getItem("token"),o=await fetch(`${x}resource_types/${s}`,{method:"PUT",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"},body:JSON.stringify({name:t,updated_by:r})});if(!o.ok)throw new Error("Failed to update resource type: "+o.statusText);const i=await o.json();(0,l.GW)({icon:"success",title:"Success!",text:(null===i||void 0===i?void 0:i.message)||"Resource type updated successfully."}),n("");const c=await fetch(`${x}resource_types`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!c.ok)throw new Error("Failed to fetch resource types: "+c.statusText);const u=await c.json();d(u.resource_types||[]),setTimeout((()=>{a(!1),g("")}),1e3)}catch(c){(0,l.GW)("error")}}else u("User is not logged in.")},children:[(0,p.jsxs)("div",{className:"mb-4",children:[(0,p.jsx)("label",{htmlFor:"name",className:"block mb-2",children:"Resource Type"}),(0,p.jsx)("input",{type:"text",id:"name",value:o,onChange:e=>n(e.target.value),className:"border rounded w-full p-2",required:!0})]}),(0,p.jsx)("button",{type:"submit",className:"bg-primary hover:bg-secondary text-white rounded-md px-4 py-2",children:"Update Resource Type"})]})]})}):null};var h=a(54244),f=a(17974),y=a(58598);const b="Responsibility Level",v=()=>{const[e,t]=(0,r.useState)({}),[a,x]=(0,r.useState)({}),[v,w]=(0,r.useState)(""),[j,N]=(0,r.useState)(""),[_,k]=(0,r.useState)(!1),[S,C]=(0,r.useState)(!1),[T,A]=(0,r.useState)(null),[R,$]=(0,r.useState)(null),[E,F]=(0,r.useState)(null),[O,P]=((0,m.Zp)(),(0,r.useState)(!1)),[D,I]=(0,r.useState)("created_at"),[M,U]=(0,r.useState)("desc"),[V,z]=(0,r.useState)("10"),[L,B]=(0,r.useState)(1),{data:W,isFetching:G,error:q}=(0,u.KQf)({sort_by:D,order:M,page:L,per_page:V,query:j}),[Q,{data:H,error:Z}]=(0,u.Z_r)(),[J]=(0,u.gNn)(),K=e=>{let t=Object.entries(e).reduce(((e,t)=>{let[a,r]=t;if("string"===typeof r)return e+`&${a}=${r}`;if(Array.isArray(r)){return e+`&${a}=${r.map((e=>e.value)).join(",")}`}return e}),"");N(t)},Y=e=>{(0,i.$3)(e,["id","team","department","updated_at","updated_by","updater","created_at","creator","created_by","updated_by"]);F(null),k(!0)},X=e=>{F(null),A(e),k(!0)},ee=e=>{(0,l.YU)({onConfirm:()=>{J(e),F(null)}})};let te=1;const{rolePermissions:ae}=(0,f.h)(),[re,se]=(0,r.useState)((()=>[{id:te++,name:"Action",width:"180px",className:"bg-red-300",cell:e=>(0,p.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>F(e),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>X(e.id),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>Y(e),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})},{id:te++,name:"S.No",selector:(e,t)=>(L-1)*V+t+1,width:"80px",omit:!1},{id:te++,name:"Resource Status Name",db_field:"name",selector:e=>e.name||"",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created by",selector:e=>{var t,a;return`${(null===(t=e.creator)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.creator)||void 0===a?void 0:a.lname)||""}`},db_field:"created_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Date",selector:e=>(0,y.hb)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Time",selector:e=>(0,y.DF)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!1},{id:te++,name:"Updated by",selector:e=>{var t,a;return`${(null===(t=e.updater)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.updater)||void 0===a?void 0:a.lname)||""}`},db_field:"updated_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Date",selector:e=>(0,y.hb)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Time",selector:e=>(0,y.DF)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!1}]));(0,r.useEffect)((()=>{se((e=>[...e.map((e=>"Action"===e.name?{...e,cell:e=>(0,p.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>F(e),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>X(e.id),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>Y(e),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})}:e))]))}),[ae]);const oe=(0,n.wA)(),le=(0,r.useCallback)((async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"group",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"select",o=e.db_field||"title";try{w(o),C(!0);var l=[];const n=await Q({type:a.trim(),column:o.trim(),text:r.trim()});if(n.data&&(l=n.data),l.length){if("searchable"===s)return t((e=>({...e,[o]:l}))),l;const a=l.map((t=>{if(e.selector){let a=e.selector(t);return a?(t.total&&t.total>1&&(a+=` (${t.total})`),{label:a,value:t[o]}):null}})).filter(Boolean);return t((t=>({...t,[e.id]:(0,i.eb)(a)}))),a}}catch(R){$(R.message)}finally{C(!1)}}),[]);return(0,p.jsx)("section",{className:"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]",children:(0,p.jsxs)("div",{className:"mx-auto pb-6 ",children:[(0,p.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4",children:[(0,p.jsx)("div",{className:"w-4/12 md:w-10/12 text-start",children:(0,p.jsx)("h2",{className:"text-2xl font-bold ",children:b})}),(0,p.jsxs)("div",{className:"w-8/12 flex items-end justify-end gap-1",children:[(0,p.jsx)(l.DF,{columns:re,setColumns:se}),!G&&W&&parseInt(W.total)>0&&(0,p.jsx)(p.Fragment,{children:(0,p.jsxs)("button",{className:"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:async()=>{try{const t=await oe(u.VvL.endpoints.getResourceTypeData.initiate({sort_by:D,order:M,page:L,per_page:(null===W||void 0===W?void 0:W.total)||10,query:j})).unwrap();if(null===t||void 0===t||!t.total||t.total<1)return!1;var e=1;let a=t.data.map((t=>{if(re.length){let a={};return re.forEach((r=>{!r.omit&&r.selector&&(a[r.name]="S.No"===r.name?e++:r.selector(t)||"")})),a}}));const r=c.Wp.json_to_sheet(a),s=c.Wp.book_new();c.Wp.book_append_sheet(s,r,"Sheet1");const o=c.M9(s,{bookType:"xlsx",type:"array"}),l=new Blob([o],{type:"application/octet-stream"});(0,d.saveAs)(l,`${b.replace(/ /g,"_")}_${a.length}.xlsx`)}catch(R){console.error("Error exporting to Excel:",R)}},children:[G&&(0,p.jsx)(p.Fragment,{children:(0,p.jsx)("span",{className:"material-symbols-outlined animate-spin text-sm me-2",children:"progress_activity"})}),!G&&(0,p.jsx)("span",{className:"material-symbols-outlined text-sm me-2",children:"file_export"}),"Export to Excel (",W.total,")"]})}),ae.hasManagerRole&&(0,p.jsx)("button",{className:" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:()=>P(!0),children:"Add New"})]})]}),(0,p.jsx)(l.$6,{columns:re,selectedFilterOptions:a,setSelectedFilterOptions:x,fetchDataOptionsForFilterBy:le,filterOptions:e,filterOptionLoading:S,showFilterOption:v,resetPage:()=>{if(Object.keys(a).length){let e={};Object.keys(a).map((t=>{"string"===typeof a[t]?e[t]="":e[t]=[]})),x({...e}),K({...e})}B(1)},setCurrentPage:B,buildQueryParams:K}),q&&(0,p.jsx)("div",{className:"text-red-500",children:R}),G&&(0,p.jsx)(o.A,{}),(0,p.jsx)("div",{className:"border border-gray-200 p-0 pb-1 rounded-lg my-5 ",children:(0,p.jsx)(s.Ay,{columns:re,data:(null===W||void 0===W?void 0:W.data)||[],className:"p-0 scrollbar-horizontal-10",fixedHeader:!0,highlightOnHover:!0,responsive:!0,pagination:!0,paginationServer:!0,paginationPerPage:V,paginationTotalRows:(null===W||void 0===W?void 0:W.total)||0,onChangePage:e=>{e!==L&&B(e)},onChangeRowsPerPage:e=>{e!==V&&(z(e),B(1))},paginationComponentOptions:{selectAllRowsItem:!0,selectAllRowsItemText:"ALL"},sortServer:!0,onSort:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"desc";Object.keys(e).length&&(I(e.db_field||e.name||"created_at"),U(t||"desc"))}})}),O&&(0,p.jsx)(h.A,{isVisible:O,setVisible:P}),_&&(0,p.jsx)(g,{isVisible:_,setVisible:k,dataItemsId:T}),E&&(0,p.jsx)(l.Qg,{item:E,setViewData:F,columns:re,handleEdit:X,handleDelete:ee})]})})},w=()=>(0,p.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-xl",children:(0,p.jsx)(v,{})})}}]);
//# sourceMappingURL=879.89b2b259.chunk.js.map